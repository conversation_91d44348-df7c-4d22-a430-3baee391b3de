# JWT Token Enhancement with Authority and Position Information

## Overview

The JWT tokens in the HealthScope backend have been enhanced to include additional user information that allows the frontend to make UI decisions without requiring additional API calls. This improves performance and user experience by providing all necessary user context directly in the token.

## New JWT Token Structure

### Access Token Claims
```json
{
  "user_id": 1,
  "username": "admin",
  "email": "<EMAIL>",
  "first_name": "System",
  "last_name": "Administrator",
  "authority_level": "center",
  "authority_location_id": 1,
  "authority_location_name": "Nepal",
  "position_id": 1,
  "position_name": "Admin",
  "token_type": "access",
  "exp": **********,
  "iat": **********
}
```

### Refresh Token Claims
```json
{
  "user_id": 1,
  "username": "admin",
  "email": "<EMAIL>",
  "first_name": "System",
  "last_name": "Administrator",
  "authority_level": "center",
  "authority_location_id": 1,
  "authority_location_name": "Nepal",
  "position_id": 1,
  "position_name": "Admin",
  "token_type": "refresh",
  "exp": 1641081600,
  "iat": **********
}
```

## New Claims Added

### 1. Authority Information
- **`authority_level`**: User's authority level (center, province, district, municipality, ward)
- **`authority_location_id`**: ID of the location the user has authority over
- **`authority_location_name`**: Human-readable name of the authority location

### 2. Position Information
- **`position_id`**: ID of the user's position
- **`position_name`**: Human-readable name of the user's position

## Authority Location Name Mapping

The `authority_location_name` is automatically resolved based on the user's authority level:

- **center**: Always "Nepal"
- **province**: Province name (e.g., "Koshi", "Madhesh")
- **district**: District name (e.g., "Taplejung", "Kathmandu")
- **municipality**: Municipality name (e.g., "Phaktanlung", "Kathmandu Metropolitan City")
- **ward**: Ward name (e.g., "Ward 1", "Ward 2")

## Implementation Details

### 1. Custom Token Classes (`user/tokens.py`)

Created custom JWT token classes that extend the default SimpleJWT tokens:

- **`CustomAccessToken`**: Extends `AccessToken` with additional claims
- **`CustomRefreshToken`**: Extends `RefreshToken` with additional claims

### 2. Location Name Resolution

The `_get_authority_location_name()` method automatically resolves location names:

```python
@staticmethod
def _get_authority_location_name(user):
    """Get the name of the authority location based on user's authority level"""
    try:
        if user.authority_level == "center":
            return "Nepal"
        elif user.authority_level == "province":
            province = Province.objects.get(id=user.authority_location_id, is_active=True)
            return province.name
        # ... similar for other authority levels
    except ImportError:
        return None
```

### 3. JWT Settings Update (`healthscope/settings/base.py`)

Updated the JWT settings to use custom token classes:

```python
SIMPLE_JWT = {
    # ... other settings
    'AUTH_TOKEN_CLASSES': ('user.tokens.CustomAccessToken',),
    # ... other settings
}
```

### 4. View Updates (`user/views.py`)

Updated all token generation points to use `CustomRefreshToken`:

```python
from .tokens import CustomRefreshToken

# In login view
refresh = CustomRefreshToken.for_user(user)

# In invitation acceptance
refresh = CustomRefreshToken.for_user(user)
```

## Frontend Usage Examples

### 1. Decode JWT Token
```javascript
// Using jwt-decode library
import jwtDecode from 'jwt-decode';

const token = 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...';
const decoded = jwtDecode(token);

console.log('User Authority Level:', decoded.authority_level);
console.log('User Location ID:', decoded.authority_location_id);
console.log('User Location Name:', decoded.authority_location_name);
console.log('User Position:', decoded.position_name);
```

### 2. Conditional Rendering Based on Authority
```javascript
// React example
const { authority_level, authority_location_name } = decoded;

if (authority_level === 'center') {
    return <AdminDashboard />;
} else if (authority_level === 'province') {
    return <ProvinceDashboard location={authority_location_name} />;
} else if (authority_level === 'district') {
    return <DistrictDashboard location={authority_location_name} />;
} else if (authority_level === 'municipality') {
    return <MunicipalityDashboard location={authority_location_name} />;
} else if (authority_level === 'ward') {
    return <WardDashboard location={authority_location_name} />;
}
```

### 3. Display User Context
```javascript
// Show user's authority context
const UserContext = () => {
    const { authority_level, authority_location_name, position_name } = decoded;
    
    return (
        <div className="user-context">
            <p>Welcome, {decoded.first_name} {decoded.last_name}</p>
            <p>Position: {position_name}</p>
            <p>Authority: {authority_level} - {authority_location_name}</p>
        </div>
    );
};
```

### 4. API Calls with Authority Context
```javascript
// The frontend can use authority information for UI decisions
// The backend will automatically filter data based on user authority

// Example: Location API will return only accessible locations
const response = await fetch('/api/v1/location/flat', {
    headers: {
        'Authorization': `Bearer ${token}`
    }
});

// Backend automatically filters based on user's authority_location_id
```

## Benefits

### 1. Performance Improvements
- **Reduced API Calls**: Frontend doesn't need to make additional API calls to get user context
- **Faster UI Rendering**: Authority information is immediately available
- **Better Caching**: Token claims can be cached with the token

### 2. User Experience
- **Immediate Context**: User sees their authority level and location immediately after login
- **Consistent UI**: Authority-based UI decisions can be made instantly
- **Reduced Loading States**: No waiting for additional API calls

### 3. Security
- **Tamper-Proof**: Authority information is signed in the JWT token
- **Consistent**: Backend and frontend always have the same authority information
- **Audit Trail**: Authority changes require new token generation

### 4. Development Efficiency
- **Simplified Frontend Logic**: No need to manage separate authority state
- **Reduced Complexity**: Authority context is always available
- **Better Testing**: Token claims can be easily mocked and tested

## API Response Examples

### Login Response
```json
{
    "refreshToken": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "accessToken": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "user": {
        "id": 1,
        "first_name": "System",
        "last_name": "Administrator",
        "email": "<EMAIL>",
        "username": "admin",
        "status": "active",
        "authority_level": "center",
        "authority_location_id": 1,
        "organization_name": "Ministry Of Health Nepal",
        "position_name": "Admin"
    },
    "message": "Login successful"
}
```

### Invitation Acceptance Response
```json
{
    "refresh": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "access": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
    "user": {
        // User object with authority information
    },
    "message": "Invitation accepted successfully"
}
```

## Authority Level Examples

### Center Level User
```json
{
    "authority_level": "center",
    "authority_location_id": 1,
    "authority_location_name": "Nepal",
    "position_name": "Admin"
}
```
**Access**: Can access all locations and data

### Province Level User
```json
{
    "authority_level": "province",
    "authority_location_id": 1,
    "authority_location_name": "Koshi",
    "position_name": "Province Manager"
}
```
**Access**: Can access Koshi province and all districts/municipalities/wards within it

### District Level User
```json
{
    "authority_level": "district",
    "authority_location_id": 101,
    "authority_location_name": "Taplejung",
    "position_name": "District Officer"
}
```
**Access**: Can access Taplejung district and all municipalities/wards within it

### Municipality Level User
```json
{
    "authority_level": "municipality",
    "authority_location_id": 10101,
    "authority_location_name": "Phaktanlung",
    "position_name": "Municipality Staff"
}
```
**Access**: Can access Phaktanlung municipality and all wards within it

### Ward Level User
```json
{
    "authority_level": "ward",
    "authority_location_id": 1010101,
    "authority_location_name": "Ward 1",
    "position_name": "Ward Representative"
}
```
**Access**: Can only access Ward 1

## Migration Notes

### For Existing Users
- Existing users will automatically get the new token structure on their next login
- No database migration required
- Backward compatibility maintained

### For Frontend Applications
- Update JWT token decoding to handle new claims
- Implement authority-based UI logic
- Remove any existing authority API calls that are no longer needed

## Testing

### Token Validation
- Verify that all new claims are present in tokens
- Test with different authority levels
- Ensure location names are correctly resolved

### Frontend Integration
- Test token decoding with new claims
- Verify authority-based UI rendering
- Test with users having different authority levels

### API Compatibility
- Ensure existing APIs still work with new token structure
- Verify that authority filtering works correctly
- Test token refresh functionality

## Security Considerations

1. **Token Size**: JWT tokens are now larger due to additional claims
2. **Information Exposure**: Authority information is visible in token (but this is intentional)
3. **Token Refresh**: New tokens maintain all authority information
4. **Backend Validation**: Backend still validates authority on each request

## Future Enhancements

1. **Dynamic Claims**: Consider adding more dynamic claims based on user permissions
2. **Caching**: Implement token claim caching for better performance
3. **Real-time Updates**: Consider WebSocket updates for authority changes
4. **Audit Logging**: Enhanced logging for authority-based actions 