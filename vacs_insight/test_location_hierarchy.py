#!/usr/bin/env python3
"""
Test script for Location Hierarchy API
Tests the new hierarchical location endpoint for frontend filtering
"""

import requests
import json
import sys

# Configuration
BASE_URL = "http://localhost:8000/api/v1"
LOGIN_URL = f"{BASE_URL}/user/auth/login"
HIERARCHY_URL = f"{BASE_URL}/location/hierarchy"
FLAT_URL = f"{BASE_URL}/location/flat"

def login():
    """Login and get JWT token"""
    login_data = {
        "username": "<EMAIL>",
        "password": "Admin@123!#"
    }
    
    try:
        response = requests.post(LOGIN_URL, json=login_data)
        response.raise_for_status()
        
        data = response.json()
        if data.get('status') == 'success':
            return data['data']['access']
        else:
            print(f"Login failed: {data.get('message')}")
            return None
            
    except requests.exceptions.RequestException as e:
        print(f"Login request failed: {e}")
        return None

def test_hierarchy_endpoint(token):
    """Test the hierarchical location endpoint"""
    headers = {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }
    
    try:
        print("Testing hierarchical location endpoint...")
        response = requests.get(HIERARCHY_URL, headers=headers)
        response.raise_for_status()
        
        data = response.json()
        
        if data.get('status') == 'success':
            print("✅ Hierarchical endpoint successful!")
            
            countries = data['data']['countries']
            print(f"📊 Found {len(countries)} countries")
            
            for country in countries:
                print(f"  🌍 {country['name']} ({country['code']})")
                print(f"     📍 {len(country['provinces'])} provinces")
                
                for province in country['provinces'][:3]:  # Show first 3 provinces
                    print(f"        🏛️  {province['name']} ({province['code']})")
                    print(f"           🏘️  {len(province['districts'])} districts")
                    
                    for district in province['districts'][:2]:  # Show first 2 districts
                        print(f"              🏙️  {district['name']} ({district['code']})")
                        print(f"                 🏘️  {len(district['municipalities'])} municipalities")
                        
                        for municipality in district['municipalities'][:1]:  # Show first municipality
                            print(f"                    🏘️  {municipality['name']} ({municipality['municipality_type']})")
                            print(f"                       🏠 {len(municipality['wards'])} wards")
            
            return True
        else:
            print(f"❌ Hierarchical endpoint failed: {data.get('message')}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Hierarchical endpoint request failed: {e}")
        return False

def test_flat_endpoint(token):
    """Test the flat location endpoint"""
    headers = {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }
    
    try:
        print("\nTesting flat location endpoint...")
        response = requests.get(FLAT_URL, headers=headers)
        response.raise_for_status()
        
        data = response.json()
        
        if data.get('status') == 'success':
            print("✅ Flat endpoint successful!")
            
            provinces = data['data']['provinces']
            districts = data['data']['districts']
            municipalities = data['data']['municipalities']
            wards = data['data']['wards']
            
            print(f"📊 Data summary:")
            print(f"   🏛️  {len(provinces)} provinces")
            print(f"   🏘️  {len(districts)} districts")
            print(f"   🏘️  {len(municipalities)} municipalities")
            print(f"   🏠 {len(wards)} wards")
            
            return True
        else:
            print(f"❌ Flat endpoint failed: {data.get('message')}")
            return False
            
    except requests.exceptions.RequestException as e:
        print(f"❌ Flat endpoint request failed: {e}")
        return False

def test_individual_endpoints(token):
    """Test individual location endpoints"""
    headers = {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }
    
    endpoints = [
        f"{BASE_URL}/locations/province",
        f"{BASE_URL}/locations/district",
        f"{BASE_URL}/locations/municipality",
        f"{BASE_URL}/locations/ward"
    ]
    
    print("\nTesting individual endpoints...")
    
    for endpoint in endpoints:
        try:
            response = requests.get(endpoint, headers=headers)
            response.raise_for_status()
            
            data = response.json()
            if 'results' in data:
                count = len(data['results'])
                name = endpoint.split('/')[-1]
                print(f"✅ {name}: {count} items")
            else:
                print(f"⚠️  {endpoint.split('/')[-1]}: Unexpected response format")
                
        except requests.exceptions.RequestException as e:
            print(f"❌ {endpoint.split('/')[-1]}: {e}")

def main():
    """Main test function"""
    print("🧪 Testing Location Hierarchy API")
    print("=" * 50)
    
    # Login
    print("🔐 Logging in...")
    token = login()
    if not token:
        print("❌ Failed to get authentication token")
        sys.exit(1)
    
    print("✅ Login successful!")
    
    # Test hierarchical endpoint
    hierarchy_success = test_hierarchy_endpoint(token)
    
    # Test flat endpoint
    flat_success = test_flat_endpoint(token)
    
    # Test individual endpoints
    test_individual_endpoints(token)
    
    # Summary
    print("\n" + "=" * 50)
    print("📋 Test Summary:")
    print(f"   Hierarchical endpoint: {'✅ PASS' if hierarchy_success else '❌ FAIL'}")
    print(f"   Flat endpoint: {'✅ PASS' if flat_success else '❌ FAIL'}")
    
    if hierarchy_success and flat_success:
        print("\n🎉 All tests passed! The location API is ready for frontend use.")
        print("\n💡 Frontend Implementation Tips:")
        print("   1. Use /location/hierarchy for cascading dropdowns")
        print("   2. Cache the response in your frontend state")
        print("   3. Implement client-side filtering for instant response")
        print("   4. See API_DOCUMENTATION.md for detailed examples")
    else:
        print("\n❌ Some tests failed. Please check the server logs.")

if __name__ == "__main__":
    main() 