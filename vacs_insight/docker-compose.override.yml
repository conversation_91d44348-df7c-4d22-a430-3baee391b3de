version: '3.8'

services:
  backend:
    container_name: vacs-insight-app
    build:
      context: .
      dockerfile: Dockerfile
    environment:
      - DEBUG=True
      - DJANGO_SETTINGS_MODULE=healthscope.settings.dev
    volumes:
      - .:/app
      - /app/venv
    command: python manage.py runserver 0.0.0.0:8000
    ports:
      - "80:8000"

  database:
    environment:
      - POSTGRES_DB=${DB_NAME}
      - POSTGRES_USER=${DB_USER}
      - POSTGRES_PASSWORD=${DB_PASSWORD}
    ports:
      - "5432:5432"

  redis:
    ports:
      - "6379:6379" 