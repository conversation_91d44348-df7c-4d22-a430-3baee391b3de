# Django Settings
DJANGO_SECRET_KEY=your-super-secret-key-change-this-in-production
DJANGO_SETTINGS_MODULE=healthscope.settings.production
DEBUG=False

# Database Settings
DB_NAME=vacs_insight
DB_USER=vacs_insight_user
DB_PASSWORD=vacs_insight_password
DB_HOST=database
DB_PORT=5432

# Email Settings
EMAIL_HOST=smtp.gmail.com
EMAIL_PORT=587
EMAIL_USE_TLS=True
EMAIL_HOST_USER=<EMAIL>
EMAIL_HOST_PASSWORD=your-app-password

# Redis Settings (for caching)
REDIS_URL=redis://redis:6379/1

# Frontend URL
FRONTEND_URL=http://localhost:3000

# Health Facility Registry API Settings
HFR_API_BASE_URL=https://hfr-api.example.com
HFR_API_KEY=your-api-key-here

# Allowed Hosts
ALLOWED_HOSTS=localhost,127.0.0.1,vacs_insight.example.com

# CORS Settings
CORS_ALLOWED_ORIGINS=http://localhost:3000,http://localhost:8000,https://vacs_insight.example.com

# Security Settings
SECURE_SSL_REDIRECT=False
SESSION_COOKIE_SECURE=False
CSRF_COOKIE_SECURE=False
SECURE_BROWSER_XSS_FILTER=True
SECURE_CONTENT_TYPE_NOSNIFF=True
X_FRAME_OPTIONS=DENY
SECURE_HSTS_SECONDS=31536000
SECURE_HSTS_INCLUDE_SUBDOMAINS=True
SECURE_HSTS_PRELOAD=True 