#!/usr/bin/env python3
"""
Script to create admin user and test location hierarchy API
"""

import os
import sys
import django

# Setup Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'healthscope.settings')
django.setup()

from django.contrib.auth import get_user_model
from user.models import Organization, UserPosition
from location.models import Country, Province, District, MunicipalityType, Municipality, Ward
import requests
import json

User = get_user_model()

def create_admin_user():
    """Create admin user if it doesn't exist"""
    try:
        # Check if admin user exists
        admin_user = User.objects.filter(email='<EMAIL>').first()
        
        if admin_user:
            print(f"✅ Admin user already exists: {admin_user.email}")
            return admin_user
        
        # Get or create default organization
        org, created = Organization.objects.get_or_create(
            name="Ministry Of Health Nepal",
            defaults={'description': 'Default organization for HealthScope'}
        )
        if created:
            print(f"✅ Created organization: {org.name}")
        
        # Get or create admin position
        position, created = UserPosition.objects.get_or_create(
            name="Admin",
            defaults={'description': 'Administrator position'}
        )
        if created:
            print(f"✅ Created position: {position.name}")
        
        # Create admin user
        admin_user = User.objects.create_user(
            username='admin',
            email='<EMAIL>',
            password='Admin@123!#',
            first_name='Admin',
            last_name='User',
            status='active',
            authority_level='center',
            organization=org,
            position=position,
            is_active=True
        )
        
        print(f"✅ Created admin user: {admin_user.email}")
        return admin_user
        
    except Exception as e:
        print(f"❌ Error creating admin user: {e}")
        return None

def create_sample_location_data():
    """Create sample location data for testing"""
    try:
        # Create country
        country, created = Country.objects.get_or_create(
            name="Nepal",
            defaults={'code': 'NPL'}
        )
        if created:
            print(f"✅ Created country: {country.name}")
        
        # Create province
        province, created = Province.objects.get_or_create(
            name="Koshi",
            country=country,
            defaults={'code': '1'}
        )
        if created:
            print(f"✅ Created province: {province.name}")
        
        # Create district
        district, created = District.objects.get_or_create(
            name="Taplejung",
            province=province,
            defaults={'code': '101'}
        )
        if created:
            print(f"✅ Created district: {district.name}")
        
        # Create municipality type
        municipality_type, created = MunicipalityType.objects.get_or_create(
            name="Rural Municipality",
            defaults={'code': 'RM'}
        )
        if created:
            print(f"✅ Created municipality type: {municipality_type.name}")
        
        # Create municipality
        municipality, created = Municipality.objects.get_or_create(
            name="Phaktanlung",
            district=district,
            defaults={
                'code': '10101',
                'municipality_type': municipality_type
            }
        )
        if created:
            print(f"✅ Created municipality: {municipality.name}")
        
        # Create ward
        ward, created = Ward.objects.get_or_create(
            name="Ward 1",
            municipality=municipality,
            defaults={
                'code': '1010101',
                'ward_number': 1
            }
        )
        if created:
            print(f"✅ Created ward: {ward.name}")
        
        print("✅ Sample location data created successfully!")
        
    except Exception as e:
        print(f"❌ Error creating sample location data: {e}")

def test_location_api():
    """Test the location hierarchy API"""
    try:
        # Login
        login_data = {
            "username": "<EMAIL>",
            "password": "Admin@123!#"
        }
        
        response = requests.post('http://localhost:8000/api/v1/user/auth/login', json=login_data)
        response.raise_for_status()
        
        data = response.json()
        if data.get('status') != 'success':
            print(f"❌ Login failed: {data.get('message')}")
            return False
        
        token = data['data']['access']
        print("✅ Login successful!")
        
        # Test hierarchical endpoint
        headers = {'Authorization': f'Bearer {token}'}
        
        print("\n🧪 Testing hierarchical location endpoint...")
        response = requests.get('http://localhost:8000/api/v1/location/hierarchy', headers=headers)
        response.raise_for_status()
        
        data = response.json()
        if data.get('status') == 'success':
            print("✅ Hierarchical endpoint successful!")
            
            countries = data['data']['countries']
            print(f"📊 Found {len(countries)} countries")
            
            for country in countries:
                print(f"  🌍 {country['name']} ({country['code']})")
                print(f"     📍 {len(country['provinces'])} provinces")
                
                for province in country['provinces']:
                    print(f"        🏛️  {province['name']} ({province['code']})")
                    print(f"           🏘️  {len(province['districts'])} districts")
                    
                    for district in province['districts']:
                        print(f"              🏙️  {district['name']} ({district['code']})")
                        print(f"                 🏘️  {len(district['municipalities'])} municipalities")
                        
                        for municipality in district['municipalities']:
                            print(f"                    🏘️  {municipality['name']} ({municipality['municipality_type']})")
                            print(f"                       🏠 {len(municipality['wards'])} wards")
            
            return True
        else:
            print(f"❌ Hierarchical endpoint failed: {data.get('message')}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing location API: {e}")
        return False

def main():
    """Main function"""
    print("🚀 Setting up admin user and testing location hierarchy API")
    print("=" * 60)
    
    # Create admin user
    print("\n1️⃣ Creating admin user...")
    admin_user = create_admin_user()
    if not admin_user:
        print("❌ Failed to create admin user")
        return
    
    # Create sample location data
    print("\n2️⃣ Creating sample location data...")
    create_sample_location_data()
    
    # Test location API
    print("\n3️⃣ Testing location hierarchy API...")
    success = test_location_api()
    
    # Summary
    print("\n" + "=" * 60)
    if success:
        print("🎉 All tests passed! The location hierarchy API is working correctly.")
        print("\n💡 Frontend Implementation Tips:")
        print("   1. Use /api/v1/location/hierarchy for cascading dropdowns")
        print("   2. Cache the response in your frontend state")
        print("   3. Implement client-side filtering for instant response")
        print("   4. See locations/API_DOCUMENTATION.md for detailed examples")
    else:
        print("❌ Some tests failed. Please check the server logs.")

if __name__ == "__main__":
    main() 