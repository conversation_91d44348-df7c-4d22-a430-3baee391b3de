"""
URL configuration for healthscope project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/4.2/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static
from user.views import CustomTokenRefreshView
from django.http import JsonResponse
from django.views.decorators.csrf import csrf_exempt

# API Version 1
api_v1_patterns = [
    path('user/', include('user.urls')),
    path('location/', include('location.urls')),
    path('token/refresh/', CustomTokenRefreshView.as_view(), name='token_refresh'),
    # Add other app URLs here as they are developed
    path('facility/', include('facilities.urls')),
    path('immunization/', include('immunization.urls')),
    # path('core/', include('core.urls')),
]

@csrf_exempt
def health_check(request):
    """Health check endpoint for Docker"""
    return JsonResponse({
        'status': 'healthy',
        'message': 'HealthScope API is running'
    })

urlpatterns = [
    path('admin/', admin.site.urls),
    path('api/v1/', include(api_v1_patterns)),
    path('health/', health_check, name='health_check'),
]

# Serve media files in development
if settings.DEBUG:
    urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
    urlpatterns += static(settings.STATIC_URL, document_root=settings.STATIC_ROOT)
