# Users App Logging System

This document describes the comprehensive logging system implemented in the users app for tracking user activities, security events, and system performance.

## Overview

The logging system provides detailed tracking of:
- User authentication attempts
- User management operations (create, update, delete, activate, deactivate)
- Security events and potential threats
- API request performance
- Error tracking and debugging
- Email operations
- Password and invitation operations

## Log Files

### Primary Log File
- **Location**: `logs/users.log`
- **Format**: Structured logging with timestamps, log levels, and detailed messages
- **Rotation**: Configured in Django settings

### Log Format
```
YYYY-MM-DD HH:MM:SS,mmm LEVEL MODULE PROCESS THREAD MESSAGE
```

Example:
```
2024-01-15 10:30:45,123 INFO users views 12345 67890 User logged in successfully: <EMAIL>
```

## Log Levels

- **DEBUG**: Detailed debugging information (API requests, function calls)
- **INFO**: General information about user actions and successful operations
- **WARNING**: Potential issues, failed operations, security concerns
- **ERROR**: Errors that need attention, failed operations

## Logged Events

### Authentication Events
- Login attempts (successful and failed)
- Logout operations
- Token blacklisting
- Authentication errors

### User Management Events
- User creation
- User updates
- User deletion (soft delete)
- User activation/deactivation
- User enable/disable operations

### Security Events
- Failed authentication attempts
- Password reset requests
- Invitation operations
- Suspicious activities

### Performance Events
- Slow API requests (>1 second)
- Performance metrics
- API response times

### Email Operations
- Invitation emails sent
- Password reset emails
- Email sending failures

### Error Events
- Validation errors
- Database errors
- Email sending errors
- API exceptions

## Components

### 1. Views Logging (`users/views.py`)
All view methods include comprehensive logging:
- User actions and their outcomes
- Authentication attempts
- Error handling
- Performance tracking

### 2. Serializers Logging (`users/serializers.py`)
Validation and data processing logging:
- Validation errors
- User creation/update operations
- Password operations
- Invitation processing

### 3. Models Logging (`users/models.py`)
Model-level state change logging:
- User status changes
- Soft delete operations
- User activation/deactivation

### 4. Authentication Backend (`users/backends.py`)
Authentication attempt logging:
- Successful and failed logins
- User lookup operations
- Authentication errors

### 5. Middleware (`users/middleware.py`)
Request-level logging:
- API request tracking
- Performance monitoring
- Security event detection
- Audit trail creation

### 6. Logging Utilities (`users/logging_utils.py`)
Helper functions for consistent logging:
- Standardized log message formats
- Context-aware logging
- Performance metric logging
- Error logging utilities

## Configuration

### Django Settings
The logging configuration is defined in `healthscope/settings/base.py`:

```python
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'verbose': {
            'format': '{levelname} {asctime} {module} {process:d} {thread:d} {message}',
            'style': '{',
        },
    },
    'handlers': {
        'console': {
            'class': 'logging.StreamHandler',
            'formatter': 'verbose',
        },
        'users_file': {
            'class': 'logging.FileHandler',
            'filename': os.path.join(BASE_DIR, 'logs', 'users.log'),
            'formatter': 'verbose',
        },
    },
    'loggers': {
        'users': {
            'handlers': ['console', 'users_file'],
            'level': 'INFO',
            'propagate': False,
        },
    },
}
```

### Middleware Configuration
The logging middleware is added to the Django middleware stack:

```python
MIDDLEWARE = [
    # ... other middleware
    'users.middleware.UserAPILoggingMiddleware',
    'users.middleware.UserSecurityLoggingMiddleware',
    'users.middleware.UserAuditLoggingMiddleware',
]
```

## Log Analysis

### Management Command
Use the provided management command to analyze logs:

```bash
# Basic summary
python manage.py analyze_user_logs

# Detailed analysis
python manage.py analyze_user_logs --output detailed

# Error analysis
python manage.py analyze_user_logs --output errors

# Security analysis
python manage.py analyze_user_logs --output security

# Custom time range
python manage.py analyze_user_logs --days 30
```

### Analysis Features
- **Summary**: Overview of log entries, user activity, and endpoint usage
- **Detailed**: Authentication patterns, user action types, recent activities
- **Errors**: Error categorization, recent errors, error patterns
- **Security**: Security events, failed attempts, suspicious activities

## Security Considerations

### Sensitive Data
- Passwords are never logged
- Personal information is logged minimally
- IP addresses are logged for security monitoring
- User agents are truncated to prevent log bloat

### Log Protection
- Log files should have appropriate permissions
- Regular log rotation to prevent disk space issues
- Secure log file storage in production

## Monitoring and Alerting

### Key Metrics to Monitor
1. **Failed Authentication Attempts**: High number may indicate brute force attacks
2. **Slow API Requests**: Performance issues that need investigation
3. **Error Rates**: Sudden spikes may indicate system issues
4. **User Activity Patterns**: Unusual activity may indicate security issues

### Recommended Alerts
- Multiple failed login attempts from same IP
- High error rates (>5% of requests)
- Slow API response times (>2 seconds average)
- Unusual user activity patterns

## Best Practices

### For Developers
1. Use appropriate log levels
2. Include relevant context in log messages
3. Use structured logging for better parsing
4. Avoid logging sensitive information
5. Use logging utilities for consistency

### For Operations
1. Monitor log file sizes and implement rotation
2. Set up log aggregation in production
3. Configure appropriate log retention policies
4. Monitor log levels and adjust as needed
5. Set up automated log analysis

## Troubleshooting

### Common Issues
1. **Missing Log Files**: Check log directory permissions
2. **No Logging Output**: Verify logger configuration
3. **Performance Impact**: Adjust log levels for production
4. **Disk Space**: Implement log rotation

### Debug Commands
```bash
# Check log file existence
ls -la logs/users.log

# View recent logs
tail -f logs/users.log

# Search for specific events
grep "Authentication failed" logs/users.log

# Check log file size
du -h logs/users.log
```

## Future Enhancements

### Planned Features
1. **Structured Logging**: JSON format for better parsing
2. **Log Aggregation**: Centralized logging system
3. **Real-time Monitoring**: Live log monitoring dashboard
4. **Advanced Analytics**: Machine learning for anomaly detection
5. **Integration**: Integration with external monitoring tools

### Performance Optimizations
1. **Async Logging**: Non-blocking log operations
2. **Log Buffering**: Batch log writes for better performance
3. **Selective Logging**: Configurable log levels per endpoint
4. **Log Compression**: Automatic log file compression

## Support

For issues with the logging system:
1. Check the log files for error messages
2. Verify Django logging configuration
3. Test with the analysis management command
4. Review middleware configuration
5. Check file permissions and disk space 