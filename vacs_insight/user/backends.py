from django.contrib.auth.backends import ModelBackend
from django.contrib.auth import get_user_model
from django.db.models import Q
import logging

User = get_user_model()
logger = logging.getLogger('user')


class EmailOrUsernameModelBackend(ModelBackend):
    """
    Custom authentication backend that allows users to login with either email or username
    """
    
    def authenticate(self, request, username=None, password=None, **kwargs):
        if username is None or password is None:
            logger.debug("Authentication failed: Missing username or password")
            return None
        
        try:
            # Try to find user by email or username (case-insensitive)
            user = User.objects.filter(
                Q(email__iexact=username) | Q(username__iexact=username),
                is_deleted=False
            ).first()
            
            if user is None:
                logger.warning(f"Authentication failed: User not found - {username}")
                return None
            
            # Check if user is active
            if not user.is_active:
                logger.warning(f"Authentication failed: Inactive user - {user.email}")
                return None
            
            # Check if user status is active
            if user.status != 'active':
                logger.warning(f"Authentication failed: User not active - {user.email}, status: {user.status}")
                return None
            
            # Verify password
            if user.check_password(password):
                logger.info(f"Authentication successful: {user.email}")
                return user
            else:
                logger.warning(f"Authentication failed: Invalid password for user - {user.email}")
                return None
                
        except Exception as e:
            logger.error(f"Authentication error: {str(e)}")
            return None
    
    def get_user(self, user_id):
        try:
            user = User.objects.get(pk=user_id, is_deleted=False)
            if user.is_active and user.status == 'active':
                return user
            else:
                logger.debug(f"User not active or deleted: {user_id}")
                return None
        except User.DoesNotExist:
            logger.debug(f"User not found: {user_id}")
            return None

    def user_can_authenticate(self, user):
        """
        Reject users with is_active=False. Custom user models that don't have
        that attribute are allowed.
        """
        is_active = getattr(user, 'is_active', None)
        return is_active or is_active is None 