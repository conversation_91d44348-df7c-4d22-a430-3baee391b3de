from django.db import models
from django.contrib.auth.models import AbstractUser
from django.contrib.contenttypes.fields import GenericForeignKey
from django.contrib.contenttypes.models import ContentType
from django.core.exceptions import ValidationError
import uuid
from django.utils import timezone
import logging

# Get logger for user app
logger = logging.getLogger('user')

AUTHORITY_LEVEL_CHOICES = [
    ("center", "Central"),
    ("province", "Province"),
    ("district", "District"),
    ("municipality", "Municipality"),
    ("ward", "Ward"),
]

STATUS_CHOICES = [
    ("invite", "Invited"),
    ("active", "Active"),
    ("inactive", "Inactive"),
]


class Organization(models.Model):
    """Organization model to track different organizations"""
    name = models.CharField(max_length=255, unique=True)
    description = models.TextField(blank=True, null=True)
    is_active = models.BooleanField(default=True)
    
    class Meta:
        db_table = 'organization'
        verbose_name = 'Organization'
        verbose_name_plural = 'Organizations'
    
    def __str__(self):
        return self.name


class UserPosition(models.Model):
    """User position model to track different positions"""
    name = models.CharField(max_length=100, unique=True)
    description = models.TextField(blank=True, null=True)
    is_active = models.BooleanField(default=True)
    
    class Meta:
        db_table = 'user_position'
        verbose_name = 'User Position'
        verbose_name_plural = 'User Positions'
    
    def __str__(self):
        return self.name


class User(AbstractUser, models.Model):
    # Basic fields
    first_name = models.CharField(max_length=150)
    middle_name = models.CharField(max_length=150, blank=True, null=True)
    last_name = models.CharField(max_length=150)
    email = models.EmailField("Email", unique=True)
    username = models.CharField(
        "Username",
        max_length=255,
        unique=True,
        null=False,
        blank=False,
    )
    phone = models.CharField(max_length=20, blank=True, null=True)

    # Status and authority level
    status = models.CharField(max_length=10, choices=STATUS_CHOICES, default="invited")
    authority_level = models.CharField(max_length=20, choices=AUTHORITY_LEVEL_CHOICES, default="ward")

    # Organization and position
    organization = models.ForeignKey(Organization, on_delete=models.SET_NULL, null=True, blank=True, default=1)
    position = models.ForeignKey(UserPosition, on_delete=models.SET_NULL, null=True, blank=True)

    # Authority location assignment for hierarchical access control
    authority_location_id = models.PositiveIntegerField(default=1, help_text="Location ID based on authority level")

    # Admin unit assignment
    assigned_admin_unit_type = models.ForeignKey(ContentType, on_delete=models.SET_NULL, null=True, blank=True)
    assigned_admin_unit_id = models.PositiveIntegerField(null=True, blank=True)
    assigned_admin_unit = GenericForeignKey('assigned_admin_unit_type', 'assigned_admin_unit_id')

    # Invitation fields (for status tracking only)
    invitation_token = models.UUIDField(default=uuid.uuid4, editable=False, unique=True, null=True, blank=True)
    invitation_expires_at = models.DateTimeField(null=True, blank=True)

    # Soft delete
    is_deleted = models.BooleanField(default=False)
    deleted_at = models.DateTimeField(null=True, blank=True)
    deleted_by = models.ForeignKey('self', on_delete=models.SET_NULL, null=True, blank=True, related_name='deleted_users')

    # Audit fields (previously from AuditModel)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    created_by = models.ForeignKey('self', on_delete=models.SET_NULL, null=True, blank=True, related_name='created_users')
    updated_by = models.ForeignKey('self', on_delete=models.SET_NULL, null=True, blank=True, related_name='updated_users')

    # Use username as the username field
    USERNAME_FIELD = 'username'
    EMAIL_FIELD = 'email'
    REQUIRED_FIELDS = ['first_name', 'last_name', 'email', 'authority_level']

    # Additional fields
    phone_number = models.CharField(max_length=15, blank=True)
    is_active = models.BooleanField(default=True)
    last_login = models.DateTimeField(null=True, blank=True)
    disable_reason = models.TextField(null=True, blank=True)

    class Meta:
        indexes = [
            models.Index(fields=['email']),
            models.Index(fields=['username']),
            models.Index(fields=['status']),
            models.Index(fields=['is_deleted']),
            models.Index(fields=['authority_level', 'authority_location_id']),
        ]
        db_table = 'users'
        verbose_name = 'User'
        verbose_name_plural = 'Users'

    def __str__(self):
        return f"{self.get_full_name()} ({self.email})"

    def get_full_name(self):
        if self.middle_name:
            return f"{self.first_name} {self.middle_name} {self.last_name}"
        return f"{self.first_name} {self.last_name}"

    def clean(self):
        """Validate authority_location_id based on authority_level"""
        super().clean()
        
        if self.authority_level and self.authority_location_id:
            try:
                # Import location models for validation
                from location.models import Province, District, Municipality, Ward
                
                if self.authority_level == "center":
                    # Center level users always have authority_location_id = 1 (country_id)
                    if self.authority_location_id != 1:
                        raise ValidationError({
                            'authority_location_id': 'Center level users must have authority_location_id = 1'
                        })
                
                elif self.authority_level == "province":
                    # Validate that the location_id exists in Province table
                    if not Province.objects.filter(id=self.authority_location_id, is_active=True).exists():
                        raise ValidationError({
                            'authority_location_id': f'Province with id {self.authority_location_id} does not exist or is not active'
                        })
                
                elif self.authority_level == "district":
                    # Validate that the location_id exists in District table
                    if not District.objects.filter(id=self.authority_location_id, is_active=True).exists():
                        raise ValidationError({
                            'authority_location_id': f'District with id {self.authority_location_id} does not exist or is not active'
                        })
                
                elif self.authority_level == "municipality":
                    # Validate that the location_id exists in Municipality table
                    if not Municipality.objects.filter(id=self.authority_location_id, is_active=True).exists():
                        raise ValidationError({
                            'authority_location_id': f'Municipality with id {self.authority_location_id} does not exist or is not active'
                        })
                
                elif self.authority_level == "ward":
                    # Validate that the location_id exists in Ward table
                    if not Ward.objects.filter(id=self.authority_location_id, is_active=True).exists():
                        raise ValidationError({
                            'authority_location_id': f'Ward with id {self.authority_location_id} does not exist or is not active'
                        })
                        
            except ImportError:
                # If location models are not available during migration, skip validation
                pass

    def save(self, *args, **kwargs):
        # Set default authority_location_id for center level users
        if self.authority_level == "center":
            self.authority_location_id = 1
        
        # Validate before saving
        self.clean()
        
        # Log important changes
        if self.pk:  # Existing user
            try:
                old_instance = User.objects.get(pk=self.pk)
                if old_instance.status != self.status:
                    logger.info(f"User status changed: {self.email} from {old_instance.status} to {self.status}")
                if old_instance.is_active != self.is_active:
                    logger.info(f"User active status changed: {self.email} from {old_instance.is_active} to {self.is_active}")
                if old_instance.authority_level != self.authority_level or old_instance.authority_location_id != self.authority_location_id:
                    logger.info(f"User authority changed: {self.email} from {old_instance.authority_level}({old_instance.authority_location_id}) to {self.authority_level}({self.authority_location_id})")
            except User.DoesNotExist:
                pass  # New user being created
        
        super().save(*args, **kwargs)

    def soft_delete(self, deleted_by=None):
        """Soft delete the user"""
        self.is_deleted = True
        self.deleted_at = timezone.now()
        self.deleted_by = deleted_by
        self.save()
        logger.info(f"User soft deleted: {self.email} by {deleted_by.email if deleted_by else 'system'}")

    def activate(self):
        """Activate the user"""
        old_status = self.status
        self.status = "active"
        self.invitation_token = None
        self.invitation_expires_at = None
        self.save()
        logger.info(f"User activated: {self.email} (status changed from {old_status} to active)")

    def deactivate(self):
        """Deactivate the user"""
        old_status = self.status
        self.status = "inactive"
        self.save()
        logger.info(f"User deactivated: {self.email} (status changed from {old_status} to inactive)")

    def get_accessible_location_ids(self):
        """
        Get all location IDs that this user can access based on their authority level
        Returns a dict with keys: provinces, districts, municipalities, wards
        """
        try:
            from location.models import Province, District, Municipality, Ward
            
            accessible_locations = {
                'provinces': [],
                'districts': [],
                'municipalities': [],
                'wards': []
            }
            
            if self.authority_level == "center":
                # Center level users can access all locations
                accessible_locations['provinces'] = list(Province.objects.filter(is_active=True).values_list('id', flat=True))
                accessible_locations['districts'] = list(District.objects.filter(is_active=True).values_list('id', flat=True))
                accessible_locations['municipalities'] = list(Municipality.objects.filter(is_active=True).values_list('id', flat=True))
                accessible_locations['wards'] = list(Ward.objects.filter(is_active=True).values_list('id', flat=True))
                
            elif self.authority_level == "province":
                # Province level users can access their province and all districts, municipalities, wards within it
                accessible_locations['provinces'] = [self.authority_location_id]
                accessible_locations['districts'] = list(District.objects.filter(
                    province_id=self.authority_location_id, is_active=True
                ).values_list('id', flat=True))
                accessible_locations['municipalities'] = list(Municipality.objects.filter(
                    district__province_id=self.authority_location_id, is_active=True
                ).values_list('id', flat=True))
                accessible_locations['wards'] = list(Ward.objects.filter(
                    municipality__district__province_id=self.authority_location_id, is_active=True
                ).values_list('id', flat=True))
                
            elif self.authority_level == "district":
                # District level users can access their district, its parent province, and all municipalities and wards within it
                district = District.objects.get(id=self.authority_location_id)
                accessible_locations['provinces'] = [district.province_id]
                accessible_locations['districts'] = [self.authority_location_id]
                accessible_locations['municipalities'] = list(Municipality.objects.filter(
                    district_id=self.authority_location_id, is_active=True
                ).values_list('id', flat=True))
                accessible_locations['wards'] = list(Ward.objects.filter(
                    municipality__district_id=self.authority_location_id, is_active=True
                ).values_list('id', flat=True))
                
            elif self.authority_level == "municipality":
                # Municipality level users can access their municipality and all wards within it
                municipality = Municipality.objects.get(id=self.authority_location_id)
                accessible_locations['provinces'] = [municipality.district.province_id]
                accessible_locations['districts'] = [municipality.district_id]
                accessible_locations['municipalities'] = [self.authority_location_id]
                accessible_locations['wards'] = list(Ward.objects.filter(
                    municipality_id=self.authority_location_id, is_active=True
                ).values_list('id', flat=True))
                
            elif self.authority_level == "ward":
                # Ward level users can only access their specific ward
                ward = Ward.objects.get(id=self.authority_location_id)
                accessible_locations['provinces'] = [ward.municipality.district.province_id]
                accessible_locations['districts'] = [ward.municipality.district_id]
                accessible_locations['municipalities'] = [ward.municipality_id]
                accessible_locations['wards'] = [self.authority_location_id]
                
            return accessible_locations
            
        except ImportError:
            # If location models are not available, return empty dict
            return {'provinces': [], 'districts': [], 'municipalities': [], 'wards': []}
