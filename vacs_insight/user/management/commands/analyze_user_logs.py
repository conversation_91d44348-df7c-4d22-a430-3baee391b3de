"""
Management command to analyze user logs and provide insights
"""
import os
import re
from datetime import datetime, timedelta
from collections import defaultdict, Counter
from django.core.management.base import BaseCommand, CommandError
from django.conf import settings


class Command(BaseCommand):
    help = 'Analyze user logs and provide insights'

    def add_arguments(self, parser):
        parser.add_argument(
            '--days',
            type=int,
            default=7,
            help='Number of days to analyze (default: 7)'
        )
        parser.add_argument(
            '--log-file',
            type=str,
            default=None,
            help='Path to log file (default: users.log)'
        )
        parser.add_argument(
            '--output',
            type=str,
            choices=['summary', 'detailed', 'errors', 'security'],
            default='summary',
            help='Type of analysis output'
        )

    def handle(self, *args, **options):
        days = options['days']
        log_file = options['log_file']
        output_type = options['output']

        if not log_file:
            log_file = os.path.join(settings.BASE_DIR, 'logs', 'users.log')

        if not os.path.exists(log_file):
            raise CommandError(f"Log file not found: {log_file}")

        # Calculate date range
        end_date = datetime.now()
        start_date = end_date - timedelta(days=days)

        self.stdout.write(
            self.style.SUCCESS(f'Analyzing user logs from {start_date.strftime("%Y-%m-%d")} to {end_date.strftime("%Y-%m-%d")}')
        )

        # Parse logs
        log_data = self.parse_logs(log_file, start_date, end_date)

        # Generate analysis
        if output_type == 'summary':
            self.generate_summary(log_data)
        elif output_type == 'detailed':
            self.generate_detailed_analysis(log_data)
        elif output_type == 'errors':
            self.generate_error_analysis(log_data)
        elif output_type == 'security':
            self.generate_security_analysis(log_data)

    def parse_logs(self, log_file, start_date, end_date):
        """Parse log file and extract relevant data"""
        log_data = {
            'total_entries': 0,
            'authentication_attempts': [],
            'user_actions': [],
            'errors': [],
            'security_events': [],
            'performance_metrics': [],
            'api_requests': defaultdict(list),
            'user_activity': defaultdict(int),
        }

        # Log entry pattern
        log_pattern = r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}:\d{2},\d{3}) (\w+) (\w+) (\d+) (\d+) (.+)'

        with open(log_file, 'r') as f:
            for line in f:
                match = re.match(log_pattern, line)
                if match:
                    timestamp_str, level, module, process, thread, message = match.groups()
                    
                    try:
                        timestamp = datetime.strptime(timestamp_str, '%Y-%m-%d %H:%M:%S,%f')
                        if start_date <= timestamp <= end_date:
                            log_data['total_entries'] += 1
                            
                            # Categorize log entries
                            self.categorize_log_entry(log_data, timestamp, level, message)
                    except ValueError:
                        continue

        return log_data

    def categorize_log_entry(self, log_data, timestamp, level, message):
        """Categorize log entry based on message content"""
        # Authentication attempts
        if 'Authentication' in message:
            log_data['authentication_attempts'].append({
                'timestamp': timestamp,
                'level': level,
                'message': message
            })

        # User actions
        elif any(keyword in message for keyword in ['User created', 'User updated', 'User deleted', 'User activated', 'User deactivated']):
            log_data['user_actions'].append({
                'timestamp': timestamp,
                'level': level,
                'message': message
            })

        # Errors
        elif level == 'ERROR':
            log_data['errors'].append({
                'timestamp': timestamp,
                'level': level,
                'message': message
            })

        # Security events
        elif any(keyword in message for keyword in ['Security event', 'Login attempt', 'Logout attempt', 'Password reset']):
            log_data['security_events'].append({
                'timestamp': timestamp,
                'level': level,
                'message': message
            })

        # Performance metrics
        elif 'Performance metric' in message or 'Slow API request' in message:
            log_data['performance_metrics'].append({
                'timestamp': timestamp,
                'level': level,
                'message': message
            })

        # API requests
        elif 'API Request' in message:
            # Extract endpoint from message
            if 'API Request completed' in message:
                # Extract method and endpoint
                method_match = re.search(r'API (\w+) (\S+)', message)
                if method_match:
                    method, endpoint = method_match.groups()
                    log_data['api_requests'][endpoint].append({
                        'timestamp': timestamp,
                        'method': method,
                        'message': message
                    })

        # User activity (extract user email if present)
        user_match = re.search(r'User: ([^\s]+@[^\s]+)', message)
        if user_match:
            user_email = user_match.group(1)
            log_data['user_activity'][user_email] += 1

    def generate_summary(self, log_data):
        """Generate summary analysis"""
        self.stdout.write(self.style.SUCCESS('\n=== USER LOGS SUMMARY ===\n'))

        # Basic statistics
        self.stdout.write(f"Total log entries: {log_data['total_entries']}")
        self.stdout.write(f"Authentication attempts: {len(log_data['authentication_attempts'])}")
        self.stdout.write(f"User actions: {len(log_data['user_actions'])}")
        self.stdout.write(f"Errors: {len(log_data['errors'])}")
        self.stdout.write(f"Security events: {len(log_data['security_events'])}")
        self.stdout.write(f"Performance metrics: {len(log_data['performance_metrics'])}")

        # Most active users
        if log_data['user_activity']:
            self.stdout.write('\n=== MOST ACTIVE USERS ===')
            sorted_users = sorted(log_data['user_activity'].items(), key=lambda x: x[1], reverse=True)
            for user_email, count in sorted_users[:10]:
                self.stdout.write(f"{user_email}: {count} actions")

        # Most accessed endpoints
        if log_data['api_requests']:
            self.stdout.write('\n=== MOST ACCESSED ENDPOINTS ===')
            sorted_endpoints = sorted(log_data['api_requests'].items(), key=lambda x: len(x[1]), reverse=True)
            for endpoint, requests in sorted_endpoints[:10]:
                self.stdout.write(f"{endpoint}: {len(requests)} requests")

    def generate_detailed_analysis(self, log_data):
        """Generate detailed analysis"""
        self.stdout.write(self.style.SUCCESS('\n=== DETAILED ANALYSIS ===\n'))

        # Authentication analysis
        if log_data['authentication_attempts']:
            self.stdout.write('\n=== AUTHENTICATION ANALYSIS ===')
            successful = [a for a in log_data['authentication_attempts'] if 'successful' in a['message']]
            failed = [a for a in log_data['authentication_attempts'] if 'failed' in a['message']]
            
            self.stdout.write(f"Successful logins: {len(successful)}")
            self.stdout.write(f"Failed logins: {len(failed)}")
            
            if failed:
                self.stdout.write('\nRecent failed login attempts:')
                for attempt in failed[-5:]:
                    self.stdout.write(f"  {attempt['timestamp']}: {attempt['message']}")

        # User actions analysis
        if log_data['user_actions']:
            self.stdout.write('\n=== USER ACTIONS ANALYSIS ===')
            action_types = Counter()
            for action in log_data['user_actions']:
                if 'User created' in action['message']:
                    action_types['created'] += 1
                elif 'User updated' in action['message']:
                    action_types['updated'] += 1
                elif 'User deleted' in action['message']:
                    action_types['deleted'] += 1
                elif 'User activated' in action['message']:
                    action_types['activated'] += 1
                elif 'User deactivated' in action['message']:
                    action_types['deactivated'] += 1

            for action_type, count in action_types.items():
                self.stdout.write(f"{action_type.title()}: {count}")

    def generate_error_analysis(self, log_data):
        """Generate error analysis"""
        self.stdout.write(self.style.SUCCESS('\n=== ERROR ANALYSIS ===\n'))

        if not log_data['errors']:
            self.stdout.write("No errors found in the specified time period.")
            return

        # Group errors by type
        error_types = defaultdict(list)
        for error in log_data['errors']:
            # Extract error type from message
            if 'Authentication error' in error['message']:
                error_types['Authentication'].append(error)
            elif 'Email' in error['message']:
                error_types['Email'].append(error)
            elif 'API Exception' in error['message']:
                error_types['API'].append(error)
            else:
                error_types['Other'].append(error)

        for error_type, errors in error_types.items():
            self.stdout.write(f"\n{error_type} Errors ({len(errors)}):")
            for error in errors[-3:]:  # Show last 3 errors of each type
                self.stdout.write(f"  {error['timestamp']}: {error['message'][:100]}...")

    def generate_security_analysis(self, log_data):
        """Generate security analysis"""
        self.stdout.write(self.style.SUCCESS('\n=== SECURITY ANALYSIS ===\n'))

        if not log_data['security_events']:
            self.stdout.write("No security events found in the specified time period.")
            return

        # Group security events by type
        security_types = defaultdict(list)
        for event in log_data['security_events']:
            if 'Login attempt' in event['message']:
                security_types['Login Attempts'].append(event)
            elif 'Logout attempt' in event['message']:
                security_types['Logout Attempts'].append(event)
            elif 'Password reset' in event['message']:
                security_types['Password Reset'].append(event)
            elif 'Security event' in event['message']:
                security_types['Security Events'].append(event)

        for event_type, events in security_types.items():
            self.stdout.write(f"\n{event_type} ({len(events)}):")
            for event in events[-5:]:  # Show last 5 events of each type
                self.stdout.write(f"  {event['timestamp']}: {event['message'][:100]}...")

        # Performance issues
        if log_data['performance_metrics']:
            self.stdout.write('\n=== PERFORMANCE ISSUES ===')
            slow_requests = [m for m in log_data['performance_metrics'] if 'Slow API request' in m['message']]
            if slow_requests:
                self.stdout.write(f"Slow API requests: {len(slow_requests)}")
                for request in slow_requests[-3:]:
                    self.stdout.write(f"  {request['timestamp']}: {request['message'][:100]}...") 