from django.shortcuts import render
from rest_framework import viewsets, status, permissions
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework_simplejwt.tokens import RefreshToken, AccessToken
from rest_framework_simplejwt.token_blacklist.models import BlacklistedToken, OutstandingToken
from rest_framework_simplejwt.views import TokenRefreshView
from rest_framework_simplejwt.exceptions import InvalidToken
from django.contrib.auth import authenticate
from django.utils import timezone
from django.core.mail import send_mail
from django.conf import settings
from django.db.models import Q, Count
from rest_framework.filters import SearchFilter, OrderingFilter
from django_filters.rest_framework import DjangoFilterBackend
from core.views import BaseModelViewSet
from core.logging_utils import get_client_ip
from .models import User, Organization, UserPosition, AUTHORITY_LEVEL_CHOICES
from .serializers import (
    UserSerializer, UserCreateSerializer, UserUpdateSerializer, UserProfileSerializer, UserListSerializer,
    UserInvitation<PERSON>cceptSerializer, LoginSerializer, DisableUserSerializer, EnableUserSerializer,
    PasswordChangeSerializer, PasswordResetRequestSerializer, PasswordResetConfirmSerializer,
    OrganizationSerializer, UserPositionSerializer
)
from rest_framework.views import APIView
from rest_framework.permissions import AllowAny
from .tokens import CustomRefreshToken, CustomAccessToken
import uuid
import logging
from datetime import timedelta
from .backends import EmailOrUsernameModelBackend

# Get logger for user app
logger = logging.getLogger('user')


class OrganizationViewSet(viewsets.ReadOnlyModelViewSet):
    """ViewSet for Organization model - Read Only"""
    queryset = Organization.objects.all()
    serializer_class = OrganizationSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['is_active']
    search_fields = ['name', 'description']
    ordering_fields = ['name']
    ordering = ['name']

    def get_queryset(self):
        queryset = super().get_queryset()
        
        # Filter by active status if provided
        is_active = self.request.query_params.get('is_active', None)
        logger.info(f"is_active::: {is_active}")

        if is_active is not None:
            queryset = queryset.filter(is_active=is_active.lower() == 't')
        
        logger.info(f"Organization list queried by {self.request.user.email}")
        return queryset

    def list(self, request, *args, **kwargs):
        """
        Get paginated organization data with filtering and search
        
        Query Parameters:
        - page_number: Page number (default: 1)
        - page_size: Items per page (default: 10, max: 100)
        - search: Search string for name, description
        - is_active: Filter by active status
        - sort_by: Field to sort by (name, description, created_at, updated_at)
        - sort_order: Sort order (asc, desc) - default: asc
        """
        try:
            logger.info("Fetching organization list", {
                'user': str(request.user),
                'ip': get_client_ip(request),
                'query_params': dict(request.query_params)
            })

            # Get pagination parameters
            try:
                page_number = int(request.query_params.get('page_number', 1))
                page_size = int(request.query_params.get('page_size', 10))
                
                if page_number < 1:
                    return Response({
                        'status': 'error',
                        'message': 'page_number must be greater than 0'
                    }, status=status.HTTP_400_BAD_REQUEST)
                
                if page_size < 1 or page_size > 100:
                    return Response({
                        'status': 'error',
                        'message': 'page_size must be between 1 and 100'
                    }, status=status.HTTP_400_BAD_REQUEST)
                    
            except ValueError:
                return Response({
                    'status': 'error',
                    'message': 'Invalid page_number or page_size format'
                }, status=status.HTTP_400_BAD_REQUEST)

            # Get the filtered queryset
            queryset = self.get_queryset()
            
            # Apply search if provided
            search_query = request.query_params.get('search', '')
            if search_query:
                queryset = queryset.filter(
                    Q(name__icontains=search_query) |
                    Q(description__icontains=search_query)
                )
            
            # Apply sorting
            sort_by = request.query_params.get('sort_by', 'name')
            sort_order = request.query_params.get('sort_order', 'asc').lower()
            
            # Validate sort_by field
            allowed_sort_fields = ['name', 'description', 'created_at', 'updated_at', 'is_active']
            
            if sort_by not in allowed_sort_fields:
                return Response({
                    'status': 'error',
                    'message': f'Invalid sort_by field. Allowed fields: {", ".join(allowed_sort_fields)}'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # Validate sort_order
            if sort_order not in ['asc', 'desc']:
                return Response({
                    'status': 'error',
                    'message': 'sort_order must be either "asc" or "desc"'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # Apply sorting
            if sort_order == 'desc':
                sort_by = f'-{sort_by}'
            
            queryset = queryset.order_by(sort_by)
            
            # Get total count
            total_count = queryset.count()
            
            # Apply pagination
            start_index = (page_number - 1) * page_size
            end_index = start_index + page_size
            paginated_queryset = queryset[start_index:end_index]
            
            # Serialize the data
            serializer = self.get_serializer(paginated_queryset, many=True)
            total_pages = (total_count + page_size - 1) // page_size
            
            logger.info(f"Successfully fetched organization list", {
                'total_count': total_count,
                'page_number': page_number,
                'page_size': page_size,
                'sort_by': sort_by,
                'sort_order': sort_order,
                'user': str(request.user),
                'ip': get_client_ip(request)
            })
            
            return Response({
                'status': 'success',
                'message': 'Organizations retrieved successfully',
                'data': {
                    'type': 'organizations',
                    'items': serializer.data,
                    'total_count': total_count,
                    'page_number': page_number,
                    'page_size': page_size,
                    'total_pages': total_pages,
                    'sort_by': sort_by.replace('-', '') if sort_by.startswith('-') else sort_by,
                    'sort_order': sort_order
                }
            }, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error(f"Error fetching organization list", {
                'user': str(request.user),
                'ip': get_client_ip(request),
                'error_type': type(e).__name__
            }, exception=e)
            return Response({
                'status': 'error',
                'message': 'Failed to fetch organization list',
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class UserPositionViewSet(viewsets.ReadOnlyModelViewSet):
    """ViewSet for UserPosition model - Read Only"""
    queryset = UserPosition.objects.all()
    serializer_class = UserPositionSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['is_active']
    search_fields = ['name', 'description']
    ordering_fields = ['name']
    ordering = ['name']

    def get_queryset(self):
        queryset = super().get_queryset()
        
        # Filter by active status if provided
        is_active = self.request.query_params.get('is_active', None)
        if is_active is not None:
            queryset = queryset.filter(is_active=is_active.lower() == 'true')
        
        logger.info(f"UserPosition list queried by {self.request.user.email}")
        return queryset

    def list(self, request, *args, **kwargs):
        """
        Get paginated user position data with filtering and search
        
        Query Parameters:
        - page_number: Page number (default: 1)
        - page_size: Items per page (default: 10, max: 100)
        - search: Search string for name, description
        - is_active: Filter by active status
        - sort_by: Field to sort by
        - sort_order: Sort order
        """
        try:
            logger.info("Fetching user position list", {
                'user': str(request.user),
                'ip': get_client_ip(request),
                'query_params': dict(request.query_params)
            })

            # Get pagination parameters
            try:
                page_number = int(request.query_params.get('page_number', 1))
                page_size = int(request.query_params.get('page_size', 10))
                
                if page_number < 1:
                    return Response({
                        'status': 'error',
                        'message': 'page_number must be greater than 0'
                    }, status=status.HTTP_400_BAD_REQUEST)
                
                if page_size < 1 or page_size > 100:
                    return Response({
                        'status': 'error',
                        'message': 'page_size must be between 1 and 100'
                    }, status=status.HTTP_400_BAD_REQUEST)
                    
            except ValueError:
                return Response({
                    'status': 'error',
                    'message': 'Invalid page_number or page_size format'
                }, status=status.HTTP_400_BAD_REQUEST)

            # Get the filtered queryset
            queryset = self.get_queryset()
            
            # Apply search if provided
            search_query = request.query_params.get('search', '')
            if search_query:
                queryset = queryset.filter(
                    Q(name__icontains=search_query) |
                    Q(description__icontains=search_query)
                )
            
            # Apply sorting
            sort_by = request.query_params.get('sort_by', 'name')
            sort_order = request.query_params.get('sort_order', 'asc').lower()
            
            # Validate sort_by field
            allowed_sort_fields = ['name', 'description', 'created_at', 'updated_at', 'is_active']
            
            if sort_by not in allowed_sort_fields:
                return Response({
                    'status': 'error',
                    'message': f'Invalid sort_by field. Allowed fields: {", ".join(allowed_sort_fields)}'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # Validate sort_order
            if sort_order not in ['asc', 'desc']:
                return Response({
                    'status': 'error',
                    'message': 'sort_order must be either "asc" or "desc"'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # Apply sorting
            if sort_order == 'desc':
                sort_by = f'-{sort_by}'
            
            queryset = queryset.order_by(sort_by)
            
            # Get total count
            total_count = queryset.count()
            
            # Apply pagination
            start_index = (page_number - 1) * page_size
            end_index = start_index + page_size
            paginated_queryset = queryset[start_index:end_index]
            
            # Serialize the data
            serializer = self.get_serializer(paginated_queryset, many=True)
            total_pages = (total_count + page_size - 1) // page_size
            
            logger.info(f"Successfully fetched user position list", {
                'total_count': total_count,
                'page_number': page_number,
                'page_size': page_size,
                'sort_by': sort_by,
                'sort_order': sort_order,
                'user': str(request.user),
                'ip': get_client_ip(request)
            })
            
            return Response({
                'status': 'success',
                'message': 'User positions retrieved successfully',
                'data': {
                    'type': 'user_positions',
                    'items': serializer.data,
                    'total_count': total_count,
                    'page_number': page_number,
                    'page_size': page_size,
                    'total_pages': total_pages,
                    'sort_by': sort_by.replace('-', '') if sort_by.startswith('-') else sort_by,
                    'sort_order': sort_order
                }
            }, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error(f"Error fetching user position list", {
                'user': str(request.user),
                'ip': get_client_ip(request),
                'error_type': type(e).__name__
            }, exception=e)
            return Response({
                'status': 'error',
                'message': 'Failed to fetch user position list',
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class UserViewSet(BaseModelViewSet):
    queryset = User.objects.filter(is_deleted=False)
    serializer_class = UserSerializer
    permission_classes = [permissions.IsAuthenticated]
    filter_backends = [DjangoFilterBackend, SearchFilter, OrderingFilter]
    filterset_fields = ['status', 'authority_level', 'is_active', 'organization', 'position']
    search_fields = ['username', 'first_name', 'last_name', 'email']
    ordering_fields = ['created_at', 'last_login', 'first_name', 'email']
    ordering = ['-created_at']

    def get_serializer_class(self):
        if self.action == 'create':
            return UserCreateSerializer
        elif self.action in ['update', 'partial_update']:
            return UserUpdateSerializer
        elif self.action == 'list':
            return UserListSerializer
        elif self.action == 'profile':
            return UserProfileSerializer
        return UserSerializer

    def get_queryset(self):
        queryset = super().get_queryset()
        
        # Filter by authority level if provided
        authority_level = self.request.query_params.get('authority_level', None)
        if authority_level:
            queryset = queryset.filter(authority_level=authority_level)
        
        # Filter by status if provided
        status_filter = self.request.query_params.get('status', None)
        if status_filter:
            queryset = queryset.filter(status=status_filter)
        
        # Filter by active status if provided
        is_active = self.request.query_params.get('is_active', None)
        if is_active is not None:
            queryset = queryset.filter(is_active=is_active.lower() == 'true')
        
        # Filter by organization if provided
        organization = self.request.query_params.get('organization', None)
        if organization:
            queryset = queryset.filter(organization_id=organization)
        
        # Filter by position if provided
        position = self.request.query_params.get('position', None)
        if position:
            queryset = queryset.filter(position_id=position)
        
        logger.info(f"User list queried by {self.request.user.email} with filters: authority_level={authority_level}, status={status_filter}, is_active={is_active}, organization={organization}, position={position}")
        return queryset

    def perform_create(self, serializer):
        user = serializer.save(created_by=self.request.user)
        logger.info(f"User created: {user.email} by {self.request.user.email}")
        
        # Send invitation email
        try:
            send_mail(
                'Invitation to HealthScope Platform',
                f'You have been invited to join HealthScope. Click the link to set your password: {settings.FRONTEND_URL}/accept-invitation/{user.invitation_token}',
                settings.DEFAULT_FROM_EMAIL,
                [user.email],
                fail_silently=True,
            )
            logger.info(f"Invitation email sent successfully to {user.email}")
        except Exception as e:
            logger.error(f"Failed to send invitation email to {user.email}: {str(e)}")

    def create(self, request, *args, **kwargs):
        """Override create to return invitation link and token"""
        serializer = self.get_serializer(data=request.data)
        serializer.is_valid(raise_exception=True)
        self.perform_create(serializer)
        headers = self.get_success_headers(serializer.data)
        
        # Get the created user to return invitation details
        user = User.objects.get(email=serializer.validated_data['email'])
        
        return Response({
            'user': serializer.data,
            'invitation_link': f"{settings.FRONTEND_URL}/accept-invitation/{user.invitation_token}",
            'invitation_token': str(user.invitation_token),
            'message': 'User created successfully and invitation email sent'
        }, status=status.HTTP_201_CREATED, headers=headers)

    def perform_update(self, serializer):
        user = serializer.save(updated_by=self.request.user)
        logger.info(f"User updated: {user.email} by {self.request.user.email}")

    def perform_destroy(self, instance):
        logger.info(f"User soft deleted: {instance.email} by {self.request.user.email}")
        instance.soft_delete(deleted_by=self.request.user)

    @action(detail=True, methods=['post'])
    def deactivate(self, request, pk=None):
        """Deactivate a user (set status to inactive)"""
        user = self.get_object()
        user.deactivate()
        logger.info(f"User deactivated: {user.email} by {request.user.email}")
        return Response({
            'message': f'User {user.email} has been deactivated',
            'status': 'success'
        })

    @action(detail=True, methods=['post'])
    def activate(self, request, pk=None):
        """Activate a user (set status to active)"""
        user = self.get_object()
        user.activate()
        logger.info(f"User activated: {user.email} by {request.user.email}")
        return Response({
            'message': f'User {user.email} has been activated',
            'status': 'success'
        })

    @action(detail=True, methods=['post'])
    def delete(self, request, pk=None):
        """Soft delete a user"""
        user = self.get_object()
        user.soft_delete(deleted_by=request.user)
        logger.info(f"User soft deleted: {user.email} by {request.user.email}")
        return Response({
            'message': f'User {user.email} has been deleted',
            'status': 'success'
        })

    @action(detail=True, methods=['post'])
    def disable(self, request, pk=None):
        """Disable a user with reason"""
        user = self.get_object()
        serializer = DisableUserSerializer(data=request.data)
        
        if serializer.is_valid():
            user.is_active = False
            user.disable_reason = serializer.validated_data['disable_reason']
            user.save(update_fields=['is_active', 'disable_reason'])
            
            logger.info(f"User disabled: {user.email} by {request.user.email}, reason: {user.disable_reason}")
            return Response({
                'message': f'User {user.email} has been disabled',
                'disable_reason': user.disable_reason,
                'status': 'success'
            })
        
        logger.warning(f"Failed to disable user {user.email}: {serializer.errors}")
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=True, methods=['post'])
    def enable(self, request, pk=None):
        """Enable a user"""
        user = self.get_object()
        serializer = EnableUserSerializer(data=request.data)
        
        if serializer.is_valid():
            user.is_active = True
            user.disable_reason = None
            user.save(update_fields=['is_active', 'disable_reason'])
            
            logger.info(f"User enabled: {user.email} by {request.user.email}")
            return Response({
                'message': f'User {user.email} has been enabled',
                'status': 'success'
            })
        
        logger.warning(f"Failed to enable user {user.email}: {serializer.errors}")
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=True, methods=['post'])
    def resend_invitation(self, request, pk=None):
        """Resend invitation email to user"""
        user = self.get_object()
        
        if user.status != 'invited':
            logger.warning(f"Resend invitation failed for {user.email}: User is not in invited status")
            return Response({
                'error': 'User is not in invited status'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        # Generate new invitation token
        user.invitation_token = uuid.uuid4()
        user.invitation_expires_at = timezone.now() + timezone.timedelta(days=7)
        user.save(update_fields=['invitation_token', 'invitation_expires_at'])
        
        try:
            send_mail(
                'Invitation to HealthScope Platform',
                f'You have been invited to join HealthScope. Click the link to set your password: {settings.FRONTEND_URL}/accept-invitation/{user.invitation_token}',
                settings.DEFAULT_FROM_EMAIL,
                [user.email],
                fail_silently=True,
            )
            logger.info(f"Invitation email resent to {user.email} by {request.user.email}")
            return Response({
                'message': f'Invitation email sent to {user.email}',
                'status': 'success'
            })
        except Exception as e:
            logger.error(f"Failed to resend invitation email to {user.email}: {str(e)}")
            return Response({
                'error': 'Failed to send invitation email'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=False, methods=['post'], permission_classes=[AllowAny])
    def accept_invitation(self, request):
        """Accept user invitation and set password"""
        serializer = UserInvitationAcceptSerializer(data=request.data)
        if serializer.is_valid():
            user = serializer.save()
            refresh = CustomRefreshToken.for_user(user)
            logger.info(f"Invitation accepted successfully for user: {user.email}")
            return Response({
                'refresh': str(refresh),
                'access': str(refresh.access_token),
                'user': UserSerializer(user).data,
                'message': 'Invitation accepted successfully'
            })
        
        logger.warning(f"Invitation acceptance failed: {serializer.errors}")
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=False, methods=['get'])
    def profile(self, request):
        """Get current user's profile"""
        logger.debug(f"Profile accessed by user: {request.user.email}")
        serializer = UserProfileSerializer(request.user)
        return Response(serializer.data)

    @action(detail=False, methods=['put', 'patch'])
    def update_profile(self, request):
        """Update current user's profile"""
        serializer = UserProfileSerializer(request.user, data=request.data, partial=True)
        if serializer.is_valid():
            serializer.save()
            logger.info(f"Profile updated by user: {request.user.email}")
            return Response({
                'message': 'Profile updated successfully',
                'user': serializer.data
            })
        
        logger.warning(f"Profile update failed for {request.user.email}: {serializer.errors}")
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=False, methods=['post'])
    def change_password(self, request):
        """Change current user's password and invalidate all existing tokens"""
        serializer = PasswordChangeSerializer(data=request.data, context={'user': request.user})
        if serializer.is_valid():
            serializer.save()
            
            # Blacklist all outstanding tokens for this user to force re-login
            try:
                outstanding_tokens = OutstandingToken.objects.filter(user_id=request.user.id)
                logger.info(f"Found {outstanding_tokens.count()} outstanding tokens for user {request.user.email}")
                
                blacklisted_count = 0
                for token in outstanding_tokens:
                    blacklisted, created = BlacklistedToken.objects.get_or_create(token=token)
                    if created:
                        blacklisted_count += 1
                        logger.info(f"Blacklisted token {token.jti} for user {request.user.email}")
                
                logger.info(f"Successfully blacklisted {blacklisted_count} tokens for user {request.user.email}")
                
                # Verify blacklisting
                try:
                    blacklisted_tokens = BlacklistedToken.objects.filter(token__user_id=request.user.id)
                    logger.info(f"Total blacklisted tokens in DB: {blacklisted_tokens.count()}")
                    
                    return Response({
                        'message': 'Password changed successfully. Please log in again with your new password.'
                    })
                except Exception as e:
                    logger.error(f"Failed to blacklist tokens after password change for user {request.user.email}: {str(e)}")
                    # Still return success since password was changed, but log the error
                    return Response({
                        'message': 'Password changed successfully. Please log in again with your new password.'
                    })
            except Exception as e:
                logger.error(f"Failed to blacklist tokens after password change for user {request.user.email}: {str(e)}")
                # Still return success since password was changed, but log the error
                return Response({
                    'message': 'Password changed successfully. Please log in again with your new password.'
                })
        
        logger.warning(f"Password change failed for {request.user.email}: {serializer.errors}")
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=False, methods=['post'], permission_classes=[AllowAny])
    def request_password_reset(self, request):
        """Request password reset"""
        serializer = PasswordResetRequestSerializer(data=request.data)
        if serializer.is_valid():
            email = serializer.validated_data['email']
            try:
                user = User.objects.get(email=email, is_deleted=False, is_active=True)
                
                # Generate reset token
                user.invitation_token = uuid.uuid4()
                user.invitation_expires_at = timezone.now() + timezone.timedelta(hours=24)
                user.save(update_fields=['invitation_token', 'invitation_expires_at'])
                
                try:
                    send_mail(
                        'Password Reset Request - HealthScope Platform',
                        f'You have requested a password reset. Click the link to reset your password: {settings.FRONTEND_URL}/reset-password/{user.invitation_token}',
                        settings.DEFAULT_FROM_EMAIL,
                        [user.email],
                        fail_silently=True,
                    )
                    logger.info(f"Password reset email sent successfully to {user.email}")
                    return Response({
                        'message': 'Password reset email sent successfully'
                    })
                except Exception as e:
                    logger.error(f"Failed to send password reset email to {user.email}: {str(e)}")
                    return Response({
                        'error': 'Failed to send password reset email'
                    }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
                    
            except User.DoesNotExist:
                # Don't reveal if email exists or not for security
                logger.info(f"Password reset requested for non-existent email: {email}")
                return Response({
                    'message': 'If the email exists, a password reset link has been sent'
                })
        
        logger.warning(f"Password reset request failed: {serializer.errors}")
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=False, methods=['post'], permission_classes=[AllowAny])
    def confirm_password_reset(self, request):
        """Confirm password reset with token and invalidate all existing tokens"""
        serializer = PasswordResetConfirmSerializer(data=request.data)
        if serializer.is_valid():
            token = serializer.validated_data['token']
            
            try:
                user = User.objects.get(
                    invitation_token=token,
                    invitation_expires_at__gt=timezone.now(),
                    is_deleted=False,
                    is_active=True
                )
                
                user.set_password(serializer.validated_data['new_password'])
                user.invitation_token = None
                user.invitation_expires_at = None
                user.save(update_fields=['password', 'invitation_token', 'invitation_expires_at'])
                
                # Blacklist all outstanding tokens for this user to force re-login
                try:
                    outstanding_tokens = OutstandingToken.objects.filter(user_id=user.id)
                    logger.info(f"Found {outstanding_tokens.count()} outstanding tokens for user {user.email}")
                    
                    blacklisted_count = 0
                    for token in outstanding_tokens:
                        blacklisted, created = BlacklistedToken.objects.get_or_create(token=token)
                        if created:
                            blacklisted_count += 1
                            logger.info(f"Blacklisted token {token.jti} for user {user.email}")
                    
                    logger.info(f"Successfully blacklisted {blacklisted_count} tokens for user {user.email}")
                    
                    # Verify blacklisting
                    try:
                        blacklisted_tokens = BlacklistedToken.objects.filter(token__user_id=user.id)
                        logger.info(f"Total blacklisted tokens in DB: {blacklisted_tokens.count()}")
                        
                        return Response({
                            'message': 'Password reset successfully. Please log in with your new password.'
                        })
                    except Exception as e:
                        logger.error(f"Failed to blacklist tokens after password reset for user {user.email}: {str(e)}")
                        # Still return success since password was reset, but log the error
                        return Response({
                            'message': 'Password reset successfully. Please log in with your new password.'
                        })
                except Exception as e:
                    logger.error(f"Failed to blacklist tokens after password reset for user {user.email}: {str(e)}")
                    # Still return success since password was reset, but log the error
                    return Response({
                        'message': 'Password reset successfully. Please log in with your new password.'
                    })
                
            except User.DoesNotExist:
                logger.warning(f"Password reset failed: Invalid or expired token")
                return Response({
                    'error': 'Invalid or expired reset token'
                }, status=status.HTTP_400_BAD_REQUEST)
        
        logger.warning(f"Password reset confirmation failed: {serializer.errors}")
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

    @action(detail=False, methods=['get'])
    def stats(self, request):
        """Get user statistics"""
        logger.info(f"User statistics requested by: {request.user.email}")
        
        total_users = User.objects.filter(is_deleted=False).count()
        active_users = User.objects.filter(is_deleted=False, status='active').count()
        invited_users = User.objects.filter(is_deleted=False, status='invited').count()
        inactive_users = User.objects.filter(is_deleted=False, status='inactive').count()
        
        authority_level_stats = {}
        for authority_level, _ in AUTHORITY_LEVEL_CHOICES:
            authority_level_stats[authority_level] = User.objects.filter(is_deleted=False, authority_level=authority_level).count()
        
        # Organization stats
        organization_stats = {}
        for org in Organization.objects.filter(is_active=True):
            organization_stats[org.name] = User.objects.filter(is_deleted=False, organization=org).count()
        
        # Position stats
        position_stats = {}
        for pos in UserPosition.objects.filter(is_active=True):
            position_stats[pos.name] = User.objects.filter(is_deleted=False, position=pos).count()
        
        # Recent activity (last 30 days)
        thirty_days_ago = timezone.now() - timedelta(days=30)
        recent_users = User.objects.filter(
            is_deleted=False,
            created_at__gte=thirty_days_ago
        ).count()
        
        return Response({
            'total_users': total_users,
            'active_users': active_users,
            'invited_users': invited_users,
            'inactive_users': inactive_users,
            'authority_level_distribution': authority_level_stats,
            'organization_distribution': organization_stats,
            'position_distribution': position_stats,
            'recent_users': recent_users,
        })

    @action(detail=False, methods=['get'])
    def authority_levels(self, request):
        """Get authority levels in key-value pairs"""
        logger.info(f"Authority levels requested by: {request.user.email}")
        
        authority_levels = dict(AUTHORITY_LEVEL_CHOICES)
        
        return Response({
            'authority_levels': authority_levels
        })

    def list(self, request, *args, **kwargs):
        """
        Get paginated user data with filtering and search
        
        Query Parameters:
        - page_number: Page number (default: 1)
        - page_size: Items per page (default: 10, max: 100)
        - search: Search string for username, first_name, last_name, email
        - authority_level: Filter by authority level
        - status: Filter by user status
        - is_active: Filter by active status
        - organization: Filter by organization ID
        - position: Filter by position ID
        - sort_by: Field to sort by (username, first_name, last_name, email, created_at, last_login, authority_level, status)
        - sort_order: Sort order (asc, desc) - default: desc
        """
        try:
            logger.info("Fetching user list", {
                'user': str(request.user),
                'ip': get_client_ip(request),
                'query_params': dict(request.query_params)
            })

            # Get pagination parameters
            try:
                page_number = int(request.query_params.get('page_number', 1))
                page_size = int(request.query_params.get('page_size', 10))
                
                if page_number < 1:
                    return Response({
                        'status': 'error',
                        'message': 'page_number must be greater than 0'
                    }, status=status.HTTP_400_BAD_REQUEST)
                
                if page_size < 1 or page_size > 100:
                    return Response({
                        'status': 'error',
                        'message': 'page_size must be between 1 and 100'
                    }, status=status.HTTP_400_BAD_REQUEST)
                    
            except ValueError:
                return Response({
                    'status': 'error',
                    'message': 'Invalid page_number or page_size format'
                }, status=status.HTTP_400_BAD_REQUEST)

            # Get the filtered queryset
            queryset = self.get_queryset()
            
            # Apply search if provided
            search_query = request.query_params.get('search', '')
            if search_query:
                queryset = queryset.filter(
                    Q(username__icontains=search_query) |
                    Q(first_name__icontains=search_query) |
                    Q(last_name__icontains=search_query) |
                    Q(email__icontains=search_query)
                )
            
            # Apply sorting
            sort_by = request.query_params.get('sort_by', 'created_at')
            sort_order = request.query_params.get('sort_order', 'desc').lower()
            
            # Validate sort_by field
            allowed_sort_fields = [
                'username', 'first_name', 'last_name', 'email', 
                'created_at', 'last_login', 'authority_level', 'status',
                'is_active', 'organization__name', 'position__name'
            ]
            
            if sort_by not in allowed_sort_fields:
                return Response({
                    'status': 'error',
                    'message': f'Invalid sort_by field. Allowed fields: {", ".join(allowed_sort_fields)}'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # Validate sort_order
            if sort_order not in ['asc', 'desc']:
                return Response({
                    'status': 'error',
                    'message': 'sort_order must be either "asc" or "desc"'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # Apply sorting
            if sort_order == 'desc':
                sort_by = f'-{sort_by}'
            
            queryset = queryset.order_by(sort_by)
            
            # Get total count
            total_count = queryset.count()
            
            # Apply pagination
            start_index = (page_number - 1) * page_size
            end_index = start_index + page_size
            paginated_queryset = queryset[start_index:end_index]
            
            # Serialize the data
            serializer = self.get_serializer(paginated_queryset, many=True)
            total_pages = (total_count + page_size - 1) // page_size
            
            logger.info(f"Successfully fetched user list", {
                'total_count': total_count,
                'page_number': page_number,
                'page_size': page_size,
                'sort_by': sort_by,
                'sort_order': sort_order,
                'user': str(request.user),
                'ip': get_client_ip(request)
            })
            
            return Response({
                'status': 'success',
                'message': 'Users retrieved successfully',
                'data': {
                    'type': 'users',
                    'items': serializer.data,
                    'total_count': total_count,
                    'page_number': page_number,
                    'page_size': page_size,
                    'total_pages': total_pages,
                    'sort_by': sort_by.replace('-', '') if sort_by.startswith('-') else sort_by,
                    'sort_order': sort_order
                }
            }, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error(f"Error fetching user list", {
                'user': str(request.user),
                'ip': get_client_ip(request),
                'error_type': type(e).__name__
            }, exception=e)
            return Response({
                'status': 'error',
                'message': 'Failed to fetch user list',
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=False, methods=['post'])
    def debug_blacklist(self, request):
        """Debug endpoint to check blacklist status of a token"""
        token = request.data.get('token')
        if not token:
            return Response({
                'error': 'No token provided'
            }, status=status.HTTP_400_BAD_REQUEST)
        
        try:
            # Decode the token to get JTI
            access_token = AccessToken(token)
            jti = access_token.get('jti')
            
            if not jti:
                return Response({
                    'error': 'Token has no JTI',
                    'token_info': {
                        'user_id': access_token.get('user_id'),
                        'email': access_token.get('email'),
                        'exp': access_token.get('exp'),
                    }
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # Check if token is blacklisted
            is_blacklisted = BlacklistedToken.objects.filter(token__jti=jti).exists()
            
            # Get outstanding token info
            outstanding_token = OutstandingToken.objects.filter(jti=jti).first()
            
            return Response({
                'jti': jti,
                'is_blacklisted': is_blacklisted,
                'token_info': {
                    'user_id': access_token.get('user_id'),
                    'email': access_token.get('email'),
                    'exp': access_token.get('exp'),
                    'outstanding_token_exists': outstanding_token is not None,
                },
                'blacklist_info': {
                    'total_blacklisted_tokens': BlacklistedToken.objects.count(),
                    'total_outstanding_tokens': OutstandingToken.objects.count(),
                }
            })
            
        except InvalidToken as e:
            return Response({
                'error': 'Invalid token',
                'details': str(e)
            }, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            return Response({
                'error': 'Error checking token',
                'details': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)


class LoginView(APIView):
    permission_classes = [AllowAny]
    serializer_class = LoginSerializer

    def post(self, request):
        serializer = self.serializer_class(data=request.data)
        if serializer.is_valid():
            user = serializer.validated_data['user']
            
            # Check if user is active
            if not user.is_active:
                logger.warning(f"Login attempt for disabled user: {user.email}")
                return Response({
                    'error': 'User account is disabled'
                }, status=status.HTTP_403_FORBIDDEN)
            
            # Update last login
            user.last_login = timezone.now()
            user.save(update_fields=['last_login'])
            
            # Generate tokens
            refresh = CustomRefreshToken.for_user(user)
            
            logger.info(f"User logged in successfully: {user.email}")
            return Response({
                'refreshToken': str(refresh),
                'accessToken': str(refresh.access_token),
                'user': UserSerializer(user).data,
                'message': 'Login successful'
            })
        
        logger.warning(f"Login failed: {serializer.errors}")
        return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


class LogoutView(APIView):
    permission_classes = [permissions.IsAuthenticated]

    def post(self, request):
        try:
            # Get the refresh token from request
            refresh_token = request.data.get('refreshToken')
            
            if refresh_token:
                # Blacklist the refresh token
                token = CustomRefreshToken(refresh_token)
                token.blacklist()
                logger.info(f"Refresh token blacklisted for user: {request.user.email}")
            
            # Also blacklist the current access token
            auth_header = request.headers.get('Authorization')
            if auth_header and auth_header.startswith('Bearer '):
                access_token = auth_header.split(' ')[1]
                try:
                    # Create a token object and blacklist it
                    token_obj = CustomAccessToken(access_token)
                    token_obj.blacklist()
                    logger.info(f"Access token blacklisted for user: {request.user.email}")
                except Exception:
                    # If access token is already expired or invalid, that's fine
                    logger.debug(f"Access token already expired for user: {request.user.email}")
            
            logger.info(f"User logged out successfully: {request.user.email}")
            return Response({
                'message': 'Logout successful. All tokens have been invalidated.'
            })
        except Exception as e:
            logger.error(f"Logout failed for user {request.user.email}: {str(e)}")
            return Response({
                'error': 'Invalid token or logout failed'
            }, status=status.HTTP_400_BAD_REQUEST)


class CustomTokenRefreshView(TokenRefreshView):
    """Custom token refresh view that checks blacklist"""
    
    def post(self, request, *args, **kwargs):
        try:
            # Get the refresh token from request
            refresh_token = request.data.get('refresh')
            if not refresh_token:
                return Response({
                    'detail': 'No refresh token provided'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # Check if the refresh token is blacklisted
            try:
                # Decode the token to get JTI
                from rest_framework_simplejwt.tokens import RefreshToken
                token = RefreshToken(refresh_token)
                jti = token.get('jti')
                
                if jti and BlacklistedToken.objects.filter(token__jti=jti).exists():
                    logger.warning(f"Blacklisted refresh token attempted: {jti}")
                    return Response({
                        'detail': 'Token is blacklisted'
                    }, status=status.HTTP_401_UNAUTHORIZED)
                
            except Exception as e:
                logger.error(f"Error checking refresh token blacklist: {str(e)}")
                # Continue with normal refresh if blacklist check fails
            
            # Proceed with normal token refresh
            response = super().post(request, *args, **kwargs)
            logger.info("Token refresh successful")
            return response
            
        except InvalidToken as e:
            logger.warning(f"Invalid refresh token: {str(e)}")
            return Response({
                'detail': 'Token is invalid or expired'
            }, status=status.HTTP_401_UNAUTHORIZED)
        except Exception as e:
            logger.error(f"Token refresh error: {str(e)}")
            return Response({
                'detail': 'Token refresh failed'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
