# User API Documentation

## Overview
The User API provides endpoints for managing users, organizations, and user positions in the VacsInsight platform.

## Authentication
All endpoints require JWT authentication. Include the access token in the Authorization header:
```
Authorization: Bearer <access_token>
```

## Pagination
All list endpoints support pagination with the following parameters:

- `page_number`: The page number to retrieve (default: 1)
- `page_size`: Number of items per page (default: 10, max: 100)

### Pagination Response Format
```json
{
  "status": "success",
  "message": "Users retrieved successfully",
  "data": {
    "type": "users",
    "items": [...],
    "total_count": 150,
    "page_number": 1,
    "page_size": 10,
    "total_pages": 15
  }
}
```

## Search and Filtering
The User API supports search and filtering capabilities:

### Search Parameters
- `search`: Search string that matches against username, first_name, last_name, and email fields

### Filter Parameters
- `authority_level`: Filter by authority level (center, province, district, municipality, ward)
- `status`: Filter by user status (active, inactive, disabled)
- `is_active`: Filter by active status (true/false)
- `organization`: Filter by organization ID
- `position`: Filter by position ID

### Sorting Parameters
- `sort_by`: Field to sort by (varies by endpoint)
- `sort_order`: Sort order (asc, desc) - default varies by endpoint

**Available sort_by fields for Users:**
- `username`, `first_name`, `last_name`, `email`
- `created_at`, `last_login`, `authority_level`, `status`
- `is_active`, `organization__name`, `position__name`

**Available sort_by fields for Organizations:**
- `name`, `description`, `created_at`, `updated_at`, `is_active`

**Available sort_by fields for User Positions:**
- `name`, `description`, `created_at`, `updated_at`, `is_active`

## JWT Token Structure

The JWT tokens now include additional user information in the payload:

### Access Token Claims
```json
{
  "user_id": 1,
  "username": "admin",
  "email": "<EMAIL>",
  "first_name": "System",
  "last_name": "Administrator",
  "authority_level": "center",
  "authority_location_id": 1,
  "authority_location_name": "Nepal",
  "position_id": 1,
  "position_name": "Admin",
  "token_type": "access",
  "exp": 1640995200,
  "iat": 1640994900
}
```

### Refresh Token Claims
```json
{
  "user_id": 1,
  "username": "admin",
  "email": "<EMAIL>",
  "first_name": "System",
  "last_name": "Administrator",
  "authority_level": "center",
  "authority_location_id": 1,
  "authority_location_name": "Nepal",
  "position_id": 1,
  "position_name": "Admin",
  "token_type": "refresh",
  "exp": 1641081600,
  "iat": 1640994900
}
```

### Authority Location Name Mapping
- **center**: Always "Nepal"
- **province**: Province name (e.g., "Koshi", "Madhesh")
- **district**: District name (e.g., "Taplejung", "Kathmandu")
- **municipality**: Municipality name (e.g., "Phaktanlung", "Kathmandu Metropolitan City")
- **ward**: Ward name (e.g., "Ward 1", "Ward 2")

## Endpoints

### Users

#### GET /api/v1/user/
List all users with pagination, search, and filtering.

**Query Parameters:**
- `page_number` (optional): Page number (default: 1)
- `page_size` (optional): Items per page (default: 10, max: 100)
- `search` (optional): Search string
- `authority_level` (optional): Filter by authority level
- `status` (optional): Filter by status
- `is_active` (optional): Filter by active status
- `organization` (optional): Filter by organization ID
- `position` (optional): Filter by position ID
- `sort_by` (optional): Field to sort by
- `sort_order` (optional): Sort order

**Example Requests:**
```bash
# Basic pagination
GET /api/v1/user/?page_number=1&page_size=20

# Search for users
GET /api/v1/user/?search=john&page_number=1&page_size=10

# Filter by authority level
GET /api/v1/user/?authority_level=district&page_number=1&page_size=15

# Combined search and filtering
GET /api/v1/user/?search=admin&authority_level=center&status=active&page_number=1&page_size=10

# Sorting examples
GET /api/v1/user/?sort_by=created_at&sort_order=desc&page_number=1&page_size=10
GET /api/v1/user/?sort_by=first_name&sort_order=asc&page_number=1&page_size=10
GET /api/v1/user/?sort_by=email&sort_order=asc&page_number=1&page_size=10

# Combined filtering and sorting
GET /api/v1/user/?authority_level=district&sort_by=last_login&sort_order=desc&page_number=1&page_size=15
```

**Response:**
```json
{
  "status": "success",
  "message": "Users retrieved successfully",
  "data": {
    "type": "users",
    "items": [
      {
        "id": 1,
        "username": "admin",
        "email": "<EMAIL>",
        "first_name": "Admin",
        "last_name": "User",
        "authority_level": "center",
        "status": "active",
        "is_active": true,
        "created_at": "2024-01-01T09:00:00Z",
        "updated_at": "2024-01-01T09:00:00Z"
      }
    ],
    "total_count": 10,
    "page_number": 1,
    "page_size": 10,
    "total_pages": 1,
    "sort_by": "created_at",
    "sort_order": "desc"
  }
}
```

#### POST /api/v1/user/
Create a new user.

#### GET /api/v1/user/{id}/
Retrieve a specific user.

#### PUT /api/v1/user/{id}/
Update a user.

#### PATCH /api/v1/user/{id}/
Partially update a user.

#### DELETE /api/v1/user/{id}/
Delete a user.

### Password Management

#### POST /api/v1/user/change_password/
Change the current user's password.

**Authentication Required:** Yes (JWT token)

**Request Body:**
```json
{
  "old_password": "current_password",
  "new_password": "new_secure_password",
  "confirm_password": "new_secure_password"
}
```

**Required Fields:**
- `old_password`: Current password of the authenticated user
- `new_password`: New password to set
- `confirm_password`: Confirmation of the new password (must match `new_password`)

**Password Requirements:**
- At least 8 characters long
- Cannot be too common
- Cannot be entirely numeric
- Cannot be similar to username/email

**Success Response:**
```json
{
  "message": "Password changed successfully. Please log in again with your new password."
}
```

**Error Responses:**

**Invalid current password:**
```json
{
  "old_password": ["Current password is incorrect"]
}
```

**Passwords don't match:**
```json
{
  "non_field_errors": ["New passwords do not match"]
}
```

**Weak password:**
```json
{
  "new_password": ["This password is too short. It must contain at least 8 characters."]
}
```

**Security Note:** After a successful password change, all existing JWT tokens for the user are automatically invalidated, forcing the user to log in again with the new password.

#### POST /api/v1/user/request_password_reset/
Request a password reset email.

**Authentication Required:** No

**Request Body:**
```json
{
  "email": "<EMAIL>"
}
```

**Success Response:**
```json
{
  "message": "If the email exists, a password reset link has been sent"
}
```

#### POST /api/v1/user/confirm_password_reset/
Confirm password reset with token.

**Authentication Required:** No

**Request Body:**
```json
{
  "token": "uuid-token-from-email",
  "new_password": "new_secure_password",
  "confirm_password": "new_secure_password"
}
```

**Success Response:**
```json
{
  "message": "Password reset successfully. Please log in with your new password."
}
```

**Security Note:** After a successful password reset, all existing JWT tokens for the user are automatically invalidated, forcing the user to log in again with the new password.

### Organizations

#### GET /api/v1/user/organizations/
List all organizations with pagination and search.

**Query Parameters:**
- `page_number` (optional): Page number (default: 1)
- `page_size` (optional): Items per page (default: 10, max: 100)
- `search` (optional): Search string (searches name and description)
- `is_active` (optional): Filter by active status
- `sort_by` (optional): Field to sort by
- `sort_order` (optional): Sort order

**Response:**
```json
{
  "status": "success",
  "message": "Organizations retrieved successfully",
  "data": {
    "type": "organizations",
    "items": [
      {
        "id": 1,
        "name": "Ministry Of Health Nepal",
        "description": "Default organization for the health scope system",
        "is_active": true,
        "created_at": "2024-01-01T09:00:00Z",
        "updated_at": "2024-01-01T09:00:00Z"
      }
    ],
    "total_count": 10,
    "page_number": 1,
    "page_size": 10,
    "total_pages": 1,
    "sort_by": "name",
    "sort_order": "asc"
  }
}
```

### User Positions

#### GET /api/v1/user/positions
List all user positions with pagination and search.

> **Note:** The correct endpoint is `/api/v1/user/positions` (no trailing slash). The singular `/api/v1/user/position` is also supported for backward compatibility. Do not use a trailing slash.

**Query Parameters:**
- `page_number` (optional): Page number (default: 1)
- `page_size` (optional): Items per page (default: 10, max: 100)
- `search` (optional): Search string (searches name and description)
- `is_active` (optional): Filter by active status
- `sort_by` (optional): Field to sort by
- `sort_order` (optional): Sort order

**Response:**
```json
{
  "status": "success",
  "message": "User positions retrieved successfully",
  "data": {
    "type": "user_positions",
    "items": [
      {
        "id": 1,
        "name": "Admin",
        "description": "Administrator with full system access",
        "is_active": true,
        "created_at": "2024-01-01T09:00:00Z",
        "updated_at": "2024-01-01T09:00:00Z"
      },
      {
        "id": 2,
        "name": "Manager",
        "description": "Manager with limited administrative access",
        "is_active": true,
        "created_at": "2024-01-01T09:00:00Z",
        "updated_at": "2024-01-01T09:00:00Z"
      },
      {
        "id": 3,
        "name": "Staff",
        "description": "Regular staff member",
        "is_active": true,
        "created_at": "2024-01-01T09:00:00Z",
        "updated_at": "2024-01-01T09:00:00Z"
      }
    ],
    "total_count": 10,
    "page_number": 1,
    "page_size": 10,
    "total_pages": 1,
    "sort_by": "name",
    "sort_order": "asc"
  }
}
```

## Error Responses
All endpoints return consistent error responses:

```json
{
  "status": "error",
  "message": "Error description",
  "error": "Detailed error information"
}
```

## Rate Limiting
API requests are subject to rate limiting. Check the response headers for rate limit information.

## Examples

### Get User Positions
```bash
curl -X GET "http://localhost:8000/api/v1/user/positions?page_number=1&page_size=10" \
  -H "Authorization: Bearer <access_token>"
```

### Search for Users by Name
```bash
curl -X GET "http://localhost:8000/api/v1/user/?search=john&page_number=1&page_size=10" \
  -H "Authorization: Bearer <access_token>"
```

### Get Users with Pagination
```bash
curl -X GET "http://localhost:8000/api/v1/user/?page_number=2&page_size=20" \
  -H "Authorization: Bearer <access_token>"
```

### Filter Users by Authority Level
```bash
curl -X GET "http://localhost:8000/api/v1/user/?authority_level=district&status=active&page_number=1&page_size=15" \
  -H "Authorization: Bearer <access_token>"
```

### Sort Users by Creation Date (Newest First)
```bash
curl -X GET "http://localhost:8000/api/v1/user/?sort_by=created_at&sort_order=desc&page_number=1&page_size=10" \
  -H "Authorization: Bearer <access_token>"
```

### Sort Users by First Name (Alphabetical)
```bash
curl -X GET "http://localhost:8000/api/v1/user/?sort_by=first_name&sort_order=asc&page_number=1&page_size=10" \
  -H "Authorization: Bearer <access_token>"
```

### Combined Search, Filter, and Sort
```bash
curl -X GET "http://localhost:8000/api/v1/user/?search=admin&authority_level=center&sort_by=last_login&sort_order=desc&page_number=1&page_size=10" \
  -H "Authorization: Bearer <access_token>"
```

## Default Data

### Default Organization
- **Name**: "Ministry Of Health Nepal"
- **Description**: "Default organization for the health scope system"

### Default Positions
- **Admin**: "Administrator with full system access"
- **Manager**: "Manager with limited administrative access"
- **Staff**: "Regular staff member"

### Default Admin User
- **Username**: "admin"
- **Email**: "<EMAIL>"
- **Password**: "Admin@123!#"
- **Authority Level**: "center"
- **Authority Location ID**: 1
- **Status**: "active"

## Notes
- All timestamps are in ISO 8601 format (UTC)
- Passwords must meet Django's default password validation requirements
- Email addresses must be unique across the system
- Usernames must be unique across the system
- Soft delete is used - deleted users are marked as `is_deleted=True` but not physically removed
- JWT tokens are automatically blacklisted on logout
- **All existing JWT tokens are automatically invalidated after password changes and password resets for security**
- Organization and position fields accept both ID and name values
- Invitation emails are automatically sent when creating new users
- Password reset tokens expire after 24 hours
- Invitation tokens expire after 7 days
- JWT tokens now include authority and position information for frontend use