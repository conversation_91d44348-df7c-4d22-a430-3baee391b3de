"""
Middleware for logging API requests and performance metrics
"""
import time
import logging
from django.utils.deprecation import MiddlewareMixin
from django.http import JsonResponse
from django.conf import settings
from rest_framework_simplejwt.token_blacklist.models import BlacklistedToken
from rest_framework_simplejwt.exceptions import InvalidToken
from rest_framework_simplejwt.tokens import AccessToken
import json

logger = logging.getLogger('user')


class UserAPILoggingMiddleware(MiddlewareMixin):
    """
    Middleware to log API requests for the users app
    """
    
    def process_request(self, request):
        """Log the start of request processing"""
        request.start_time = time.time()
        
        # Only log requests to users API endpoints
        if '/api/v1/user' in request.path:
            user_email = None
            if hasattr(request, 'user') and request.user.is_authenticated:
                user_email = request.user.email
            
            logger.debug(f"API Request started: {request.method} {request.path} - User: {user_email}")
    
    def process_response(self, request, response):
        """Log the completion of request processing"""
        # Only log responses for users API endpoints
        if '/api/v1/user' in request.path and hasattr(request, 'start_time'):
            response_time = time.time() - request.start_time
            
            user_email = None
            if hasattr(request, 'user') and request.user.is_authenticated:
                user_email = request.user.email
            
            # Log the API request with response details
            logger.debug(
                f"API Request completed: {request.method} {request.path} - "
                f"Status: {response.status_code} - "
                f"Time: {response_time:.3f}s - "
                f"User: {user_email}"
            )
            
            # Log performance metrics for slow requests
            if response_time > 1.0:  # Log requests taking more than 1 second
                logger.warning(
                    f"Slow API request: {request.method} {request.path} - "
                    f"Time: {response_time:.3f}s - "
                    f"User: {user_email}"
                )
        
        return response
    
    def process_exception(self, request, exception):
        """Log exceptions that occur during request processing"""
        if '/api/v1/user' in request.path:
            user_email = None
            if hasattr(request, 'user') and request.user.is_authenticated:
                user_email = request.user.email
            
            logger.error(
                f"API Exception: {request.method} {request.path} - "
                f"Exception: {type(exception).__name__}: {str(exception)} - "
                f"User: {user_email}"
            )


class UserSecurityLoggingMiddleware(MiddlewareMixin):
    """
    Middleware to log security-related events
    """
    
    def process_request(self, request):
        """Log security events during request processing"""
        # Only log for users API endpoints
        if '/api/v1/user' in request.path:
            user_email = None
            if hasattr(request, 'user') and request.user.is_authenticated:
                user_email = request.user.email
            
            # Log authentication attempts
            if request.path.endswith('/auth/login') and request.method == 'POST':
                logger.info(f"Login attempt from IP: {self.get_client_ip(request)} - User: {user_email}")
            
            # Log logout attempts
            elif request.path.endswith('/auth/logout') and request.method == 'POST':
                logger.info(f"Logout attempt from IP: {self.get_client_ip(request)} - User: {user_email}")
            
            # Log password reset attempts
            elif request.path.endswith('/request_password_reset') and request.method == 'POST':
                logger.info(f"Password reset request from IP: {self.get_client_ip(request)} - User: {user_email}")
            
            # Log user management operations
            elif request.method in ['POST', 'PUT', 'PATCH', 'DELETE'] and '/user/' in request.path:
                logger.info(
                    f"User management operation: {request.method} {request.path} - "
                    f"IP: {self.get_client_ip(request)} - "
                    f"User: {user_email}"
                )
    
    def get_client_ip(self, request):
        """Get the client IP address"""
        x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
        if x_forwarded_for:
            ip = x_forwarded_for.split(',')[0]
        else:
            ip = request.META.get('REMOTE_ADDR')
        return ip


class UserAuditLoggingMiddleware(MiddlewareMixin):
    """
    Middleware to log audit events for user actions
    """
    
    def process_request(self, request):
        """Log audit events during request processing"""
        # Only log for users API endpoints
        if '/api/v1/user' in request.path:
            user_email = None
            if hasattr(request, 'user') and request.user.is_authenticated:
                user_email = request.user.email
            
            # Log sensitive operations
            if request.method in ['POST', 'PUT', 'PATCH', 'DELETE']:
                # User creation
                if request.path.endswith('/user') and request.method == 'POST':
                    logger.info(f"User creation attempt by: {user_email}")
                
                # User deletion
                elif '/delete' in request.path and request.method == 'POST':
                    logger.warning(f"User deletion attempt by: {user_email}")
                
                # User activation/deactivation
                elif any(action in request.path for action in ['/activate', '/deactivate', '/enable', '/disable']):
                    logger.info(f"User status change attempt by: {user_email}")
                
                # Password operations
                elif any(action in request.path for action in ['/change_password', '/confirm_password_reset']):
                    logger.info(f"Password operation attempt by: {user_email}")
    
    def process_response(self, request, response):
        """Log audit events for successful operations"""
        # Only log for users API endpoints
        if '/api/v1/user' in request.path:
            user_email = None
            if hasattr(request, 'user') and request.user.is_authenticated:
                user_email = request.user.email
            
            # Log successful sensitive operations
            if response.status_code in [200, 201] and request.method in ['POST', 'PUT', 'PATCH', 'DELETE']:
                # User creation
                if request.path.endswith('/user') and request.method == 'POST':
                    logger.info(f"User creation successful by: {user_email}")
                
                # User deletion
                elif '/delete' in request.path and request.method == 'POST':
                    logger.warning(f"User deletion successful by: {user_email}")
                
                # User activation/deactivation
                elif any(action in request.path for action in ['/activate', '/deactivate', '/enable', '/disable']):
                    logger.info(f"User status change successful by: {user_email}")
                
                # Password operations
                elif any(action in request.path for action in ['/change_password', '/confirm_password_reset']):
                    logger.info(f"Password operation successful by: {user_email}")
        
        return response 


class JWTBlacklistMiddleware(MiddlewareMixin):
    """Middleware to check JWT tokens against blacklist"""
    
    def process_request(self, request):
        """Check JWT tokens against blacklist on every request"""
        # Skip for non-API requests and certain endpoints
        if not request.path.startswith('/api/'):
            return None
        
        # Skip for login, token refresh, and other auth endpoints
        auth_endpoints = [
            '/api/v1/user/auth/login',
            '/api/v1/user/auth/refresh',
            '/api/v1/user/request_password_reset',
            '/api/v1/user/confirm_password_reset',
            '/api/v1/user/accept_invitation',
            '/health/',
        ]
        
        if any(request.path.endswith(endpoint) for endpoint in auth_endpoints):
            return None
        
        # Check for Authorization header
        auth_header = request.META.get('HTTP_AUTHORIZATION', '')
        if not auth_header.startswith('Bearer '):
            logger.debug(f"No Bearer token found for {request.path}")
            return None
        
        # Extract token
        token = auth_header.split(' ')[1]
        logger.debug(f"Checking token for {request.path}")
        
        try:
            # Decode token to get JTI
            access_token = AccessToken(token)
            jti = access_token.get('jti')
            
            if not jti:
                logger.warning("Token has no JTI - cannot check blacklist")
                return None
            
            # Check if token is blacklisted
            is_blacklisted = BlacklistedToken.objects.filter(token__jti=jti).exists()
            
            if is_blacklisted:
                logger.warning(f"Blacklisted token attempted: {jti} for {request.path}")
                return JsonResponse({
                    'detail': 'Token is blacklisted',
                    'code': 'token_blacklisted'
                }, status=401)
            
            logger.debug(f"Token {jti} is valid and not blacklisted for {request.path}")
            
        except InvalidToken as e:
            logger.warning(f"Invalid token in middleware: {str(e)} for {request.path}")
            return JsonResponse({
                'detail': 'Token is invalid or expired',
                'code': 'token_invalid'
            }, status=401)
        except Exception as e:
            logger.error(f"Error checking token blacklist in middleware: {str(e)} for {request.path}")
            # Don't block the request if there's an error checking blacklist
            return None
        
        return None 