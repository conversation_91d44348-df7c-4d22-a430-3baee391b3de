from rest_framework_simplejwt.tokens import AccessToken, RefreshToken
from django.utils import timezone
import uuid


class CustomAccessToken(AccessToken):
    """Custom access token with additional user claims"""
    
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
    
    @classmethod
    def for_user(cls, user):
        """Create a token for a user with custom claims"""
        token = cls()
        token['user_id'] = user.id
        token['username'] = user.username
        token['email'] = user.email
        token['first_name'] = user.first_name
        token['last_name'] = user.last_name
        token['authority_level'] = user.authority_level
        token['authority_location_id'] = user.authority_location_id
        token['position_id'] = user.position.id if user.position else None
        token['position_name'] = user.position.name if user.position else None
        
        # Ensure JTI is set for blacklisting
        if 'jti' not in token:
            token['jti'] = str(uuid.uuid4())
        
        # Get authority location name based on authority level
        authority_location_name = cls._get_authority_location_name(user)
        token['authority_location_name'] = authority_location_name
        
        return token
    
    @staticmethod
    def _get_authority_location_name(user):
        """Get the name of the authority location based on user's authority level"""
        try:
            if user.authority_level == "center":
                return "Nepal"  # Default country name for center level
            
            elif user.authority_level == "province":
                from location.models import Province
                try:
                    province = Province.objects.get(id=user.authority_location_id, is_active=True)
                    return province.name
                except Province.DoesNotExist:
                    return None
            
            elif user.authority_level == "district":
                from location.models import District
                try:
                    district = District.objects.get(id=user.authority_location_id, is_active=True)
                    return district.name
                except District.DoesNotExist:
                    return None
            
            elif user.authority_level == "municipality":
                from location.models import Municipality
                try:
                    municipality = Municipality.objects.get(id=user.authority_location_id, is_active=True)
                    return municipality.name
                except Municipality.DoesNotExist:
                    return None
            
            elif user.authority_level == "ward":
                from location.models import Ward
                try:
                    ward = Ward.objects.get(id=user.authority_location_id, is_active=True)
                    return ward.name
                except Ward.DoesNotExist:
                    return None
            
            return None
            
        except ImportError:
            # If location models are not available, return None
            return None


class CustomRefreshToken(RefreshToken):
    """Custom refresh token that uses custom access token"""
    
    @classmethod
    def for_user(cls, user):
        """Create a refresh token for a user"""
        token = cls()
        token['user_id'] = user.id
        token['username'] = user.username
        token['email'] = user.email
        token['first_name'] = user.first_name
        token['last_name'] = user.last_name
        token['authority_level'] = user.authority_level
        token['authority_location_id'] = user.authority_location_id
        token['position_id'] = user.position.id if user.position else None
        token['position_name'] = user.position.name if user.position else None
        
        # Ensure JTI is set for blacklisting
        if 'jti' not in token:
            token['jti'] = str(uuid.uuid4())
        
        # Get authority location name based on authority level
        authority_location_name = CustomAccessToken._get_authority_location_name(user)
        token['authority_location_name'] = authority_location_name
        
        return token
    
    @property
    def access_token(self):
        """Return a custom access token"""
        access = CustomAccessToken()
        
        # Copy claims from refresh token
        access['user_id'] = self['user_id']
        access['username'] = self['username']
        access['email'] = self['email']
        access['first_name'] = self['first_name']
        access['last_name'] = self['last_name']
        access['authority_level'] = self['authority_level']
        access['authority_location_id'] = self['authority_location_id']
        access['authority_location_name'] = self['authority_location_name']
        access['position_id'] = self['position_id']
        access['position_name'] = self['position_name']
        access['authority_location_name'] = self['authority_location_name']
        
        # Ensure JTI is set for blacklisting
        if 'jti' not in access:
            access['jti'] = str(uuid.uuid4())
        
        # Set token type and expiration
        access.set_exp(from_time=timezone.now())
        access.set_iat(at_time=timezone.now())
        access['token_type'] = 'access'
        access['exp'] = self.current_time + self.lifetime
        
        return access 