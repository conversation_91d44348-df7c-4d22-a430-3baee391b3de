from django.urls import path, include
from rest_framework.routers import <PERSON>fault<PERSON>out<PERSON>
from .views import UserViewSet, OrganizationViewSet, UserPositionViewSet, LoginView, LogoutView

router = DefaultRouter(trailing_slash=False)
router.register(r'', UserViewSet)
router.register(r'organization', OrganizationViewSet)
router.register(r'position', UserPositionViewSet, basename='user-position')
router.register(r'positions', UserPositionViewSet, basename='user-positions')

urlpatterns = [
    path('', include(router.urls)),
    path('auth/login', LoginView.as_view(), name='login'),
    path('auth/logout', LogoutView.as_view(), name='logout'),
]