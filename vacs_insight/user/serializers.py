from rest_framework import serializers
from django.contrib.auth import get_user_model
from .models import User, Organization, UserPosition
from django.contrib.contenttypes.models import ContentType
from django.utils import timezone
from datetime import timedelta
import uuid
from django.contrib.auth import authenticate
from django.contrib.auth.password_validation import validate_password
from django.db import models
import logging

User = get_user_model()
logger = logging.getLogger('user')


class OrganizationSerializer(serializers.ModelSerializer):
    """Serializer for Organization model"""
    
    class Meta:
        model = Organization
        fields = ('id', 'name', 'description', 'is_active', 'created_at', 'updated_at')
        read_only_fields = ('id', 'created_at', 'updated_at')


class UserPositionSerializer(serializers.ModelSerializer):
    """Serializer for UserPosition model"""
    
    class Meta:
        model = UserPosition
        fields = ('id', 'name', 'description', 'is_active', 'created_at', 'updated_at')
        read_only_fields = ('id', 'created_at', 'updated_at')


class UserSerializer(serializers.ModelSerializer):
    full_name = serializers.SerializerMethodField()
    created_by_name = serializers.SerializerMethodField()
    updated_by_name = serializers.SerializerMethodField()
    organization_name = serializers.CharField(source='organization.name', read_only=True)
    position_name = serializers.CharField(source='position.name', read_only=True)
    
    class Meta:
        model = User
        fields = ('id', 'first_name', 'middle_name', 'last_name', 'full_name', 'email', 
                 'phone', 'phone_number', 'status', 'authority_level', 'authority_location_id', 'assigned_admin_unit_type', 'assigned_admin_unit_id',
                 'organization', 'organization_name', 'position', 'position_name', 'is_active', 'last_login', 
                 'created_at', 'created_by', 'created_by_name', 'updated_at', 'updated_by', 'updated_by_name')
        read_only_fields = ('id', 'created_at', 'created_by', 'created_by_name', 
                          'updated_at', 'updated_by', 'updated_by_name', 'last_login')

    def get_full_name(self, obj):
        return obj.get_full_name()
    
    def get_created_by_name(self, obj):
        return obj.created_by.get_full_name() if obj.created_by else None
    
    def get_updated_by_name(self, obj):
        return obj.updated_by.get_full_name() if obj.updated_by else None


class UserCreateSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = ('first_name', 'middle_name', 'last_name', 'email', 'phone', 'phone_number', 'username',
                 'authority_level', 'authority_location_id', 'assigned_admin_unit_type', 'assigned_admin_unit_id', 'organization', 'position')

    def validate_email(self, value):
        if User.objects.filter(email=value, is_deleted=False).exists():
            logger.warning(f"User creation failed: Email already exists - {value}")
            raise serializers.ValidationError("A user with this email already exists.")
        return value

    def validate_username(self, value):
        if User.objects.filter(username=value, is_deleted=False).exists():
            logger.warning(f"User creation failed: Username already exists - {value}")
            raise serializers.ValidationError("A user with this username already exists.")
        return value

    def validate_authority_location_id(self, value):
        """Validate authority_location_id based on authority_level"""
        authority_level = self.initial_data.get('authority_level')
        
        if authority_level and value:
            try:
                from location.models import Province, District, Municipality, Ward
                
                if authority_level == "center":
                    if value != 1:
                        raise serializers.ValidationError("Center level users must have authority_location_id = 1")
                
                elif authority_level == "province":
                    if not Province.objects.filter(id=value, is_active=True).exists():
                        raise serializers.ValidationError(f"Province with id {value} does not exist or is not active")
                
                elif authority_level == "district":
                    if not District.objects.filter(id=value, is_active=True).exists():
                        raise serializers.ValidationError(f"District with id {value} does not exist or is not active")
                
                elif authority_level == "municipality":
                    if not Municipality.objects.filter(id=value, is_active=True).exists():
                        raise serializers.ValidationError(f"Municipality with id {value} does not exist or is not active")
                
                elif authority_level == "ward":
                    if not Ward.objects.filter(id=value, is_active=True).exists():
                        raise serializers.ValidationError(f"Ward with id {value} does not exist or is not active")
                        
            except ImportError:
                # If location models are not available during migration, skip validation
                pass
        
        return value

    def validate_position(self, value):
        """Validate position field - accept both ID and name"""
        if value is None:
            return value
        
        # If it's already a UserPosition instance, return it
        if hasattr(value, 'id'):
            return value
        
        # If it's an integer (ID), try to get the position
        if isinstance(value, int):
            try:
                return UserPosition.objects.get(id=value, is_active=True)
            except UserPosition.DoesNotExist:
                raise serializers.ValidationError("Invalid position ID")
        
        # If it's a string (name), try to get the position by name
        if isinstance(value, str):
            try:
                return UserPosition.objects.get(name__iexact=value, is_active=True)
            except UserPosition.DoesNotExist:
                raise serializers.ValidationError(f"Position '{value}' not found")
        
        raise serializers.ValidationError("Invalid position value")

    def validate_organization(self, value):
        """Validate organization field - accept both ID and name"""
        if value is None:
            return value
        
        # If it's already an Organization instance, return it
        if hasattr(value, 'id'):
            return value
        
        # If it's an integer (ID), try to get the organization
        if isinstance(value, int):
            try:
                return Organization.objects.get(id=value, is_active=True)
            except Organization.DoesNotExist:
                raise serializers.ValidationError("Invalid organization ID")
        
        # If it's a string (name), try to get the organization by name
        if isinstance(value, str):
            try:
                return Organization.objects.get(name__iexact=value, is_active=True)
            except Organization.DoesNotExist:
                raise serializers.ValidationError(f"Organization '{value}' not found")
        
        raise serializers.ValidationError("Invalid organization value")

    def create(self, validated_data):
        try:
            user = User(**validated_data)
            user.status = 'invited'
            user.invitation_token = uuid.uuid4()
            user.invitation_expires_at = timezone.now() + timedelta(days=7)  # 7 days expiry
            user.save()
            logger.info(f"User created successfully: {user.email}")
            return user
        except Exception as e:
            logger.error(f"User creation failed: {str(e)}")
            raise


class UserUpdateSerializer(serializers.ModelSerializer):
    class Meta:
        model = User
        fields = ('first_name', 'middle_name', 'last_name', 'phone', 'phone_number',
                 'authority_level', 'authority_location_id', 'assigned_admin_unit_type', 'assigned_admin_unit_id', 'organization', 'position')

    def validate_email(self, value):
        user_id = self.instance.id if self.instance else None
        if User.objects.filter(email=value, is_deleted=False).exclude(id=user_id).exists():
            logger.warning(f"User update failed: Email already exists - {value}")
            raise serializers.ValidationError("A user with this email already exists.")
        return value

    def validate_authority_location_id(self, value):
        """Validate authority_location_id based on authority_level"""
        authority_level = self.initial_data.get('authority_level')
        
        if authority_level and value:
            try:
                from location.models import Province, District, Municipality, Ward
                
                if authority_level == "center":
                    if value != 1:
                        raise serializers.ValidationError("Center level users must have authority_location_id = 1")
                
                elif authority_level == "province":
                    if not Province.objects.filter(id=value, is_active=True).exists():
                        raise serializers.ValidationError(f"Province with id {value} does not exist or is not active")
                
                elif authority_level == "district":
                    if not District.objects.filter(id=value, is_active=True).exists():
                        raise serializers.ValidationError(f"District with id {value} does not exist or is not active")
                
                elif authority_level == "municipality":
                    if not Municipality.objects.filter(id=value, is_active=True).exists():
                        raise serializers.ValidationError(f"Municipality with id {value} does not exist or is not active")
                
                elif authority_level == "ward":
                    if not Ward.objects.filter(id=value, is_active=True).exists():
                        raise serializers.ValidationError(f"Ward with id {value} does not exist or is not active")
                        
            except ImportError:
                # If location models are not available during migration, skip validation
                pass
        
        return value

    def validate_position(self, value):
        """Validate position field - accept both ID and name"""
        if value is None:
            return value
        
        # If it's already a UserPosition instance, return it
        if hasattr(value, 'id'):
            return value
        
        # If it's an integer (ID), try to get the position
        if isinstance(value, int):
            try:
                return UserPosition.objects.get(id=value, is_active=True)
            except UserPosition.DoesNotExist:
                raise serializers.ValidationError("Invalid position ID")
        
        # If it's a string (name), try to get the position by name
        if isinstance(value, str):
            try:
                return UserPosition.objects.get(name__iexact=value, is_active=True)
            except UserPosition.DoesNotExist:
                raise serializers.ValidationError(f"Position '{value}' not found")
        
        raise serializers.ValidationError("Invalid position value")

    def validate_organization(self, value):
        """Validate organization field - accept both ID and name"""
        if value is None:
            return value
        
        # If it's already an Organization instance, return it
        if hasattr(value, 'id'):
            return value
        
        # If it's an integer (ID), try to get the organization
        if isinstance(value, int):
            try:
                return Organization.objects.get(id=value, is_active=True)
            except Organization.DoesNotExist:
                raise serializers.ValidationError("Invalid organization ID")
        
        # If it's a string (name), try to get the organization by name
        if isinstance(value, str):
            try:
                return Organization.objects.get(name__iexact=value, is_active=True)
            except Organization.DoesNotExist:
                raise serializers.ValidationError(f"Organization '{value}' not found")
        
        raise serializers.ValidationError("Invalid organization value")


class UserInvitationAcceptSerializer(serializers.Serializer):
    token = serializers.UUIDField()
    password = serializers.CharField(write_only=True, required=True, style={'input_type': 'password'})
    confirm_password = serializers.CharField(write_only=True, required=True, style={'input_type': 'password'})

    def validate_token(self, value):
        try:
            user = User.objects.get(
                invitation_token=value,
                status='invited',
                invitation_expires_at__gt=timezone.now(),
                is_deleted=False
            )
            return value
        except User.DoesNotExist:
            logger.warning(f"Invitation acceptance failed: Invalid or expired token - {value}")
            raise serializers.ValidationError("Invalid or expired invitation token")

    def validate(self, data):
        if data['password'] != data['confirm_password']:
            logger.warning(f"Invitation acceptance failed: Passwords do not match")
            raise serializers.ValidationError("Passwords do not match")
        
        # Validate password strength
        try:
            validate_password(data['password'])
        except Exception as e:
            logger.warning(f"Invitation acceptance failed: Password validation error - {str(e)}")
            raise serializers.ValidationError(str(e))
        
        return data

    def save(self, **kwargs):
        try:
            user = User.objects.get(invitation_token=self.validated_data['token'])
            user.set_password(self.validated_data['password'])
            user.activate()  # This will set status to active and clear invitation fields
            logger.info(f"Invitation accepted successfully for user: {user.email}")
            return user
        except Exception as e:
            logger.error(f"Invitation acceptance failed: {str(e)}")
            raise


class LoginSerializer(serializers.Serializer):
    username = serializers.CharField(required=True)
    password = serializers.CharField(write_only=True, style={'input_type': 'password'})

    def validate(self, data):
        username_input = data.get('username', '').strip()
        password = data.get('password', '').strip()
        
        if not username_input or not password:
            logger.warning(f"Login failed: Missing username or password")
            raise serializers.ValidationError("Both username and password are required")
        
        # Try to find user by email or username (case-insensitive)
        user = User.objects.filter(
            models.Q(email__iexact=username_input) | models.Q(username__iexact=username_input),
            is_deleted=False
        ).first()
        
        if not user:
            logger.warning(f"Login failed: User not found - {username_input}")
            raise serializers.ValidationError("Invalid credentials")
        
        # Authenticate using the found user's username
        user_auth = authenticate(username=user.username, password=password)
        if not user_auth:
            logger.warning(f"Login failed: Invalid password for user - {user.email}")
            raise serializers.ValidationError("Invalid credentials")
        
        if user_auth.is_deleted:
            logger.warning(f"Login failed: Deleted user account - {user.email}")
            raise serializers.ValidationError("User account has been deleted")
        
        if user_auth.status != 'active':
            logger.warning(f"Login failed: Inactive user account - {user.email}, status: {user_auth.status}")
            raise serializers.ValidationError("User account is not active")
        
        return {'user': user_auth}


class PasswordChangeSerializer(serializers.Serializer):
    old_password = serializers.CharField(write_only=True, style={'input_type': 'password'})
    new_password = serializers.CharField(write_only=True, style={'input_type': 'password'})
    confirm_password = serializers.CharField(write_only=True, style={'input_type': 'password'})

    def validate(self, data):
        if data['new_password'] != data['confirm_password']:
            logger.warning(f"Password change failed: New passwords do not match")
            raise serializers.ValidationError("New passwords do not match")
        
        # Validate password strength
        try:
            validate_password(data['new_password'])
        except Exception as e:
            logger.warning(f"Password change failed: Password validation error - {str(e)}")
            raise serializers.ValidationError(str(e))
        
        return data

    def validate_old_password(self, value):
        user = self.context.get('user')
        if user and not user.check_password(value):
            logger.warning(f"Password change failed: Incorrect current password for user {user.email}")
            raise serializers.ValidationError("Current password is incorrect")
        return value

    def save(self, **kwargs):
        user = self.context.get('user')
        if user:
            user.set_password(self.validated_data['new_password'])
            user.save(update_fields=['password'])
            logger.info(f"Password changed successfully for user: {user.email}")
        return user


class PasswordResetRequestSerializer(serializers.Serializer):
    email = serializers.EmailField()

    def validate_email(self, value):
        try:
            user = User.objects.get(email=value, is_deleted=False, is_active=True)
            return value
        except User.DoesNotExist:
            logger.info(f"Password reset requested for non-existent or inactive email: {value}")
            raise serializers.ValidationError("No active user found with this email address")


class PasswordResetConfirmSerializer(serializers.Serializer):
    token = serializers.UUIDField()
    new_password = serializers.CharField(write_only=True, style={'input_type': 'password'})
    confirm_password = serializers.CharField(write_only=True, style={'input_type': 'password'})

    def validate(self, data):
        if data['new_password'] != data['confirm_password']:
            logger.warning(f"Password reset failed: Passwords do not match")
            raise serializers.ValidationError("Passwords do not match")
        
        # Validate password strength
        try:
            validate_password(data['new_password'])
        except Exception as e:
            logger.warning(f"Password reset failed: Password validation error - {str(e)}")
            raise serializers.ValidationError(str(e))
        
        return data


class DisableUserSerializer(serializers.Serializer):
    disable_reason = serializers.CharField(required=True, max_length=500)

    def validate_disable_reason(self, value):
        if not value.strip():
            logger.warning(f"User disable failed: Empty disable reason")
            raise serializers.ValidationError("Disable reason is required")
        return value


class EnableUserSerializer(serializers.Serializer):
    pass


class UserProfileSerializer(serializers.ModelSerializer):
    full_name = serializers.SerializerMethodField()
    organization_name = serializers.CharField(source='organization.name', read_only=True)
    position_name = serializers.CharField(source='position.name', read_only=True)
    
    class Meta:
        model = User
        fields = ('id', 'first_name', 'middle_name', 'last_name', 'full_name', 'email', 
                 'phone', 'phone_number', 'status', 'authority_level', 'authority_location_id', 'organization', 'organization_name', 
                 'position', 'position_name', 'last_login', 'created_at')
        read_only_fields = ('id', 'email', 'status', 'authority_level', 'authority_location_id', 'last_login', 'created_at')

    def get_full_name(self, obj):
        return obj.get_full_name()


class UserListSerializer(serializers.ModelSerializer):
    full_name = serializers.SerializerMethodField()
    organization_name = serializers.CharField(source='organization.name', read_only=True)
    position_name = serializers.CharField(source='position.name', read_only=True)
    
    class Meta:
        model = User
        fields = ('id', 'first_name', 'last_name', 'full_name', 'email', 
                 'status', 'authority_level', 'authority_location_id', 'is_active', 'organization_name', 'position_name', 'last_login', 'created_at')
        read_only_fields = ('id', 'email', 'status', 'authority_level', 'authority_location_id', 'last_login', 'created_at')

    def get_full_name(self, obj):
        return obj.get_full_name()
