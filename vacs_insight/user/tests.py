#!/usr/bin/env python3
"""
Test script for User APIs
"""
import requests
import json
from django.test import TestCase
from django.urls import reverse
from rest_framework.test import APITestCase, APIClient
from rest_framework import status
from django.contrib.auth import get_user_model
from user.models import UserPosition
from rest_framework_simplejwt.token_blacklist.models import OutstandingToken, BlacklistedToken

User = get_user_model()


class PasswordChangeTokenInvalidationTestCase(APITestCase):
    """Test case for password change token invalidation"""
    
    def setUp(self):
        """Set up test data"""
        # Create admin position
        self.admin_position, _ = UserPosition.objects.get_or_create(
            name="Admin", 
            defaults={"description": "Administrator", "is_active": True}
        )
        
        # Create test user
        self.user = User.objects.create_user(
            username="testuser",
            email="<EMAIL>",
            password="TestPassword123!",
            first_name="Test",
            last_name="User",
            authority_level="center",
            position=self.admin_position,
            status="active"
        )
        
        self.client = APIClient()
        
    def test_password_change_invalidates_tokens(self):
        """Test that password change invalidates all existing tokens"""
        # Login to get tokens
        login_data = {
            "username": "<EMAIL>",
            "password": "TestPassword123!"
        }
        
        login_response = self.client.post(reverse('login'), login_data)
        self.assertEqual(login_response.status_code, status.HTTP_200_OK)
        
        access_token = login_response.data.get("accessToken")
        refresh_token = login_response.data.get("refreshToken")
        
        # Verify we can access protected endpoint with the token
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {access_token}')
        profile_response = self.client.get(reverse('user-profile'))
        self.assertEqual(profile_response.status_code, status.HTTP_200_OK)
        
        # Change password
        password_change_data = {
            "old_password": "TestPassword123!",
            "new_password": "NewSecurePassword123!",
            "confirm_password": "NewSecurePassword123!"
        }
        
        password_change_response = self.client.post(
            reverse('user-change-password'), 
            password_change_data
        )
        self.assertEqual(password_change_response.status_code, status.HTTP_200_OK)
        
        # Verify the response message
        self.assertIn("Please log in again", password_change_response.data.get("message", ""))
        
        # Try to access profile with the old token - should fail
        profile_response_after = self.client.get(reverse('user-profile'))
        self.assertEqual(profile_response_after.status_code, status.HTTP_401_UNAUTHORIZED)
        
        # Try to refresh the token - should also fail
        refresh_data = {"refresh": refresh_token}
        refresh_response = self.client.post(reverse('token_refresh'), refresh_data)
        self.assertEqual(refresh_response.status_code, status.HTTP_401_UNAUTHORIZED)
        
        # Verify tokens are blacklisted in database
        outstanding_tokens = OutstandingToken.objects.filter(user_id=self.user.id)
        blacklisted_tokens = BlacklistedToken.objects.filter(token__user_id=self.user.id)
        self.assertEqual(outstanding_tokens.count(), blacklisted_tokens.count())
        
        # Login with new password to verify it works
        new_login_data = {
            "username": "<EMAIL>",
            "password": "NewSecurePassword123!"
        }
        
        new_login_response = self.client.post(reverse('login'), new_login_data)
        self.assertEqual(new_login_response.status_code, status.HTTP_200_OK)
        
        # Change password back to original for cleanup
        new_access_token = new_login_response.data.get("accessToken")
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {new_access_token}')
        
        revert_password_data = {
            "old_password": "NewSecurePassword123!",
            "new_password": "TestPassword123!",
            "confirm_password": "TestPassword123!"
        }
        
        revert_response = self.client.post(
            reverse('user-change-password'), 
            revert_password_data
        )
        self.assertEqual(revert_response.status_code, status.HTTP_200_OK)


# Legacy test functions for manual testing
BASE_URL = "http://localhost:8000/api/users"


def test_login():
    """Test login API"""
    print("Testing Login API...")

    url = f"{BASE_URL}/auth/login"
    data = {
        "email": "<EMAIL>",
        "password": "Admin@123!#"
    }

    try:
        response = requests.post(url, json=data)
        print(f"Status Code: {response.status_code}")

        if response.status_code == 200:
            result = response.json()
            print("✅ Login successful!")
            print(f"Access Token: {result.get('access', '')[:50]}...")
            print(f"User: {result.get('user', {}).get('email', '')}")
            return result.get('access')
        else:
            print(f"❌ Login failed: {response.text}")
            return None
    except requests.exceptions.ConnectionError:
        print("❌ Could not connect to server. Make sure the server is running on localhost:8000")
        return None
    except Exception as e:
        print(f"❌ Error: {e}")
        return None


def test_user_list(token):
    """Test user list API"""
    print("\nTesting User List API...")

    url = f"{BASE_URL}/users"
    headers = {"Authorization": f"Bearer {token}"}

    try:
        response = requests.get(url, headers=headers)
        print(f"Status Code: {response.status_code}")

        if response.status_code == 200:
            result = response.json()
            print("✅ User list retrieved successfully!")
            print(f"Total users: {result.get('count', 0)}")
            if result.get('results'):
                user = result['results'][0]
                print(f"First user: {user.get('email', '')} - {user.get('full_name', '')}")
        else:
            print(f"❌ Failed to get user list: {response.text}")
    except Exception as e:
        print(f"❌ Error: {e}")


def test_user_profile(token):
    """Test user profile API"""
    print("\nTesting User Profile API...")

    url = f"{BASE_URL}/users/profile"
    headers = {"Authorization": f"Bearer {token}"}

    try:
        response = requests.get(url, headers=headers)
        print(f"Status Code: {response.status_code}")

        if response.status_code == 200:
            result = response.json()
            print("✅ User profile retrieved successfully!")
            print(f"User: {result.get('email', '')} - {result.get('full_name', '')}")
            print(f"Role: {result.get('role', '')}")
            print(f"Status: {result.get('status', '')}")
        else:
            print(f"❌ Failed to get user profile: {response.text}")
    except Exception as e:
        print(f"❌ Error: {e}")


def test_user_stats(token):
    """Test user stats API"""
    print("\nTesting User Stats API...")

    url = f"{BASE_URL}/users/stats"
    headers = {"Authorization": f"Bearer {token}"}

    try:
        response = requests.get(url, headers=headers)
        print(f"Status Code: {response.status_code}")

        if response.status_code == 200:
            result = response.json()
            print("✅ User stats retrieved successfully!")
            print(f"Total users: {result.get('total_users', 0)}")
            print(f"Active users: {result.get('active_users', 0)}")
            print(f"Invited users: {result.get('invited_users', 0)}")
            print(f"Inactive users: {result.get('inactive_users', 0)}")
            print(f"Role distribution: {result.get('role_distribution', {})}")
        else:
            print(f"❌ Failed to get user stats: {response.text}")
    except Exception as e:
        print(f"❌ Error: {e}")


def test_password_change_invalidates_tokens():
    """Test that password change invalidates all existing tokens"""
    print("\n🔐 Testing Password Change Token Invalidation...")
    
    # First login to get tokens
    login_data = {
        "username": "<EMAIL>",
        "password": "Admin@123!#"
    }
    
    login_response = requests.post(f"{BASE_URL}/auth/login", json=login_data)
    print(f"Login Status: {login_response.status_code}")
    
    if login_response.status_code != 200:
        print("❌ Failed to login for token invalidation test")
        return
    
    access_token = login_response.json().get("accessToken")
    refresh_token = login_response.json().get("refreshToken")
    
    # Test that we can access a protected endpoint with the token
    headers = {"Authorization": f"Bearer {access_token}"}
    profile_response = requests.get(f"{BASE_URL}/profile", headers=headers)
    print(f"Profile Access Before Password Change: {profile_response.status_code}")
    
    if profile_response.status_code != 200:
        print("❌ Failed to access profile before password change")
        return
    
    # Change password
    password_change_data = {
        "old_password": "Admin@123!#",
        "new_password": "NewSecurePassword123!",
        "confirm_password": "NewSecurePassword123!"
    }
    
    password_change_response = requests.post(
        f"{BASE_URL}/change_password", 
        json=password_change_data, 
        headers=headers
    )
    print(f"Password Change Status: {password_change_response.status_code}")
    
    if password_change_response.status_code != 200:
        print("❌ Failed to change password")
        return
    
    # Try to access profile with the old token - should fail
    profile_response_after = requests.get(f"{BASE_URL}/profile", headers=headers)
    print(f"Profile Access After Password Change: {profile_response_after.status_code}")
    
    if profile_response_after.status_code == 401:
        print("✅ Token successfully invalidated after password change")
    else:
        print("❌ Token was not invalidated after password change")
    
    # Try to refresh the token - should also fail
    refresh_data = {"refresh": refresh_token}
    refresh_response = requests.post(f"{BASE_URL}/auth/refresh", json=refresh_data)
    print(f"Token Refresh After Password Change: {refresh_response.status_code}")
    
    if refresh_response.status_code == 401:
        print("✅ Refresh token successfully invalidated after password change")
    else:
        print("❌ Refresh token was not invalidated after password change")
    
    # Login with new password to verify it works
    new_login_data = {
        "username": "<EMAIL>",
        "password": "NewSecurePassword123!"
    }
    
    new_login_response = requests.post(f"{BASE_URL}/auth/login", json=new_login_data)
    print(f"Login with New Password: {new_login_response.status_code}")
    
    if new_login_response.status_code == 200:
        print("✅ Successfully logged in with new password")
        
        # Change password back to original for other tests
        new_access_token = new_login_response.json().get("accessToken")
        new_headers = {"Authorization": f"Bearer {new_access_token}"}
        
        revert_password_data = {
            "old_password": "NewSecurePassword123!",
            "new_password": "Admin@123!#",
            "confirm_password": "Admin@123!#"
        }
        
        revert_response = requests.post(
            f"{BASE_URL}/change_password", 
            json=revert_password_data, 
            headers=new_headers
        )
        print(f"Revert Password Status: {revert_response.status_code}")
        
        if revert_response.status_code == 200:
            print("✅ Successfully reverted password to original")
        else:
            print("❌ Failed to revert password to original")
    else:
        print("❌ Failed to login with new password")


def main():
    """Main test function"""
    print("🧪 Starting User API Tests...")

    # Test login
    access_token = test_login()

    if access_token:
        print(f"✅ Login test passed. Access token: {access_token[:50]}...")
    else:
        print("❌ Login test failed")
        return
    
    # Test password change invalidates tokens
    test_password_change_invalidates_tokens()
    
    print("\n✅ All tests completed!")


if __name__ == "__main__":
    main()
