"""
Logging utilities for the users app
"""
import logging
from functools import wraps
from django.utils import timezone

logger = logging.getLogger('user')


def log_user_action(action, user_email, performed_by=None, details=None):
    """
    Log a user-related action with consistent formatting
    
    Args:
        action (str): The action being performed
        user_email (str): Email of the user being acted upon
        performed_by (str, optional): Email of the user performing the action
        details (dict, optional): Additional details about the action
    """
    message = f"User action: {action} - Target: {user_email}"
    if performed_by:
        message += f" - Performed by: {performed_by}"
    if details:
        message += f" - Details: {details}"
    
    logger.info(message)


def log_authentication_attempt(username_input, success, user_email=None, reason=None):
    """
    Log authentication attempts
    
    Args:
        username_input (str): The username/email used in the attempt
        success (bool): Whether the authentication was successful
        user_email (str, optional): The actual user email if found
        reason (str, optional): Reason for failure if unsuccessful
    """
    if success:
        logger.info(f"Authentication successful: {user_email} (input: {username_input})")
    else:
        message = f"Authentication failed: {username_input}"
        if user_email:
            message += f" (user: {user_email})"
        if reason:
            message += f" - Reason: {reason}"
        logger.warning(message)


def log_security_event(event_type, user_email=None, ip_address=None, user_agent=None, details=None):
    """
    Log security-related events
    
    Args:
        event_type (str): Type of security event
        user_email (str, optional): Email of the user involved
        ip_address (str, optional): IP address of the request
        user_agent (str, optional): User agent string
        details (dict, optional): Additional details about the event
    """
    message = f"Security event: {event_type}"
    if user_email:
        message += f" - User: {user_email}"
    if ip_address:
        message += f" - IP: {ip_address}"
    if user_agent:
        message += f" - UA: {user_agent[:100]}..."  # Truncate long user agents
    if details:
        message += f" - Details: {details}"
    
    logger.warning(message)


def log_email_operation(operation, recipient_email, success, error=None):
    """
    Log email operations (sending, etc.)
    
    Args:
        operation (str): Type of email operation
        recipient_email (str): Email address of the recipient
        success (bool): Whether the operation was successful
        error (str, optional): Error message if unsuccessful
    """
    if success:
        logger.info(f"Email {operation} successful: {recipient_email}")
    else:
        logger.error(f"Email {operation} failed: {recipient_email} - Error: {error}")


def log_user_status_change(user_email, old_status, new_status, performed_by=None):
    """
    Log user status changes
    
    Args:
        user_email (str): Email of the user whose status changed
        old_status (str): Previous status
        new_status (str): New status
        performed_by (str, optional): Email of the user who made the change
    """
    message = f"User status changed: {user_email} from {old_status} to {new_status}"
    if performed_by:
        message += f" by {performed_by}"
    
    logger.info(message)


def log_api_request(method, endpoint, user_email=None, status_code=None, response_time=None):
    """
    Log API requests for monitoring
    
    Args:
        method (str): HTTP method
        endpoint (str): API endpoint
        user_email (str, optional): Email of the authenticated user
        status_code (int, optional): HTTP status code
        response_time (float, optional): Response time in seconds
    """
    message = f"API {method} {endpoint}"
    if user_email:
        message += f" - User: {user_email}"
    if status_code:
        message += f" - Status: {status_code}"
    if response_time:
        message += f" - Time: {response_time:.3f}s"
    
    logger.debug(message)


def log_function_call(func_name, user_email=None, success=True, error=None, **kwargs):
    """
    Log function calls for debugging
    
    Args:
        func_name (str): Name of the function being called
        user_email (str, optional): Email of the user context
        success (bool): Whether the function call was successful
        error (str, optional): Error message if unsuccessful
        **kwargs: Additional context parameters
    """
    message = f"Function call: {func_name}"
    if user_email:
        message += f" - User: {user_email}"
    if kwargs:
        message += f" - Params: {kwargs}"
    
    if success:
        logger.debug(message)
    else:
        message += f" - Error: {error}"
        logger.error(message)


def log_user_creation(user_email, created_by=None, role=None, status=None):
    """
    Log user creation events
    
    Args:
        user_email (str): Email of the created user
        created_by (str, optional): Email of the user who created the account
        role (str, optional): Role assigned to the user
        status (str, optional): Initial status of the user
    """
    message = f"User created: {user_email}"
    if created_by:
        message += f" by {created_by}"
    if role:
        message += f" - Role: {role}"
    if status:
        message += f" - Status: {status}"
    
    logger.info(message)


def log_user_deletion(user_email, deleted_by=None, soft_delete=True):
    """
    Log user deletion events
    
    Args:
        user_email (str): Email of the deleted user
        deleted_by (str, optional): Email of the user who deleted the account
        soft_delete (bool): Whether it was a soft delete
    """
    delete_type = "soft deleted" if soft_delete else "hard deleted"
    message = f"User {delete_type}: {user_email}"
    if deleted_by:
        message += f" by {deleted_by}"
    
    logger.info(message)


def log_password_operation(operation, user_email, success, error=None):
    """
    Log password-related operations
    
    Args:
        operation (str): Type of password operation (change, reset, etc.)
        user_email (str): Email of the user
        success (bool): Whether the operation was successful
        error (str, optional): Error message if unsuccessful
    """
    if success:
        logger.info(f"Password {operation} successful: {user_email}")
    else:
        logger.warning(f"Password {operation} failed: {user_email} - Error: {error}")


def log_invitation_operation(operation, user_email, success, error=None):
    """
    Log invitation-related operations
    
    Args:
        operation (str): Type of invitation operation (send, accept, etc.)
        user_email (str): Email of the user
        success (bool): Whether the operation was successful
        error (str, optional): Error message if unsuccessful
    """
    if success:
        logger.info(f"Invitation {operation} successful: {user_email}")
    else:
        logger.warning(f"Invitation {operation} failed: {user_email} - Error: {error}")


def log_performance_metric(metric_name, value, unit=None, user_email=None):
    """
    Log performance metrics
    
    Args:
        metric_name (str): Name of the metric
        value (float): Value of the metric
        unit (str, optional): Unit of measurement
        user_email (str, optional): Email of the user context
    """
    message = f"Performance metric: {metric_name} = {value}"
    if unit:
        message += f" {unit}"
    if user_email:
        message += f" - User: {user_email}"
    
    logger.info(message)


def log_error(error_type, error_message, user_email=None, request_info=None):
    """
    Log errors with context
    
    Args:
        error_type (str): Type of error
        error_message (str): Error message
        user_email (str, optional): Email of the user context
        request_info (dict, optional): Request information
    """
    message = f"Error ({error_type}): {error_message}"
    if user_email:
        message += f" - User: {user_email}"
    if request_info:
        message += f" - Request: {request_info}"
    
    logger.error(message) 