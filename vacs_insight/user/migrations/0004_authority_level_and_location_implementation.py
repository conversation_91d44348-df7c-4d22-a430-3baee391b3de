# Generated by Django 4.2.10 on 2025-06-23 17:13

from django.db import migrations, models
import django.db.models.deletion
from django.contrib.auth.hashers import make_password
import uuid
from django.utils import timezone


def create_initial_data(apps, schema_editor):
    Organization = apps.get_model('user', 'Organization')
    UserPosition = apps.get_model('user', 'UserPosition')
    User = apps.get_model('user', 'User')

    # Create default organization
    organization, _ = Organization.objects.get_or_create(
        name="Ministry Of Health Nepal",
        defaults={
            'description': 'Default organization for the health scope system',
            'is_active': True
        }
    )

    # Create default user positions
    admin_position, _ = UserPosition.objects.get_or_create(
        name="Admin",
        defaults={
            'description': 'Administrator with full system access',
            'is_active': True
        }
    )
    manager_position, _ = UserPosition.objects.get_or_create(
        name="Manager",
        defaults={
            'description': 'Manager with limited administrative access',
            'is_active': True
        }
    )
    staff_position, _ = UserPosition.objects.get_or_create(
        name="Staff",
        defaults={
            'description': 'Regular staff member',
            'is_active': True
        }
    )

    # Create admin user with center authority level and default location
    User.objects.get_or_create(
        username='admin',
        defaults={
            'first_name': 'System',
            'last_name': 'Administrator',
            'email': '<EMAIL>',
            'password': make_password('Admin@123!#'),
            'username': 'admin',
            'status': 'active',
            'authority_level': 'center',
            'authority_location_id': 1,  # Default country ID for center level
            'is_active': True,
            'is_staff': True,
            'is_superuser': True,
            'organization': organization,
            'position': admin_position,
        }
    )


def reverse_initial_data(apps, schema_editor):
    Organization = apps.get_model('user', 'Organization')
    UserPosition = apps.get_model('user', 'UserPosition')
    User = apps.get_model('user', 'User')
    User.objects.filter(username='admin').delete()
    UserPosition.objects.filter(name__in=['Admin', 'Manager', 'Staff']).delete()
    Organization.objects.filter(name="Ministry Of Health Nepal").delete()


class Migration(migrations.Migration):

    dependencies = [
        ('user', '0003_create_initial_data'),
    ]

    operations = [
        # Remove old role field and add authority_level field
        migrations.RemoveField(
            model_name='user',
            name='role',
        ),
        migrations.AddField(
            model_name='user',
            name='authority_level',
            field=models.CharField(
                choices=[
                    ('center', 'Central'), 
                    ('province', 'Province'), 
                    ('district', 'District'), 
                    ('municipality', 'Municipality'), 
                    ('ward', 'Ward')
                ], 
                default='ward', 
                max_length=20
            ),
        ),
        
        # Update status field choices
        migrations.AlterField(
            model_name='user',
            name='status',
            field=models.CharField(
                choices=[
                    ('invite', 'Invited'), 
                    ('active', 'Active'), 
                    ('inactive', 'Inactive')
                ], 
                default='invited', 
                max_length=10
            ),
        ),
        
        # Add authority_location_id field
        migrations.AddField(
            model_name='user',
            name='authority_location_id',
            field=models.PositiveIntegerField(
                default=1, 
                help_text='Location ID based on authority level'
            ),
        ),
        
        # Update organization field
        migrations.AlterField(
            model_name='user',
            name='organization',
            field=models.ForeignKey(
                blank=True, 
                default=1, 
                null=True, 
                on_delete=django.db.models.deletion.SET_NULL, 
                to='user.organization'
            ),
        ),
        
        # Add index for performance
        migrations.AddIndex(
            model_name='user',
            index=models.Index(
                fields=['authority_level', 'authority_location_id'], 
                name='users_authori_2a725f_idx'
            ),
        ),
        
        # Create initial data with updated fields
        migrations.RunPython(create_initial_data, reverse_initial_data),
    ] 