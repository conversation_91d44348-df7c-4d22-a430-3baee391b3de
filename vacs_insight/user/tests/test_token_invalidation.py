#!/usr/bin/env python3
"""
Test script for JWT Token Invalidation
Run with: python users/tests/test_token_invalidation.py
"""

import requests
import json
import sys
import os

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

# Configuration
BASE_URL = "http://localhost:8000/api/v1"
LOGIN_URL = f"{BASE_URL}/user/auth/login"
LOGOUT_URL = f"{BASE_URL}/user/auth/logout"
PROTECTED_URL = f"{BASE_URL}/user/profile"
REFRESH_URL = f"{BASE_URL}/token/refresh"

# Test credentials
TEST_USER = {
    "username": "admin",
    "password": "admin123"
}

def print_response(response, title):
    """Print formatted response"""
    print(f"\n{'='*50}")
    print(f"{title}")
    print(f"{'='*50}")
    print(f"Status Code: {response.status_code}")
    print(f"Headers: {dict(response.headers)}")
    try:
        print(f"Response: {json.dumps(response.json(), indent=2)}")
    except:
        print(f"Response: {response.text}")

def login_user():
    """Login and get tokens"""
    print("\n🔐 Logging in to get tokens...")
    
    data = {
        "username": TEST_USER["username"],
        "password": TEST_USER["password"]
    }
    
    response = requests.post(LOGIN_URL, json=data)
    print_response(response, "Login Response")
    
    if response.status_code == 200:
        result = response.json()
        return {
            'access_token': result.get('accessToken'),
            'refresh_token': result.get('refreshToken'),
            'user': result.get('user')
        }
    return None

def test_access_with_token(access_token, test_name):
    """Test accessing protected endpoint with token"""
    print(f"\n🔒 Testing access with token: {test_name}")
    
    headers = {
        "Authorization": f"Bearer {access_token}",
        "Content-Type": "application/json"
    }
    
    response = requests.get(PROTECTED_URL, headers=headers)
    print_response(response, f"Protected Access - {test_name}")
    
    return response.status_code == 200

def test_refresh_token(refresh_token, test_name):
    """Test refresh token functionality"""
    print(f"\n🔄 Testing refresh token: {test_name}")
    
    headers = {
        "Content-Type": "application/json"
    }
    
    data = {
        "refresh": refresh_token
    }
    
    response = requests.post(REFRESH_URL, json=data, headers=headers)
    print_response(response, f"Refresh Token - {test_name}")
    
    if response.status_code == 200:
        result = response.json()
        return result.get('access')
    return None

def test_logout_and_invalidation(access_token, refresh_token):
    """Test logout and token invalidation"""
    print("\n🚪 Testing Logout and Token Invalidation...")
    
    headers = {
        "Authorization": f"Bearer {access_token}",
        "Content-Type": "application/json"
    }
    
    data = {
        "refreshToken": refresh_token
    }
    
    response = requests.post(LOGOUT_URL, json=data, headers=headers)
    print_response(response, "Logout Response")
    
    return response.status_code == 200

def test_multiple_sessions():
    """Test multiple login sessions and individual logout"""
    print("\n🔄 Testing Multiple Login Sessions...")
    
    # Create first session
    print("\n1. Creating first session...")
    tokens1 = login_user()
    if not tokens1:
        print("❌ Failed to create first session")
        return False
    
    # Create second session
    print("\n2. Creating second session...")
    tokens2 = login_user()
    if not tokens2:
        print("❌ Failed to create second session")
        return False
    
    # Test both tokens work
    print("\n3. Testing both tokens work...")
    access1_before = test_access_with_token(tokens1['access_token'], "Session 1 - Before Logout")
    access2_before = test_access_with_token(tokens2['access_token'], "Session 2 - Before Logout")
    
    if not access1_before or not access2_before:
        print("❌ One or both tokens don't work before logout")
        return False
    
    # Logout from first session only
    print("\n4. Logging out from first session only...")
    logout_success = test_logout_and_invalidation(tokens1['access_token'], tokens1['refresh_token'])
    
    if not logout_success:
        print("❌ Failed to logout from first session")
        return False
    
    # Test token invalidation
    print("\n5. Testing token invalidation...")
    access1_after = test_access_with_token(tokens1['access_token'], "Session 1 - After Logout")
    access2_after = test_access_with_token(tokens2['access_token'], "Session 2 - After Logout")
    
    # Test refresh token invalidation
    print("\n6. Testing refresh token invalidation...")
    refresh1_after = test_refresh_token(tokens1['refresh_token'], "Session 1 - After Logout")
    refresh2_after = test_refresh_token(tokens2['refresh_token'], "Session 2 - Still Valid")
    
    # Summary
    print("\n" + "="*60)
    print("MULTIPLE SESSION TEST SUMMARY")
    print("="*60)
    print(f"Session 1 - Access before logout: {'✅' if access1_before else '❌'}")
    print(f"Session 1 - Access after logout: {'❌' if not access1_after else '⚠️'}")
    print(f"Session 1 - Refresh after logout: {'❌' if not refresh1_after else '⚠️'}")
    print(f"Session 2 - Access before logout: {'✅' if access2_before else '❌'}")
    print(f"Session 2 - Access after logout: {'✅' if access2_after else '❌'}")
    print(f"Session 2 - Refresh after logout: {'✅' if refresh2_after else '❌'}")
    
    # Check if test passed
    if (access1_before and not access1_after and not refresh1_after and 
        access2_before and access2_after and refresh2_after):
        print("\n✅ Multiple session test PASSED!")
        return True
    else:
        print("\n❌ Multiple session test FAILED!")
        return False

def test_single_session_invalidation():
    """Test single session logout and token invalidation"""
    print("\n🔄 Testing Single Session Invalidation...")
    
    # Login
    tokens = login_user()
    if not tokens:
        print("❌ Failed to login")
        return False
    
    # Test access before logout
    access_before = test_access_with_token(tokens['access_token'], "Before Logout")
    if not access_before:
        print("❌ Cannot access protected endpoint before logout")
        return False
    
    # Test refresh before logout
    refresh_before = test_refresh_token(tokens['refresh_token'], "Before Logout")
    if not refresh_before:
        print("❌ Cannot refresh token before logout")
        return False
    
    # Logout
    logout_success = test_logout_and_invalidation(tokens['access_token'], tokens['refresh_token'])
    if not logout_success:
        print("❌ Failed to logout")
        return False
    
    # Test access after logout
    access_after = test_access_with_token(tokens['access_token'], "After Logout")
    
    # Test refresh after logout
    refresh_after = test_refresh_token(tokens['refresh_token'], "After Logout")
    
    # Summary
    print("\n" + "="*60)
    print("SINGLE SESSION TEST SUMMARY")
    print("="*60)
    print(f"Access before logout: {'✅' if access_before else '❌'}")
    print(f"Refresh before logout: {'✅' if refresh_before else '❌'}")
    print(f"Logout successful: {'✅' if logout_success else '❌'}")
    print(f"Access after logout: {'❌' if not access_after else '⚠️'}")
    print(f"Refresh after logout: {'❌' if not refresh_after else '⚠️'}")
    
    # Check if test passed
    if access_before and refresh_before and logout_success and not access_after and not refresh_after:
        print("\n✅ Single session test PASSED!")
        return True
    else:
        print("\n❌ Single session test FAILED!")
        return False

def main():
    """Main test function"""
    print("🧪 Starting JWT Token Invalidation Tests...")
    print(f"Base URL: {BASE_URL}")
    
    # Test single session invalidation
    print("\n" + "="*60)
    print("SINGLE SESSION INVALIDATION TEST")
    print("="*60)
    single_success = test_single_session_invalidation()
    
    # Test multiple sessions
    print("\n" + "="*60)
    print("MULTIPLE SESSION INVALIDATION TEST")
    print("="*60)
    multiple_success = test_multiple_sessions()
    
    # Final summary
    print("\n" + "="*60)
    print("FINAL TEST SUMMARY")
    print("="*60)
    print(f"Single session test: {'✅ PASSED' if single_success else '❌ FAILED'}")
    print(f"Multiple session test: {'✅ PASSED' if multiple_success else '❌ FAILED'}")
    
    if single_success and multiple_success:
        print("\n🎉 All token invalidation tests PASSED!")
    else:
        print("\n⚠️ Some token invalidation tests FAILED!")
    
    print("\n✅ All tests completed!")

if __name__ == "__main__":
    main() 