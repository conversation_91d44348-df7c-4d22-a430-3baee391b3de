# User API Tests

This directory contains test scripts for the User API endpoints.

## API URL Structure

The User API uses the following base URL structure:
- **Base URL**: `/api/v1/user/`
- **Authentication**: `/api/v1/user/auth/login` and `/api/v1/user/auth/logout`
- **User Management**: `/api/v1/user/` (list, create), `/api/v1/user/{id}/` (detail, update, delete)
- **Profile**: `/api/v1/user/profile/`
- **Statistics**: `/api/v1/user/stats/`

## Test Files

### 1. `test_user_apis.py`
Comprehensive test script for all User API endpoints including:
- Authentication (login with email/username)
- User listing and details
- User profile management
- User statistics
- Logout functionality

### 2. `test_token_invalidation.py`
Specialized test script for token management and session handling:
- Login and logout with token invalidation
- Multiple session management
- Token refresh functionality
- Session isolation testing

## Running the Tests

### Prerequisites
1. Make sure the Django server is running:
   ```bash
   python manage.py runserver 0.0.0.0:8000
   ```

2. Ensure you have the required dependencies:
   ```bash
   pip install requests
   ```

### Running Tests

#### Basic API Tests
```bash
python users/tests/test_user_apis.py
```

#### Token Invalidation Tests
```bash
python users/tests/test_token_invalidation.py
```

## Default Test Credentials

The tests use the default admin user:
- **Email**: `<EMAIL>`
- **Username**: `admin`
- **Password**: `Admin@123!#`

## Test Output

The tests provide detailed output showing:
- ✅ Success indicators for passed tests
- ❌ Error indicators for failed tests
- Status codes and response data
- Token information (truncated for security)

## Notes

- These are integration tests that require a running Django server
- They test the actual API endpoints, not unit tests
- Token invalidation tests may show access tokens as still valid after logout (this is expected behavior with SimpleJWT)
- The tests use real HTTP requests to the API endpoints

## Troubleshooting

If tests fail:
1. Ensure the Django server is running on port 8000
2. Check that the database has the default admin user
3. Verify that all migrations have been applied
4. Check the server logs for any errors 