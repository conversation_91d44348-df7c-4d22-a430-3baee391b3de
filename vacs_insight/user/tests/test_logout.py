#!/usr/bin/env python3
"""
Test script for Logout API with JWT token blacklisting
Run with: python users/tests/test_logout.py
"""

import requests
import json
import sys
import os

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

# Configuration
BASE_URL = "http://localhost:8000/api/v1"
LOGIN_URL = f"{BASE_URL}/user/auth/login"
LOGOUT_URL = f"{BASE_URL}/user/auth/logout"
PROTECTED_URL = f"{BASE_URL}/user/profile"

# Test credentials
TEST_USER = {
    "username": "admin",
    "password": "admin123"
}

def print_response(response, title):
    """Print formatted response"""
    print(f"\n{'='*50}")
    print(f"{title}")
    print(f"{'='*50}")
    print(f"Status Code: {response.status_code}")
    print(f"Headers: {dict(response.headers)}")
    try:
        print(f"Response: {json.dumps(response.json(), indent=2)}")
    except:
        print(f"Response: {response.text}")

def login_user():
    """Login and get tokens"""
    print("\n🔐 Logging in to get tokens...")
    
    data = {
        "username": TEST_USER["username"],
        "password": TEST_USER["password"]
    }
    
    response = requests.post(LOGIN_URL, json=data)
    print_response(response, "Login Response")
    
    if response.status_code == 200:
        result = response.json()
        return {
            'access_token': result.get('accessToken'),
            'refresh_token': result.get('refreshToken'),
            'user': result.get('user')
        }
    return None

def test_access_with_token(access_token, test_name):
    """Test accessing protected endpoint with token"""
    print(f"\n🔒 Testing access with token: {test_name}")
    
    headers = {
        "Authorization": f"Bearer {access_token}",
        "Content-Type": "application/json"
    }
    
    response = requests.get(PROTECTED_URL, headers=headers)
    print_response(response, f"Protected Access - {test_name}")
    
    return response.status_code == 200

def test_logout_with_refresh_token(access_token, refresh_token):
    """Test logout with refresh token"""
    print("\n🚪 Testing Logout with Refresh Token...")
    
    headers = {
        "Authorization": f"Bearer {access_token}",
        "Content-Type": "application/json"
    }
    
    data = {
        "refreshToken": refresh_token
    }
    
    response = requests.post(LOGOUT_URL, json=data, headers=headers)
    print_response(response, "Logout with Refresh Token")
    
    return response.status_code == 200

def test_logout_without_refresh_token(access_token):
    """Test logout without refresh token"""
    print("\n🚪 Testing Logout without Refresh Token...")
    
    headers = {
        "Authorization": f"Bearer {access_token}",
        "Content-Type": "application/json"
    }
    
    response = requests.post(LOGOUT_URL, headers=headers)
    print_response(response, "Logout without Refresh Token")
    
    return response.status_code == 200

def test_logout_with_invalid_refresh_token(access_token):
    """Test logout with invalid refresh token"""
    print("\n🚪 Testing Logout with Invalid Refresh Token...")
    
    headers = {
        "Authorization": f"Bearer {access_token}",
        "Content-Type": "application/json"
    }
    
    data = {
        "refreshToken": "invalid-refresh-token-12345"
    }
    
    response = requests.post(LOGOUT_URL, json=data, headers=headers)
    print_response(response, "Logout with Invalid Refresh Token")
    
    return response.status_code == 400

def test_unauthorized_logout():
    """Test logout without authentication"""
    print("\n🚪 Testing Logout without Authentication...")
    
    headers = {
        "Content-Type": "application/json"
    }
    
    data = {
        "refreshToken": "dummy-refresh-token"
    }
    
    response = requests.post(LOGOUT_URL, json=data, headers=headers)
    print_response(response, "Logout without Authentication")
    
    return response.status_code == 401

def main():
    """Main test function"""
    print("🧪 Starting Logout API Tests...")
    print(f"Base URL: {BASE_URL}")
    
    # Test unauthorized logout first
    test_unauthorized_logout()
    
    # Login to get tokens
    tokens = login_user()
    if not tokens:
        print("\n❌ Login failed. Cannot proceed with logout tests.")
        return
    
    access_token = tokens['access_token']
    refresh_token = tokens['refresh_token']
    
    print(f"\n✅ Login successful!")
    print(f"Access Token: {access_token[:50]}...")
    print(f"Refresh Token: {refresh_token[:50]}...")
    
    # Test access before logout
    print("\n" + "="*60)
    print("TESTING ACCESS BEFORE LOGOUT")
    print("="*60)
    
    access_before = test_access_with_token(access_token, "Before Logout")
    
    if not access_before:
        print("\n❌ Cannot access protected endpoint before logout. Stopping tests.")
        return
    
    # Test different logout scenarios
    print("\n" + "="*60)
    print("TESTING LOGOUT SCENARIOS")
    print("="*60)
    
    # Test logout with invalid refresh token
    test_logout_with_invalid_refresh_token(access_token)
    
    # Test logout without refresh token
    test_logout_without_refresh_token(access_token)
    
    # Test logout with valid refresh token
    logout_success = test_logout_with_refresh_token(access_token, refresh_token)
    
    # Test access after logout
    print("\n" + "="*60)
    print("TESTING ACCESS AFTER LOGOUT")
    print("="*60)
    
    access_after = test_access_with_token(access_token, "After Logout")
    
    # Summary
    print("\n" + "="*60)
    print("TEST SUMMARY")
    print("="*60)
    print(f"Access before logout: {'✅' if access_before else '❌'}")
    print(f"Logout successful: {'✅' if logout_success else '❌'}")
    print(f"Access after logout: {'❌' if not access_after else '⚠️ (Token still works)'}")
    
    if access_before and not access_after:
        print("\n✅ Logout test PASSED - Token was properly invalidated!")
    elif access_before and access_after:
        print("\n⚠️ Logout test PARTIAL - Token still works after logout")
    else:
        print("\n❌ Logout test FAILED - Unexpected behavior")
    
    print("\n✅ All logout tests completed!")

if __name__ == "__main__":
    main()
