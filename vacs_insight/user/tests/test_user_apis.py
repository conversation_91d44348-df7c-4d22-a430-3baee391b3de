#!/usr/bin/env python3
"""
Test script for User APIs
Run with: python users/tests/test_user_apis.py
"""

import requests
import json
import sys
import os

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__)))))

# Configuration
BASE_URL = "http://localhost:8000/api/v1"
LOGIN_URL = f"{BASE_URL}/user/auth/login"
LOGOUT_URL = f"{BASE_URL}/user/auth/logout"
USERS_URL = f"{BASE_URL}/user"
PROFILE_URL = f"{BASE_URL}/user/profile"
STATS_URL = f"{BASE_URL}/user/stats"
POSITION_URL = f"{BASE_URL}/user/position"
POSITIONS_URL = f"{BASE_URL}/user/positions"

# Test credentials
TEST_USER = {
    "username": "admin",
    "password": "admin123"
}

def print_response(response, title):
    """Print formatted response"""
    print(f"\n{'='*50}")
    print(f"{title}")
    print(f"{'='*50}")
    print(f"Status Code: {response.status_code}")
    print(f"Headers: {dict(response.headers)}")
    try:
        print(f"Response: {json.dumps(response.json(), indent=2)}")
    except:
        print(f"Response: {response.text}")

def test_login():
    """Test login API"""
    print("\n🔐 Testing Login API...")
    
    # Test with username
    data = {
        "username": TEST_USER["username"],
        "password": TEST_USER["password"]
    }
    
    response = requests.post(LOGIN_URL, json=data)
    print_response(response, "Login with Username")
    
    if response.status_code == 200:
        return response.json().get('accessToken')
    return None

def test_login_with_email():
    """Test login with email"""
    print("\n📧 Testing Login with Email...")
    
    data = {
        "username": "<EMAIL>",
        "password": TEST_USER["password"]
    }
    
    response = requests.post(LOGIN_URL, json=data)
    print_response(response, "Login with Email")

def test_invalid_login():
    """Test invalid login"""
    print("\n❌ Testing Invalid Login...")
    
    data = {
        "username": "invalid_user",
        "password": "wrong_password"
    }
    
    response = requests.post(LOGIN_URL, json=data)
    print_response(response, "Invalid Login")

def test_users_list(access_token):
    """Test users list API"""
    print("\n👥 Testing Users List API...")
    
    headers = {
        "Authorization": f"Bearer {access_token}",
        "Content-Type": "application/json"
    }
    
    response = requests.get(USERS_URL, headers=headers)
    print_response(response, "Users List")

def test_users_list_with_pagination_and_search(access_token):
    """Test users list API with pagination and search parameters"""
    print("\n🔍 Testing Users List with Pagination and Search...")
    
    headers = {
        "Authorization": f"Bearer {access_token}",
        "Content-Type": "application/json"
    }
    
    # Test pagination parameters
    params = {
        "page_number": 1,
        "page_size": 5
    }
    
    response = requests.get(USERS_URL, headers=headers, params=params)
    print_response(response, "Users List with Pagination (page_number=1, page_size=5)")
    
    # Test search functionality
    params = {
        "search": "admin",
        "page_number": 1,
        "page_size": 10
    }
    
    response = requests.get(USERS_URL, headers=headers, params=params)
    print_response(response, "Users List with Search (search=admin)")
    
    # Test search with email
    params = {
        "search": "<EMAIL>",
        "page_number": 1,
        "page_size": 10
    }
    
    response = requests.get(USERS_URL, headers=headers, params=params)
    print_response(response, "Users List with Email Search")
    
    # Test filtering with authority level
    params = {
        "authority_level": "center",
        "page_number": 1,
        "page_size": 10
    }
    
    response = requests.get(USERS_URL, headers=headers, params=params)
    print_response(response, "Users List with Authority Level Filter")
    
    # Test sorting functionality
    params = {
        "sort_by": "created_at",
        "sort_order": "desc",
        "page_number": 1,
        "page_size": 10
    }
    
    response = requests.get(USERS_URL, headers=headers, params=params)
    print_response(response, "Users List with Sorting (created_at desc)")
    
    # Test sorting by first name ascending
    params = {
        "sort_by": "first_name",
        "sort_order": "asc",
        "page_number": 1,
        "page_size": 10
    }
    
    response = requests.get(USERS_URL, headers=headers, params=params)
    print_response(response, "Users List with Sorting (first_name asc)")
    
    # Test combined search, filter, and sort
    params = {
        "search": "admin",
        "authority_level": "center",
        "sort_by": "last_login",
        "sort_order": "desc",
        "page_number": 1,
        "page_size": 10
    }
    
    response = requests.get(USERS_URL, headers=headers, params=params)
    print_response(response, "Users List with Combined Search, Filter, and Sort")

def test_position_api(access_token):
    """Test position API endpoints"""
    print("\n👔 Testing Position API...")
    
    headers = {
        "Authorization": f"Bearer {access_token}",
        "Content-Type": "application/json"
    }
    
    # Test singular endpoint
    print("\n--- Testing /position endpoint (singular) ---")
    response = requests.get(POSITION_URL, headers=headers)
    print_response(response, "Position List (singular endpoint)")
    
    # Test plural endpoint
    print("\n--- Testing /positions endpoint (plural) ---")
    response = requests.get(POSITIONS_URL, headers=headers)
    print_response(response, "Position List (plural endpoint)")
    
    # Test with pagination
    params = {
        "page_number": 1,
        "page_size": 5
    }
    response = requests.get(POSITIONS_URL, headers=headers, params=params)
    print_response(response, "Position List with Pagination")
    
    # Test with search
    params = {
        "search": "admin",
        "page_number": 1,
        "page_size": 10
    }
    response = requests.get(POSITIONS_URL, headers=headers, params=params)
    print_response(response, "Position List with Search (admin)")
    
    # Test with active filter
    params = {
        "is_active": "true",
        "page_number": 1,
        "page_size": 10
    }
    response = requests.get(POSITIONS_URL, headers=headers, params=params)
    print_response(response, "Position List with Active Filter")
    
    # Test with sorting
    params = {
        "sort_by": "name",
        "sort_order": "asc",
        "page_number": 1,
        "page_size": 10
    }
    response = requests.get(POSITIONS_URL, headers=headers, params=params)
    print_response(response, "Position List with Sorting (name asc)")
    
    # Test with sorting by description
    params = {
        "sort_by": "description",
        "sort_order": "desc",
        "page_number": 1,
        "page_size": 10
    }
    response = requests.get(POSITIONS_URL, headers=headers, params=params)
    print_response(response, "Position List with Sorting (description desc)")
    
    # Test combined parameters
    params = {
        "search": "manager",
        "is_active": "true",
        "sort_by": "name",
        "sort_order": "asc",
        "page_number": 1,
        "page_size": 5
    }
    response = requests.get(POSITIONS_URL, headers=headers, params=params)
    print_response(response, "Position List with Combined Parameters")

def test_position_api_errors(access_token):
    """Test position API error handling"""
    print("\n❌ Testing Position API Error Handling...")
    
    headers = {
        "Authorization": f"Bearer {access_token}",
        "Content-Type": "application/json"
    }
    
    # Test invalid page number
    params = {
        "page_number": -1,
        "page_size": 10
    }
    response = requests.get(POSITIONS_URL, headers=headers, params=params)
    print_response(response, "Position List with Invalid Page Number")
    
    # Test invalid page size
    params = {
        "page_number": 1,
        "page_size": 150  # Above max of 100
    }
    response = requests.get(POSITIONS_URL, headers=headers, params=params)
    print_response(response, "Position List with Invalid Page Size")
    
    # Test invalid sort field
    params = {
        "sort_by": "invalid_field",
        "sort_order": "asc"
    }
    response = requests.get(POSITIONS_URL, headers=headers, params=params)
    print_response(response, "Position List with Invalid Sort Field")
    
    # Test invalid sort order
    params = {
        "sort_by": "name",
        "sort_order": "invalid"
    }
    response = requests.get(POSITIONS_URL, headers=headers, params=params)
    print_response(response, "Position List with Invalid Sort Order")

def test_user_profile(access_token):
    """Test user profile API"""
    print("\n👤 Testing User Profile API...")
    
    headers = {
        "Authorization": f"Bearer {access_token}",
        "Content-Type": "application/json"
    }
    
    response = requests.get(PROFILE_URL, headers=headers)
    print_response(response, "User Profile")

def test_user_stats(access_token):
    """Test user statistics API"""
    print("\n📊 Testing User Statistics API...")
    
    headers = {
        "Authorization": f"Bearer {access_token}",
        "Content-Type": "application/json"
    }
    
    response = requests.get(STATS_URL, headers=headers)
    print_response(response, "User Statistics")

def test_create_user(access_token):
    """Test create user API"""
    print("\n➕ Testing Create User API...")
    
    headers = {
        "Authorization": f"Bearer {access_token}",
        "Content-Type": "application/json"
    }
    
    data = {
        "username": "testuser123",
        "email": "<EMAIL>",
        "first_name": "Test",
        "last_name": "User",
        "role": "ward",
        "phone": "1234567890"
    }
    
    response = requests.post(USERS_URL, json=data, headers=headers)
    print_response(response, "Create User")
    
    if response.status_code == 201:
        return response.json().get('id')
    return None

def test_update_user(access_token, user_id):
    """Test update user API"""
    if not user_id:
        print("\n⚠️ Skipping Update User Test (no user created)")
        return
        
    print("\n✏️ Testing Update User API...")
    
    headers = {
        "Authorization": f"Bearer {access_token}",
        "Content-Type": "application/json"
    }
    
    data = {
        "first_name": "Updated",
        "last_name": "Name",
        "phone": "9876543210"
    }
    
    response = requests.patch(f"{USERS_URL}/{user_id}", json=data, headers=headers)
    print_response(response, "Update User")

def test_user_actions(access_token, user_id):
    """Test user action APIs"""
    if not user_id:
        print("\n⚠️ Skipping User Actions Test (no user created)")
        return
        
    print("\n⚡ Testing User Actions...")
    
    headers = {
        "Authorization": f"Bearer {access_token}",
        "Content-Type": "application/json"
    }
    
    # Test deactivate
    response = requests.post(f"{USERS_URL}/{user_id}/deactivate", headers=headers)
    print_response(response, "Deactivate User")
    
    # Test activate
    response = requests.post(f"{USERS_URL}/{user_id}/activate", headers=headers)
    print_response(response, "Activate User")
    
    # Test disable
    data = {"disable_reason": "Testing disable functionality"}
    response = requests.post(f"{USERS_URL}/{user_id}/disable", json=data, headers=headers)
    print_response(response, "Disable User")
    
    # Test enable
    data = {"enable_reason": "Testing enable functionality"}
    response = requests.post(f"{USERS_URL}/{user_id}/enable", json=data, headers=headers)
    print_response(response, "Enable User")

def test_logout(access_token):
    """Test logout API"""
    print("\n🚪 Testing Logout API...")
    
    headers = {
        "Authorization": f"Bearer {access_token}",
        "Content-Type": "application/json"
    }
    
    data = {
        "refreshToken": "dummy-refresh-token"  # In real scenario, this would be the actual refresh token
    }
    
    response = requests.post(LOGOUT_URL, json=data, headers=headers)
    print_response(response, "Logout")

def test_unauthorized_access():
    """Test unauthorized access"""
    print("\n🚫 Testing Unauthorized Access...")
    
    headers = {
        "Content-Type": "application/json"
    }
    
    response = requests.get(USERS_URL, headers=headers)
    print_response(response, "Unauthorized Access")

def test_password_change_invalidates_tokens():
    """Test that password change invalidates all existing tokens"""
    print("\n🔐 Testing Password Change Token Invalidation...")
    
    # First login to get tokens
    login_data = {
        "username": "<EMAIL>",
        "password": TEST_USER["password"]
    }
    
    login_response = requests.post(LOGIN_URL, json=login_data)
    print_response(login_response, "Login for Token Invalidation Test")
    
    if login_response.status_code != 200:
        print("❌ Failed to login for token invalidation test")
        return
    
    access_token = login_response.json().get("accessToken")
    refresh_token = login_response.json().get("refreshToken")
    
    # Test that we can access a protected endpoint with the token
    headers = {"Authorization": f"Bearer {access_token}"}
    profile_response = requests.get(f"{BASE_URL}/profile", headers=headers)
    print_response(profile_response, "Profile Access Before Password Change")
    
    if profile_response.status_code != 200:
        print("❌ Failed to access profile before password change")
        return
    
    # Change password
    password_change_data = {
        "old_password": TEST_USER["password"],
        "new_password": "NewSecurePassword123!",
        "confirm_password": "NewSecurePassword123!"
    }
    
    password_change_response = requests.post(
        f"{BASE_URL}/change_password", 
        json=password_change_data, 
        headers=headers
    )
    print_response(password_change_response, "Password Change")
    
    if password_change_response.status_code != 200:
        print("❌ Failed to change password")
        return
    
    # Try to access profile with the old token - should fail
    profile_response_after = requests.get(f"{BASE_URL}/profile", headers=headers)
    print_response(profile_response_after, "Profile Access After Password Change (Should Fail)")
    
    if profile_response_after.status_code == 401:
        print("✅ Token successfully invalidated after password change")
    else:
        print("❌ Token was not invalidated after password change")
    
    # Try to refresh the token - should also fail
    refresh_data = {"refresh": refresh_token}
    refresh_response = requests.post(f"{BASE_URL}/auth/refresh", json=refresh_data)
    print_response(refresh_response, "Token Refresh After Password Change (Should Fail)")
    
    if refresh_response.status_code == 401:
        print("✅ Refresh token successfully invalidated after password change")
    else:
        print("❌ Refresh token was not invalidated after password change")
    
    # Login with new password to verify it works
    new_login_data = {
        "username": "<EMAIL>",
        "password": "NewSecurePassword123!"
    }
    
    new_login_response = requests.post(LOGIN_URL, json=new_login_data)
    print_response(new_login_response, "Login with New Password")
    
    if new_login_response.status_code == 200:
        print("✅ Successfully logged in with new password")
        
        # Change password back to original for other tests
        new_access_token = new_login_response.json().get("accessToken")
        new_headers = {"Authorization": f"Bearer {new_access_token}"}
        
        revert_password_data = {
            "old_password": "NewSecurePassword123!",
            "new_password": TEST_USER["password"],
            "confirm_password": TEST_USER["password"]
        }
        
        revert_response = requests.post(
            f"{BASE_URL}/change_password", 
            json=revert_password_data, 
            headers=new_headers
        )
        print_response(revert_response, "Revert Password to Original")
        
        if revert_response.status_code == 200:
            print("✅ Successfully reverted password to original")
        else:
            print("❌ Failed to revert password to original")
    else:
        print("❌ Failed to login with new password")

def main():
    """Main test function"""
    print("🧪 Starting User API Tests...")
    print(f"Base URL: {BASE_URL}")
    
    # Test unauthorized access first
    test_unauthorized_access()
    
    # Test login scenarios
    test_invalid_login()
    test_login_with_email()
    access_token = test_login()
    
    if not access_token:
        print("\n❌ Login failed. Cannot proceed with authenticated tests.")
        return
    
    # Test authenticated endpoints
    test_users_list(access_token)
    test_users_list_with_pagination_and_search(access_token)
    test_user_profile(access_token)
    test_user_stats(access_token)
    
    # Test position API
    test_position_api(access_token)
    test_position_api_errors(access_token)
    
    # Test user management
    user_id = test_create_user(access_token)
    test_update_user(access_token, user_id)
    test_user_actions(access_token, user_id)
    
    # Test logout
    test_logout(access_token)
    
    # Test password change invalidates tokens
    test_password_change_invalidates_tokens()
    
    print("\n✅ All tests completed!")

if __name__ == "__main__":
    main() 