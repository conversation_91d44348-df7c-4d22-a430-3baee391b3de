#!/usr/bin/env python3
"""
Django tests for Position API
Run with: python manage.py test user.tests.test_position_api
"""

from django.test import TestCase
from django.urls import reverse
from rest_framework.test import APITestCase, APIClient
from rest_framework import status
from django.contrib.auth import get_user_model
from user.models import UserPosition, Organization
from user.serializers import UserPositionSerializer

User = get_user_model()

class PositionAPITestCase(APITestCase):
    """Test cases for Position API endpoints"""
    
    def setUp(self):
        """Set up test data"""
        # Create test organization
        self.organization = Organization.objects.create(
            name="Test Organization",
            description="Test organization for testing"
        )
        
        # Get or create test positions with unique names
        self.admin_position, _ = UserPosition.objects.get_or_create(
            name="Test Admin",
            defaults={
                "description": "Test Administrator with full system access",
                "is_active": True
            }
        )
        
        self.manager_position, _ = UserPosition.objects.get_or_create(
            name="Test Manager",
            defaults={
                "description": "Test Manager with limited administrative access",
                "is_active": True
            }
        )
        
        self.staff_position, _ = UserPosition.objects.get_or_create(
            name="Test Staff",
            defaults={
                "description": "Test Regular staff member",
                "is_active": True
            }
        )
        
        self.inactive_position, _ = UserPosition.objects.get_or_create(
            name="Test Inactive Position",
            defaults={
                "description": "This test position is inactive",
                "is_active": False
            }
        )
        
        # Create test user
        self.user = User.objects.create_user(
            username="testuser",
            email="<EMAIL>",
            password="testpass123",
            first_name="Test",
            last_name="User",
            authority_level="center",
            organization=self.organization,
            position=self.admin_position
        )
        
        # Set up API client
        self.client = APIClient()
        self.client.force_authenticate(user=self.user)
        
        # Define URLs
        self.position_url = reverse('user-position-list')
        self.positions_url = reverse('user-positions-list')
    
    def test_position_list_singular_endpoint(self):
        """Test position list using singular endpoint (/position)"""
        response = self.client.get(self.position_url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('status', response.data)
        self.assertEqual(response.data['status'], 'success')
        self.assertIn('data', response.data)
        self.assertIn('items', response.data['data'])
        
        # Check that all active positions are returned
        items = response.data['data']['items']
        self.assertEqual(len(items), 3)  # 3 active positions
        
        # Check position names
        position_names = [item['name'] for item in items]
        self.assertIn('Test Admin', position_names)
        self.assertIn('Test Manager', position_names)
        self.assertIn('Test Staff', position_names)
        self.assertNotIn('Test Inactive Position', position_names)
    
    def test_position_list_plural_endpoint(self):
        """Test position list using plural endpoint (/positions)"""
        response = self.client.get(self.positions_url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('status', response.data)
        self.assertEqual(response.data['status'], 'success')
        self.assertIn('data', response.data)
        self.assertIn('items', response.data['data'])
        
        # Check that all active positions are returned
        items = response.data['data']['items']
        self.assertEqual(len(items), 3)  # 3 active positions
        
        # Check position names
        position_names = [item['name'] for item in items]
        self.assertIn('Test Admin', position_names)
        self.assertIn('Test Manager', position_names)
        self.assertIn('Test Staff', position_names)
        self.assertNotIn('Test Inactive Position', position_names)
    
    def test_position_list_with_pagination(self):
        """Test position list with pagination parameters"""
        params = {
            'page_number': 1,
            'page_size': 2
        }
        
        response = self.client.get(self.positions_url, params)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('data', response.data)
        data = response.data['data']
        
        # Check pagination fields
        self.assertIn('total_count', data)
        self.assertIn('page_number', data)
        self.assertIn('page_size', data)
        self.assertIn('total_pages', data)
        
        self.assertEqual(data['total_count'], 3)
        self.assertEqual(data['page_number'], 1)
        self.assertEqual(data['page_size'], 2)
        self.assertEqual(data['total_pages'], 2)
        
        # Check items count
        self.assertEqual(len(data['items']), 2)
    
    def test_position_list_with_search(self):
        """Test position list with search functionality"""
        params = {
            'search': 'admin',
            'page_number': 1,
            'page_size': 10
        }
        
        response = self.client.get(self.positions_url, params)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.data['data']
        
        # Should find Test Admin position
        items = data['items']
        self.assertEqual(len(items), 1)
        self.assertEqual(items[0]['name'], 'Test Admin')
    
    def test_position_list_with_search_description(self):
        """Test position list with search in description"""
        params = {
            'search': 'limited',
            'page_number': 1,
            'page_size': 10
        }
        
        response = self.client.get(self.positions_url, params)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.data['data']
        
        # Should find Test Manager position (has 'limited' in description)
        items = data['items']
        self.assertEqual(len(items), 1)
        self.assertEqual(items[0]['name'], 'Test Manager')
    
    def test_position_list_with_active_filter(self):
        """Test position list with active status filter"""
        params = {
            'is_active': 'true',
            'page_number': 1,
            'page_size': 10
        }
        
        response = self.client.get(self.positions_url, params)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.data['data']
        
        # Should only return active positions
        items = data['items']
        self.assertEqual(len(items), 3)
        
        for item in items:
            self.assertTrue(item['is_active'])
    
    def test_position_list_with_inactive_filter(self):
        """Test position list with inactive status filter"""
        params = {
            'is_active': 'false',
            'page_number': 1,
            'page_size': 10
        }
        
        response = self.client.get(self.positions_url, params)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.data['data']
        
        # Should only return inactive positions
        items = data['items']
        self.assertEqual(len(items), 1)
        self.assertEqual(items[0]['name'], 'Test Inactive Position')
        self.assertFalse(items[0]['is_active'])
    
    def test_position_list_with_sorting(self):
        """Test position list with sorting"""
        params = {
            'sort_by': 'name',
            'sort_order': 'asc',
            'page_number': 1,
            'page_size': 10
        }
        
        response = self.client.get(self.positions_url, params)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.data['data']
        
        # Check sorting (alphabetical order)
        items = data['items']
        self.assertEqual(len(items), 3)
        self.assertEqual(items[0]['name'], 'Test Admin')
        self.assertEqual(items[1]['name'], 'Test Manager')
        self.assertEqual(items[2]['name'], 'Test Staff')
    
    def test_position_list_with_sorting_desc(self):
        """Test position list with descending sorting"""
        params = {
            'sort_by': 'name',
            'sort_order': 'desc',
            'page_number': 1,
            'page_size': 10
        }
        
        response = self.client.get(self.positions_url, params)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.data['data']
        
        # Check reverse sorting
        items = data['items']
        self.assertEqual(len(items), 3)
        self.assertEqual(items[0]['name'], 'Test Staff')
        self.assertEqual(items[1]['name'], 'Test Manager')
        self.assertEqual(items[2]['name'], 'Test Admin')
    
    def test_position_list_with_sorting_description(self):
        """Test position list with sorting by description"""
        params = {
            'sort_by': 'description',
            'sort_order': 'asc',
            'page_number': 1,
            'page_size': 10
        }
        
        response = self.client.get(self.positions_url, params)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.data['data']
        
        # Check sorting by description
        items = data['items']
        self.assertEqual(len(items), 3)
        # Test Admin should come first alphabetically in description
        self.assertEqual(items[0]['name'], 'Test Admin')
    
    def test_position_list_combined_parameters(self):
        """Test position list with combined search, filter, and sort"""
        params = {
            'search': 'admin',
            'is_active': 'true',
            'sort_by': 'name',
            'sort_order': 'asc',
            'page_number': 1,
            'page_size': 5
        }
        
        response = self.client.get(self.positions_url, params)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.data['data']
        
        # Should find Test Admin position
        items = data['items']
        self.assertEqual(len(items), 1)
        self.assertEqual(items[0]['name'], 'Test Admin')
        self.assertTrue(items[0]['is_active'])
    
    def test_position_list_error_handling(self):
        """Test position list error handling"""
        # Test invalid page number
        params = {'page_number': -1, 'page_size': 10}
        response = self.client.get(self.positions_url, params)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        
        # Test invalid page size
        params = {'page_number': 1, 'page_size': 150}
        response = self.client.get(self.positions_url, params)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        
        # Test invalid sort field
        params = {'sort_by': 'invalid_field', 'sort_order': 'asc'}
        response = self.client.get(self.positions_url, params)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        
        # Test invalid sort order
        params = {'sort_by': 'name', 'sort_order': 'invalid'}
        response = self.client.get(self.positions_url, params)
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
    
    def test_position_detail(self):
        """Test position detail endpoint"""
        position = self.admin_position
        
        # Test singular endpoint
        url = reverse('user-position-detail', args=[position.id])
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['name'], 'Test Admin')
        self.assertEqual(response.data['description'], 'Test Administrator with full system access')
        self.assertTrue(response.data['is_active'])
        
        # Test plural endpoint
        url = reverse('user-positions-detail', args=[position.id])
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['name'], 'Test Admin')
    
    def test_position_list_unauthorized(self):
        """Test position list without authentication"""
        self.client.force_authenticate(user=None)
        
        response = self.client.get(self.positions_url)
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
    
    def test_position_response_structure(self):
        """Test that position response has correct structure"""
        response = self.client.get(self.positions_url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        
        # Check response structure
        self.assertIn('status', response.data)
        self.assertIn('message', response.data)
        self.assertIn('data', response.data)
        
        data = response.data['data']
        self.assertIn('type', data)
        self.assertIn('items', data)
        self.assertIn('total_count', data)
        self.assertIn('page_number', data)
        self.assertIn('page_size', data)
        self.assertIn('total_pages', data)
        self.assertIn('sort_by', data)
        self.assertIn('sort_order', data)
        
        # Check item structure
        if data['items']:
            item = data['items'][0]
            self.assertIn('id', item)
            self.assertIn('name', item)
            self.assertIn('description', item)
            self.assertIn('is_active', item)
            self.assertIn('created_at', item)
            self.assertIn('updated_at', item) 