from rest_framework_simplejwt.authentication import J<PERSON><PERSON><PERSON>entication
from rest_framework_simplejwt.exceptions import InvalidToken, TokenError
from rest_framework_simplejwt.token_blacklist.models import BlacklistedToken
from rest_framework_simplejwt.tokens import Token
from django.utils.translation import gettext_lazy as _
import logging

logger = logging.getLogger('user')


class CustomJWTAuthentication(JWTAuthentication):
    """Custom JWT authentication that explicitly checks blacklist"""
    
    def get_validated_token(self, raw_token):
        """
        Validates the token and checks if it's blacklisted
        """
        # First, validate the token normally
        token = super().get_validated_token(raw_token)
        
        # Then check if it's blacklisted
        if self._is_token_blacklisted(token):
            logger.warning(f"Token {token.get('jti', 'unknown')} is blacklisted - rejecting authentication")
            raise InvalidToken(_('Token is blacklisted'))
        
        logger.debug(f"Token {token.get('jti', 'unknown')} is valid and not blacklisted")
        return token
    
    def _is_token_blacklisted(self, token):
        """
        Check if the token is blacklisted
        """
        try:
            # Get the JTI (JWT ID) from the token
            jti = token.get('jti')
            if not jti:
                logger.warning("Token has no JTI - cannot check blacklist")
                return False
            
            # Check if this token is in the blacklist
            is_blacklisted = BlacklistedToken.objects.filter(token__jti=jti).exists()
            if is_blacklisted:
                logger.info(f"Token {jti} found in blacklist")
            else:
                logger.debug(f"Token {jti} not found in blacklist")
            
            return is_blacklisted
        except Exception as e:
            # If there's any error checking the blacklist, assume it's not blacklisted
            # This prevents authentication failures due to database issues
            logger.error(f"Error checking token blacklist: {str(e)}")
            return False 