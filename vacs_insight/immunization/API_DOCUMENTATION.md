# Vaccine Storage Center API Documentation

## Endpoints

### List & Search Vaccine Storage Centers

**GET** `/api/v1/immunization/vaccine-center/`

#### Query Parameters
- `search`: Search by name, province, district, or municipality (case-insensitive, partial match)
- `province_id`: Filter by province ID
- `district_id`: Filter by district ID
- `municipality_id`: Filter by municipality ID
- `sort_by`: Sort field (name, province, district, municipality, country, created_at, updated_at, latitude, longitude)
- `sort_order`: Sort order (asc, desc)

#### Permissions
- Authenticated users only

#### Response Example
```json
{
  "count": 1,
  "search": "kathmandu",
  "results": [
    {
      "id": 1,
      "name": "Central Medical Store Pathalaiya",
      "country": 1,
      "country_name": "Nepal",
      "province": 1,
      "province_name": "Madhesh",
      "district": 1,
      "district_name": "Bara",
      "municipality": 1,
      "municipality_name": "Jeetpur Simara",
      "latitude": 27.1667,
      "longitude": 84.8933,
      "created_at": "2025-06-24T12:00:00Z",
      "updated_at": "2025-06-24T12:00:00Z"
    }
  ]
}
```

#### Example Requests
```
# Basic listing
GET /api/v1/immunization/vaccine-center/

# Search with sorting
GET /api/v1/immunization/vaccine-center/?search=kathmandu&sort_by=name&sort_order=asc

# Filter by location
GET /api/v1/immunization/vaccine-center/?province_id=1

# Sort by creation date (newest first)
GET /api/v1/immunization/vaccine-center/?sort_by=created_at&sort_order=desc

# Search in specific district
GET /api/v1/immunization/vaccine-center/?search=medical&district_id=5
```

---

### Upload Vaccine Storage Centers (Bulk Upsert)

**POST** `/api/v1/immunization/vaccine-center/upload/`

#### Permissions
- Only users with `authority_level` = `center` and `position` = `Admin` or `Manager`

#### Request (multipart/form-data)
- `file`: `.csv` or `.xlsx` file with columns:
  - `Vaccine Storage Centers` (unique, required)
  - `Province` (required)
  - `District` (required)
  - `Municipality` (required)
  - `Location Coordinates` (required, e.g. `27.1667° N, 84.8933° E`)

#### Behavior
- If a row's name exists, it is updated; otherwise, it is created.
- If any location (province, district, municipality) is not found, the upload fails with an error (no partial upload).
- Coordinates are parsed and stored as floats.

#### Success Response Example
```json
{
  "detail": "Upload successful."
}
```

#### Error Response Example (invalid location)
```json
{
  "detail": "Errors in upload",
  "errors": [
    "Row 2: Location not found (District matching query does not exist.)"
  ]
}
```

#### Error Response Example (missing column)
```json
{
  "detail": "Missing required column: Province"
}
```

#### Error Response Example (unsupported file type)
```json
{
  "detail": "Unsupported file type."
}
```

---

## Model Fields
- `name` (unique, string)
- `country` (ForeignKey, auto-populated from province)
- `province` (ForeignKey)
- `district` (ForeignKey)
- `municipality` (ForeignKey)
- `latitude` (float)
- `longitude` (float)
- `created_at`, `updated_at` (timestamps)

---

## Notes
- No create/update/delete API for individual records.
- Only bulk upload via file is supported for data changes.
- All endpoints require authentication.
- All results are returned in a single response without pagination.
- Sorting supports all model fields including related location names.

## /immunization/dataset

**GET** `/immunization/dataset`

Returns aggregated immunization data grouped by program and category.

### Query Parameters
- `month` (optional, format: YYYY-MM): Filter by month. If not provided, aggregates for the year of the latest entry in the DB.
- `facility_id` (optional): Filter by facility. Can be either:
  * Integer: filters by facility.facility_id field
  * UUID: filters by facility.id (primary key)
- `province_id` (optional): Filter by facility's province (integer ID)
- `district_id` (optional): Filter by facility's district (integer ID)
- `municipality_id` (optional): Filter by facility's municipality (integer ID)
- `ward_id` (optional): Filter by facility's ward (integer ID)

### Example Requests
- **Yearly aggregation (latest year):**
  ```
  GET /immunization/dataset
  ```
- **Monthly aggregation:**
  ```
  GET /immunization/dataset?month=2024-02
  ```
- **Facility filter (integer):**
  ```
  GET /immunization/dataset?facility_id=12345
  ```
- **Facility filter (UUID):**
  ```
  GET /immunization/dataset?facility_id=09aa1062-d796-4d11-9317-7b6244bcd628
  ```
- **Province filter:**
  ```
  GET /immunization/dataset?province_id=1
  ```
- **District filter:**
  ```
  GET /immunization/dataset?district_id=2
  ```
- **Municipality filter:**
  ```
  GET /immunization/dataset?municipality_id=3
  ```
- **Ward filter:**
  ```
  GET /immunization/dataset?ward_id=4
  ```
- **Combined filters:**
  ```
  GET /immunization/dataset?province_id=1&district_id=2&municipality_id=3&ward_id=4&month=2024-02
  ```

### Example Response
```json
{
  "reportDate": "February 2024 (as on 01 July 2025 12:30:45 PM)",
  "data": [
    {
      "program": "BCG",
      "total": 1500,
      "categories": [
        {
          "label": "Immunization Program - Age Group - BCG - 0-11 Months",
          "value": 1500
        }
      ]
    },
    {
      "program": "DPT-HepB-Hib",
      "total": 2800,
      "categories": [
        {
          "label": "Immunization Program - Dose - DPT-HepB-Hib - 1st",
          "value": 1200
        },
        {
          "label": "Immunization Program - Dose - DPT-HepB-Hib - 2nd",
          "value": 1000
        },
        {
          "label": "Immunization Program - Dose - DPT-HepB-Hib - 3rd",
          "value": 600
        }
      ]
    }
  ]
}
```

### Response Fields
- `reportDate` (string): Date range of the aggregated data and current timestamp
- `data` (array): Array of immunization programs with their totals and categories
  - `program` (string): Name of the vaccine program
  - `total` (integer): Total immunizations for the program
  - `categories` (array): Array of categories within the program
    - `label` (string): Descriptive label for the category
    - `value` (integer): Number of immunizations for this category

- If no data is found, returns an empty data array with reportDate showing "No data available"
- If `month` is invalid, returns 400 with an error message.