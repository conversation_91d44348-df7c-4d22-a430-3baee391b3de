from django.db import models
from location.models import Country, Province, District, Municipality


class VaccineStorageCenter(models.Model):
    name = models.CharField(max_length=255, unique=True)
    country = models.ForeignKey(Country, on_delete=models.PROTECT, related_name='vaccine_centers')
    province = models.ForeignKey(Province, on_delete=models.PROTECT, related_name='vaccine_centers')
    district = models.ForeignKey(District, on_delete=models.PROTECT, related_name='vaccine_centers')
    municipality = models.ForeignKey(Municipality, on_delete=models.PROTECT, related_name='vaccine_centers')
    latitude = models.FloatField()
    longitude = models.FloatField()
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'vaccine_storage_center'

    def __str__(self):
        return self.name


class VaccineProgram(models.Model):
    """Master table for vaccine programs"""
    name = models.Char<PERSON>ield(max_length=255, unique=True)  # e.g., "Measles/Rubella"
    code = models.CharField(max_length=50, unique=True)   # e.g., "MR"
    description = models.TextField(blank=True)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'vaccine_program'

    def __str__(self):
        return f"{self.name} ({self.code})"


class VaccineCategory(models.Model):
    """Categories within vaccine programs (age groups, doses, target populations)"""
    CATEGORY_TYPES = [
        ('age_group', 'Age Group'),
        ('dose', 'Dose'),
        ('target_population', 'Target Population'),
        ('special', 'Special Category'),
    ]
    
    TARGET_GROUPS = [
        ('children', 'Children'),
        ('pregnant_women', 'Pregnant Women'),
        ('general', 'General'),
    ]
    
    vaccine_program = models.ForeignKey(VaccineProgram, on_delete=models.CASCADE, related_name='categories')
    category_name = models.CharField(max_length=100)  # e.g., "12-23 Months", "1st", "2nd"
    category_type = models.CharField(max_length=50, choices=CATEGORY_TYPES)
    target_group = models.CharField(max_length=50, choices=TARGET_GROUPS, default='children')
    age_min = models.IntegerField(null=True, blank=True)  # in months
    age_max = models.IntegerField(null=True, blank=True)  # in months
    dose_number = models.IntegerField(null=True, blank=True)  # for dose-based categories
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'vaccine_category'
        unique_together = ['vaccine_program', 'category_name']

    def __str__(self):
        return f"{self.vaccine_program.name} - {self.category_name}"


class ImmunizationData(models.Model):
    """Monthly immunization data for each facility"""
    time_period = models.DateField()  # First day of month
    facility = models.ForeignKey('facilities.Facility', on_delete=models.CASCADE, related_name='immunization_data')
    vaccine_category = models.ForeignKey(VaccineCategory, on_delete=models.CASCADE, related_name='immunization_data')
    children_immunized = models.IntegerField(default=0)  # Only this field from source data
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'immunization_data'
        unique_together = ['time_period', 'facility', 'vaccine_category']
        indexes = [
            models.Index(fields=['time_period']),
            models.Index(fields=['facility', 'time_period']),
            models.Index(fields=['vaccine_category', 'time_period']),
        ]

    def __str__(self):
        return f"{self.facility.name} - {self.vaccine_category} - {self.time_period.strftime('%B %Y')}"


class ImmunizationDataImport(models.Model):
    """Track immunization data imports for audit purposes"""
    IMPORT_STATUS = [
        ('pending', 'Pending'),
        ('processing', 'Processing'),
        ('completed', 'Completed'),
        ('failed', 'Failed'),
    ]
    
    file_name = models.CharField(max_length=255)
    file_size = models.IntegerField()  # in bytes
    uploaded_by = models.ForeignKey('user.User', on_delete=models.CASCADE, related_name='immunization_imports')
    time_period = models.DateField()  # Month being imported
    total_records = models.IntegerField(default=0)
    processed_records = models.IntegerField(default=0)
    failed_records = models.IntegerField(default=0)
    status = models.CharField(max_length=50, choices=IMPORT_STATUS, default='pending')
    error_message = models.TextField(blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    class Meta:
        db_table = 'immunization_data_import'
        ordering = ['-created_at']

    def __str__(self):
        return f"{self.file_name} - {self.time_period.strftime('%B %Y')} - {self.status}"
