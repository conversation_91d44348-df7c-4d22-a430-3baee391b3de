from django.urls import path, include
from rest_framework.routers import <PERSON>fault<PERSON><PERSON><PERSON>
from . import views

router = DefaultRouter()
router.register(r'vaccine-programs', views.VaccineProgramViewSet)
router.register(r'vaccine-categories', views.VaccineCategoryViewSet)
router.register(r'immunization-data', views.ImmunizationDataViewSet)
router.register(r'imports', views.ImmunizationDataImportViewSet)
router.register(r'vaccine-center', views.VaccineStorageCenterViewSet)
router.register(r'upload', views.ImmunizationDataUploadView, basename='upload')

urlpatterns = [
    path('', include(router.urls)),
    path('dataset', views.immunization_dataset, name='immunization-dataset'),
] 