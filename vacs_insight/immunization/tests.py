from django.test import TestCase
from django.urls import reverse
from rest_framework.test import APITestCase, APIClient
from rest_framework import status
from django.contrib.auth import get_user_model
from location.models import Country, Province, District, Municipality, MunicipalityType
from user.models import UserPosition
from .models import VaccineStorageCenter, ImmunizationData, VaccineCategory, VaccineProgram
from django.core.files.uploadedfile import SimpleUploadedFile
import io
import pandas as pd
from .services import import_vaccine_storage_centers
from django.utils import timezone
from datetime import date
from facilities.models import Facility

User = get_user_model()

class VaccineStorageCenterAPITestCase(APITestCase):
    def setUp(self):
        self.country, _ = Country.objects.get_or_create(name="Nepal", code="NPL")
        
        # Use existing provinces
        self.province = Province.objects.get(name="Koshi")
        self.province_madhesh = Province.objects.get(name="Madhesh")
        self.province_bagmati = Province.objects.get(name="<PERSON>gmati")
        self.province_sudurpashchim = Province.objects.get(name="Sudurpaschim")  # Note: existing name is "Sudurpaschim" not "Sudurpashchim"
        
        # Use existing districts
        self.district = District.objects.get(name="Taplejung", province=self.province)
        self.district_bara = District.objects.get(name="Bara", province=self.province_madhesh)
        self.district_kathmandu = District.objects.get(name="Kathmandu", province=self.province_bagmati)
        self.district_achham = District.objects.get(name="Achham", province=self.province_sudurpashchim)
        
        # Create municipality types
        self.municipality_type, _ = MunicipalityType.objects.get_or_create(name="Rural Municipality", code="RM")
        self.municipality_type_metro, _ = MunicipalityType.objects.get_or_create(name="Metropolitan City", code="MC")
        self.municipality_type_sub_metro, _ = MunicipalityType.objects.get_or_create(name="Sub-Metropolitan City", code="SMC")
        
        # Create municipalities needed for tests
        try:
            self.municipality = Municipality.objects.get(code="10101", district=self.district)
            if self.municipality.municipality_type != self.municipality_type:
                self.municipality.municipality_type = self.municipality_type
                self.municipality.save()
        except Municipality.DoesNotExist:
            self.municipality = Municipality.objects.create(name="Phaktanlung", code="10101", district=self.district, municipality_type=self.municipality_type)
        
        # Create other required municipalities
        self.municipality_jeetpur, _ = Municipality.objects.get_or_create(
            name="Jeetpur Simara", code="99901", district=self.district_bara, municipality_type=self.municipality_type_sub_metro
        )
        self.municipality_kathmandu, _ = Municipality.objects.get_or_create(
            name="Kathmandu", code="99902", district=self.district_kathmandu, municipality_type=self.municipality_type_metro
        )
        self.municipality_mangalsen, _ = Municipality.objects.get_or_create(
            name="Mangalsen", code="99903", district=self.district_achham, municipality_type=self.municipality_type
        )
        
        self.admin_position, _ = UserPosition.objects.get_or_create(name="Admin", defaults={"description": "Administrator", "is_active": True})
        self.user, _ = User.objects.get_or_create(username="admin", defaults={"password": "testpass", "authority_level": "center", "position": self.admin_position})
        self.user.position = self.admin_position
        self.user.authority_level = "center"
        self.user.set_password("testpass")
        self.user.save()
        self.client = APIClient()
        self.client.force_authenticate(user=self.user)
        self.url = reverse('vaccine-center-list')
        self.upload_url = reverse('vaccine-center-upload')

    def test_create_and_search_vaccine_center(self):
        # Upload a file
        data = {
            'Vaccine Storage Centers': ['Central Store'],
            'Province': ['Koshi'],
            'District': ['Taplejung'],
            'Municipality': ['Phaktanlung Rural Municipality'],
            'Location Coordinates': ['27.1667° N, 84.8933° E']
        }
        df = pd.DataFrame(data)
        csv_bytes = df.to_csv(index=False).encode('utf-8')
        csv_file = SimpleUploadedFile('test.csv', csv_bytes, content_type='text/csv')
        response = self.client.post(self.upload_url, {'file': csv_file}, format='multipart')
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(VaccineStorageCenter.objects.count(), 1)
        
        # Search by name
        response = self.client.get(self.url, {'search': 'Central'})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['results'][0]['name'], 'Central Store')
        self.assertEqual(response.data['count'], 1)
        self.assertEqual(response.data['search'], 'Central')
        
        # Filter by province
        response = self.client.get(self.url, {'province_id': self.province.id})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(len(response.data['results']), 1)
        self.assertEqual(response.data['count'], 1)
        
        # Test basic listing without pagination
        response = self.client.get(self.url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('results', response.data)
        self.assertIn('count', response.data)
        self.assertIn('search', response.data)
        self.assertEqual(response.data['count'], 1)
        self.assertEqual(len(response.data['results']), 1)
        self.assertEqual(response.data['search'], '')

    def test_upload_invalid_location(self):
        data = {
            'Vaccine Storage Centers': ['Invalid Center'],
            'Province': ['NotExist'],
            'District': ['Taplejung'],
            'Municipality': ['Phaktanlung Rural Municipality'],
            'Location Coordinates': ['27.1667° N, 84.8933° E']
        }
        df = pd.DataFrame(data)
        csv_bytes = df.to_csv(index=False).encode('utf-8')
        csv_file = SimpleUploadedFile('test.csv', csv_bytes, content_type='text/csv')
        response = self.client.post(self.upload_url, {'file': csv_file}, format='multipart')
        self.assertEqual(response.status_code, status.HTTP_400_BAD_REQUEST)
        self.assertIn('errors', response.data)

    def test_upload_sample_data(self):
        # Prepare the CSV data
        data = [
            {
                "Vaccine Storage Centers": "Central Medical Store Pathalaiya",
                "Province": "Madhesh",
                "District": "Bara",
                "Municipality": "Jeetpur Simara",
                "Location Coordinates": "27.1667° N, 84.9833° E"
            },
            {
                "Vaccine Storage Centers": "CW Teku - EPI Store (10 Districts)",
                "Province": "Bagmati",
                "District": "Kathmandu",
                "Municipality": "Kathmandu",
                "Location Coordinates": "27.6934° N, 85.3123° E"
            },
            {
                "Vaccine Storage Centers": "EDCD Store - CW Teku",
                "Province": "Bagmati",
                "District": "Kathmandu",
                "Municipality": "Kathmandu",
                "Location Coordinates": "27.6934° N, 85.3123° E"
            },
            {
                "Vaccine Storage Centers": "Health Office Achham",
                "Province": "Sudurpaschim",
                "District": "Achham",
                "Municipality": "Mangalsen",
                "Location Coordinates": "29.1205° N, 81.2980° E"
            }
        ]
        df = pd.DataFrame(data)
        csv_bytes = df.to_csv(index=False).encode('utf-8')
        csv_file = SimpleUploadedFile('test.csv', csv_bytes, content_type='text/csv')
        response = self.client.post(self.upload_url, {'file': csv_file}, format='multipart')
        print("Status code:", response.status_code)
        print("Response data:", response.data)

    def test_import_service_success(self):
        # Ensure required locations exist
        country, _ = Country.objects.get_or_create(name="Nepal", code="NPL")
        province, _ = Province.objects.get_or_create(name="Koshi", code="1", country=country)
        district, _ = District.objects.get_or_create(name="Taplejung", code="101", province=province)
        municipality_type, _ = MunicipalityType.objects.get_or_create(name="Rural Municipality", code="RM")
        try:
            municipality = Municipality.objects.get(code="10101", district=district)
            if municipality.municipality_type != municipality_type:
                municipality.municipality_type = municipality_type
                municipality.save()
        except Municipality.DoesNotExist:
            municipality = Municipality.objects.create(name="Phaktanlung Rural Municipality", code="10101", district=district, municipality_type=municipality_type)
        
        # Prepare the CSV data
        data = [
            {
                "Vaccine Storage Centers": "Central Medical Store Pathalaiya",
                "Province": "Koshi",
                "District": "Taplejung",
                "Municipality": "Phaktanlung Rural Municipality",
                "Location Coordinates": "27.1667° N, 84.9833° E"
            }
        ]
        import pandas as pd
        import io
        df = pd.DataFrame(data)
        csv_bytes = df.to_csv(index=False).encode('utf-8')
        csv_file = io.BytesIO(csv_bytes)
        csv_file.name = 'test.csv'
        success, errors = import_vaccine_storage_centers(csv_file)
        
        self.assertTrue(success)
        self.assertEqual(errors, [])
        self.assertEqual(VaccineStorageCenter.objects.count(), 1)

    def test_import_service_fail(self):
        # Prepare the CSV data with a non-existent location
        data = [
            {
                "Vaccine Storage Centers": "Invalid Center",
                "Province": "NotExist",
                "District": "Taplejung",
                "Municipality": "Phaktanlung Rural Municipality",
                "Location Coordinates": "27.1667° N, 84.8933° E"
            }
        ]
        import pandas as pd
        import io
        df = pd.DataFrame(data)
        csv_bytes = df.to_csv(index=False).encode('utf-8')
        csv_file = io.BytesIO(csv_bytes)
        csv_file.name = 'test.csv'
        success, errors = import_vaccine_storage_centers(csv_file)
        self.assertFalse(success)
        self.assertTrue(errors)
        self.assertIn('Province not found', errors[0])

    def test_listing_api(self):
        """Test the listing API with filtering and search functionality (no pagination)"""
        # First, upload some test data
        data = [
            {
                "Vaccine Storage Centers": "Central Medical Store Pathalaiya",
                "Province": "Madhesh",
                "District": "Bara",
                "Municipality": "Jeetpur Simara",
                "Location Coordinates": "27.1667° N, 84.9833° E"
            },
            {
                "Vaccine Storage Centers": "CW Teku - EPI Store (10 Districts)",
                "Province": "Bagmati",
                "District": "Kathmandu",
                "Municipality": "Kathmandu",
                "Location Coordinates": "27.6934° N, 85.3123° E"
            },
            {
                "Vaccine Storage Centers": "Health Office Achham",
                "Province": "Sudurpaschim",
                "District": "Achham",
                "Municipality": "Mangalsen",
                "Location Coordinates": "29.1205° N, 81.2980° E"
            }
        ]
        df = pd.DataFrame(data)
        csv_bytes = df.to_csv(index=False).encode('utf-8')
        csv_file = SimpleUploadedFile('test.csv', csv_bytes, content_type='text/csv')
        response = self.client.post(self.upload_url, {'file': csv_file}, format='multipart')
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(VaccineStorageCenter.objects.count(), 3)

        # Test basic listing without pagination
        response = self.client.get(self.url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertIn('results', response.data)
        self.assertIn('count', response.data)
        self.assertIn('search', response.data)
        self.assertEqual(response.data['count'], 3)
        self.assertEqual(len(response.data['results']), 3)
        self.assertEqual(response.data['search'], '')  # Empty search string when no search provided

        # Test search by name
        response = self.client.get(self.url, {'search': 'Central'})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['count'], 1)
        self.assertEqual(response.data['results'][0]['name'], 'Central Medical Store Pathalaiya')
        # Verify search string is returned in response
        self.assertEqual(response.data['search'], 'Central')

        # Test search by partial name
        response = self.client.get(self.url, {'search': 'Teku'})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['count'], 1)
        self.assertEqual(response.data['results'][0]['name'], 'CW Teku - EPI Store (10 Districts)')
        # Verify search string is returned in response
        self.assertEqual(response.data['search'], 'Teku')

        # Test filtering by province
        response = self.client.get(self.url, {'province_id': self.province_madhesh.id})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['count'], 1)
        self.assertEqual(response.data['results'][0]['name'], 'Central Medical Store Pathalaiya')

        # Test filtering by district
        response = self.client.get(self.url, {'district_id': self.district_kathmandu.id})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['count'], 1)
        self.assertEqual(response.data['results'][0]['name'], 'CW Teku - EPI Store (10 Districts)')

        # Test filtering by municipality
        response = self.client.get(self.url, {'municipality_id': self.municipality_jeetpur.id})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['count'], 1)
        self.assertEqual(response.data['results'][0]['name'], 'Central Medical Store Pathalaiya')

        # Test sorting by name (ascending)
        response = self.client.get(self.url, {'sort_by': 'name', 'sort_order': 'asc'})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        # Check alphabetical order: CW, Central, Health
        self.assertEqual(response.data['results'][0]['name'], 'CW Teku - EPI Store (10 Districts)')
        self.assertEqual(response.data['results'][1]['name'], 'Central Medical Store Pathalaiya')
        self.assertEqual(response.data['results'][2]['name'], 'Health Office Achham')

        # Test sorting by name (descending)
        response = self.client.get(self.url, {'sort_by': 'name', 'sort_order': 'desc'})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        # Check reverse alphabetical order: Health, Central, CW
        self.assertEqual(response.data['results'][0]['name'], 'Health Office Achham')
        self.assertEqual(response.data['results'][1]['name'], 'Central Medical Store Pathalaiya')
        self.assertEqual(response.data['results'][2]['name'], 'CW Teku - EPI Store (10 Districts)')

        # Test sorting by province name
        response = self.client.get(self.url, {'sort_by': 'province', 'sort_order': 'asc'})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        # Should be sorted by province name alphabetically

        # Test sorting by creation date
        response = self.client.get(self.url, {'sort_by': 'created_at', 'sort_order': 'desc'})
        self.assertEqual(response.status_code, status.HTTP_200_OK)

        # Test combined filtering and search
        response = self.client.get(self.url, {
            'search': 'Store',
            'province_id': self.province_bagmati.id
        })
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['count'], 1)
        self.assertEqual(response.data['results'][0]['name'], 'CW Teku - EPI Store (10 Districts)')

        # Test response structure
        result = response.data['results'][0]
        required_fields = ['id', 'name', 'province', 'province_name', 'district', 'district_name', 'municipality', 'municipality_name', 'latitude', 'longitude', 'created_at', 'updated_at']
        for field in required_fields:
            self.assertIn(field, result)

        # Test that location data is properly included
        self.assertIsInstance(result['province'], int)  # Province ID
        self.assertIsInstance(result['province_name'], str)  # Province name
        self.assertIsInstance(result['district'], int)  # District ID
        self.assertIsInstance(result['district_name'], str)  # District name
        self.assertIsInstance(result['municipality'], int)  # Municipality ID
        self.assertIsInstance(result['municipality_name'], str)  # Municipality name

    def test_non_paginated_response_format(self):
        """Test the non-paginated response format and edge cases"""
        # First create some test data to work with
        for i in range(25):
            VaccineStorageCenter.objects.create(
                name=f"Test Center {i}",
                country=self.country,
                province=self.province,
                district=self.district,
                municipality=self.municipality,
                latitude=27.1667,
                longitude=84.8933
            )

        # Test that all results are returned in a single response
        response = self.client.get(self.url)
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['count'], 25)
        self.assertEqual(len(response.data['results']), 25)
        self.assertIn('search', response.data)
        self.assertEqual(response.data['search'], '')

        # Test search functionality with count
        response = self.client.get(self.url, {'search': 'Test Center 1'})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['count'], 11)  # Test Center 1, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19
        self.assertEqual(len(response.data['results']), 11)
        self.assertEqual(response.data['search'], 'Test Center 1')

        # Test empty search result
        response = self.client.get(self.url, {'search': 'NonExistentCenter'})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['count'], 0)
        self.assertEqual(len(response.data['results']), 0)
        self.assertEqual(response.data['search'], 'NonExistentCenter')

        # Test filtering with count
        response = self.client.get(self.url, {'province_id': self.province.id})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['count'], 25)
        self.assertEqual(len(response.data['results']), 25)

        # Test combined search and filter
        response = self.client.get(self.url, {
            'search': 'Test Center',
            'province_id': self.province.id
        })
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['count'], 25)
        self.assertEqual(len(response.data['results']), 25)
        self.assertEqual(response.data['search'], 'Test Center')

        # Test that pagination parameters are ignored
        response = self.client.get(self.url, {'page_number': 2, 'page_size': 10})
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        self.assertEqual(response.data['count'], 25)
        self.assertEqual(len(response.data['results']), 25)
        # Should not contain pagination fields
        self.assertNotIn('page_number', response.data)
        self.assertNotIn('page_size', response.data)
        self.assertNotIn('total_pages', response.data)
        self.assertNotIn('next', response.data)
        self.assertNotIn('previous', response.data)

class ImmunizationDatasetAPITestCase(APITestCase):
    def setUp(self):
        # Create user and authenticate
        self.user = User.objects.create_user(username='testuser', password='testpass')
        self.client = APIClient()
        self.client.force_authenticate(user=self.user)
        
        # Create programs, categories, facility, and data
        program = VaccineProgram.objects.create(name='Test Program', code='TP')
        category1 = VaccineCategory.objects.create(
            vaccine_program=program, 
            category_name='Cat1', 
            category_type='age_group'
        )
        category2 = VaccineCategory.objects.create(
            vaccine_program=program, 
            category_name='Cat2', 
            category_type='dose'
        )
        
        # Create facility with unique facility_id
        self.facility = Facility.objects.create(
            facility_id=99999999, name='Test Facility', hf_code='99999999',
            country_id=1, province_id=1, district_id=1, municipality_id=1, authority_level_id=1,
            facility_type_id=1, authority='Test', is_active=True
        )
        
        # Create test data
        ImmunizationData.objects.create(
            time_period=date(2024, 2, 1),
            facility=self.facility,
            vaccine_category=category1,
            children_immunized=20
        )
        ImmunizationData.objects.create(
            time_period=date(2024, 2, 1),
            facility=self.facility,
            vaccine_category=category2,
            children_immunized=10
        )

    def test_dataset_yearly(self):
        """Test yearly aggregation (latest year)"""
        url = reverse('immunization-dataset')
        response = self.client.get(f"{url}?facility_id={self.facility.facility_id}")
        
        self.assertEqual(response.status_code, 200)
        data = response.json()
        
        # Check response structure
        self.assertIn('reportDate', data)
        self.assertIn('data', data)
        
        # Should return all programs, even with zero values
        self.assertGreater(len(data['data']), 0)
        
        # Find our test program
        test_program = next((p for p in data['data'] if p['program'] == 'Test Program'), None)
        self.assertIsNotNone(test_program)
        self.assertEqual(test_program['total'], 30)  # 20 + 10
        self.assertEqual(len(test_program['categories']), 2)
        
        # Check categories
        cat1 = next((c for c in test_program['categories'] if 'Cat1' in c['label']), None)
        cat2 = next((c for c in test_program['categories'] if 'Cat2' in c['label']), None)
        self.assertIsNotNone(cat1)
        self.assertIsNotNone(cat2)
        self.assertEqual(cat1['value'], 20)
        self.assertEqual(cat2['value'], 10)

    def test_dataset_monthly(self):
        """Test monthly aggregation"""
        url = reverse('immunization-dataset')
        response = self.client.get(f"{url}?month=2024-02&facility_id={self.facility.facility_id}")
        
        self.assertEqual(response.status_code, 200)
        data = response.json()
        
        # Check response structure
        self.assertIn('reportDate', data)
        self.assertIn('data', data)
        
        # Should return all programs, even with zero values
        self.assertGreater(len(data['data']), 0)
        
        # Find our test program
        test_program = next((p for p in data['data'] if p['program'] == 'Test Program'), None)
        self.assertIsNotNone(test_program)
        self.assertEqual(test_program['total'], 30)  # 20 + 10
        self.assertEqual(len(test_program['categories']), 2)
        
        # Check categories
        cat1 = next((c for c in test_program['categories'] if 'Cat1' in c['label']), None)
        cat2 = next((c for c in test_program['categories'] if 'Cat2' in c['label']), None)
        self.assertIsNotNone(cat1)
        self.assertIsNotNone(cat2)
        self.assertEqual(cat1['value'], 20)
        self.assertEqual(cat2['value'], 10)
