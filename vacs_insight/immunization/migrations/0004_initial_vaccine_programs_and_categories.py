from django.db import migrations


def create_initial_vaccine_programs_and_categories(apps, schema_editor):
    """Create initial vaccine programs and categories based on the 21 vaccine columns"""
    VaccineProgram = apps.get_model('immunization', 'VaccineProgram')
    VaccineCategory = apps.get_model('immunization', 'VaccineCategory')
    
    # Clear existing data to avoid duplicates
    print("🗑️ Clearing existing vaccine programs and categories...")
    VaccineCategory.objects.all().delete()
    VaccineProgram.objects.all().delete()
    
    # Create vaccine programs
    vaccine_programs = [
        {
            'name': 'Measles/Rubella',
            'code': 'MR',
            'description': 'Measles and Rubella vaccine'
        },
        {
            'name': 'BCG',
            'code': 'BCG',
            'description': 'Bacillus Calmette-Guérin vaccine for tuberculosis'
        },
        {
            'name': 'JE',
            'code': 'JE',
            'description': 'Japanese Encephalitis vaccine'
        },
        {
            'name': 'TD',
            'code': 'TD',
            'description': 'Tetanus-Diphtheria vaccine'
        },
        {
            'name': 'DPT-HepB-Hib',
            'code': 'DPT-HepB-Hib',
            'description': 'Diphtheria, Pertussis, Tetanus, Hepatitis B, and Haemophilus influenzae type b vaccine'
        },
        {
            'name': 'FIPV',
            'code': 'FIPV',
            'description': 'Fractional Inactivated Polio Vaccine'
        },
        {
            'name': 'OPV',
            'code': 'OPV',
            'description': 'Oral Polio Vaccine'
        },
        {
            'name': 'PCV',
            'code': 'PCV',
            'description': 'Pneumococcal Conjugate Vaccine'
        },
        {
            'name': 'Rota',
            'code': 'Rota',
            'description': 'Rotavirus vaccine'
        },
        {
            'name': 'Fully Immunized',
            'code': 'FI',
            'description': 'Fully immunized children within 23 months'
        },
        {
            'name': 'TCV',
            'code': 'TCV',
            'description': 'Typhoid Conjugate Vaccine'
        }
    ]
    
    created_programs = {}
    for program_data in vaccine_programs:
        program = VaccineProgram.objects.create(**program_data)
        created_programs[program.code] = program
        print(f"✅ Created vaccine program: {program.name}")
    
    # Create vaccine categories
    vaccine_categories = [
        # Measles/Rubella (2 categories)
        ('MR', '12-23 Months', 'age_group', 'children', 12, 23),
        ('MR', '9-11 Months', 'age_group', 'children', 9, 11),
        
        # BCG (1 category)
        ('BCG', 'Single Dose', 'dose', 'children', None, None),
        
        # JE (1 category)
        ('JE', 'Single Dose', 'dose', 'children', None, None),
        
        # TD for Pregnant Women (2 categories)
        ('TD', '1st', 'dose', 'pregnant_women', None, None),
        ('TD', '2nd', 'dose', 'pregnant_women', None, None),
        
        # DPT-HepB-Hib (3 categories)
        ('DPT-HepB-Hib', '1st', 'dose', 'children', None, None),
        ('DPT-HepB-Hib', '2nd', 'dose', 'children', None, None),
        ('DPT-HepB-Hib', '3rd', 'dose', 'children', None, None),
        
        # FIPV (2 categories)
        ('FIPV', '1st', 'dose', 'children', None, None),
        ('FIPV', '2nd', 'dose', 'children', None, None),
        
        # OPV (3 categories)
        ('OPV', '1st', 'dose', 'children', None, None),
        ('OPV', '2nd', 'dose', 'children', None, None),
        ('OPV', '3rd', 'dose', 'children', None, None),
        
        # PCV (3 categories)
        ('PCV', '1st', 'dose', 'children', None, None),
        ('PCV', '2nd', 'dose', 'children', None, None),
        ('PCV', '3rd', 'dose', 'children', None, None),
        
        # Rota (2 categories)
        ('Rota', '1st', 'dose', 'children', None, None),
        ('Rota', '2nd', 'dose', 'children', None, None),
        
        # Fully Immunized (1 category)
        ('FI', 'Within 23 months', 'special', 'children', None, 23),
        
        # TCV (1 category)
        ('TCV', 'Single Dose', 'dose', 'children', None, None),
    ]
    
    for code, category_name, category_type, target_group, age_min, age_max in vaccine_categories:
        program = created_programs[code]
        category = VaccineCategory.objects.create(
            vaccine_program=program,
            category_name=category_name,
            category_type=category_type,
            target_group=target_group,
            age_min=age_min,
            age_max=age_max
        )
        print(f"✅ Created vaccine category: {category}")
    
    print(f"\n🎉 Created {len(created_programs)} vaccine programs and {len(vaccine_categories)} categories")


def reverse_create_initial_vaccine_programs_and_categories(apps, schema_editor):
    """Reverse the creation of vaccine programs and categories"""
    VaccineProgram = apps.get_model('immunization', 'VaccineProgram')
    VaccineCategory = apps.get_model('immunization', 'VaccineCategory')
    
    VaccineCategory.objects.all().delete()
    VaccineProgram.objects.all().delete()


class Migration(migrations.Migration):
    dependencies = [
        ('immunization', '0003_add_immunization_data_models'),
    ]

    operations = [
        migrations.RunPython(
            create_initial_vaccine_programs_and_categories,
            reverse_create_initial_vaccine_programs_and_categories
        ),
    ] 