# Generated by Django 4.2.10 on 2025-06-24 13:24

from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        ('location', '0006_initial_wards'),
    ]

    operations = [
        migrations.CreateModel(
            name='VaccineStorageCenter',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255, unique=True)),
                ('latitude', models.FloatField()),
                ('longitude', models.FloatField()),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('country', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='vaccine_centers', to='location.country')),
                ('district', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='vaccine_centers', to='location.district')),
                ('municipality', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='vaccine_centers', to='location.municipality')),
                ('province', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, related_name='vaccine_centers', to='location.province')),
            ],
            options={
                'db_table': 'vaccine_storage_center',
            },
        ),
    ]
