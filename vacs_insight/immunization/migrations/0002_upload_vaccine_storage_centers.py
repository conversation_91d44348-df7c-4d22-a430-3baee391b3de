from django.db import migrations
import os
from immunization.services import import_vaccine_storage_centers


def upload_vaccine_storage_centers(apps, schema_editor):
    from django.conf import settings
    db_name = settings.DATABASES['default']['NAME']
    if 'test' in db_name.lower():
        print('Skipping vaccine storage centers upload during tests')
        return

    try:
        base_dir = os.path.dirname(__file__)
        file_path = os.path.join(base_dir, 'files', 'Vaccine_Storage_Centers.xlsx')
        if not os.path.exists(file_path):
            print(f"File not found: {file_path}")
            print("Migration marked as incomplete - file not found")
            return

        success, errors = import_vaccine_storage_centers(file_path, is_excel=True)
        if not success:
            print('Errors in upload:')
            for err in errors:
                print(err)
            print("Migration marked as incomplete - import failed")
            return
        print('Vaccine Storage Centers upload successful.')
    except Exception as e:
        print(f"Migration failed with exception: {str(e)}")
        print("Migration marked as incomplete - exception occurred")
        # Don't raise the exception to prevent migration failure
        return


class Migration(migrations.Migration):
    dependencies = [
        ('immunization', '0001_initial'),
    ]
    operations = [
        migrations.RunPython(upload_vaccine_storage_centers, reverse_code=migrations.RunPython.noop),
    ]
