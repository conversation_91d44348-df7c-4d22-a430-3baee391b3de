from django.db import migrations
import os
import pandas as pd
from datetime import datetime
from rapidfuzz import fuzz, process
import uuid


def parse_time_period(period_str):
    # Example: "Feb 2024" or "February 2024" -> datetime.date(2024, 2, 1)
    try:
        parts = period_str.split()
        month_str = parts[0].lower()
        year = int(parts[1])
        
        # Handle both abbreviated and full month names
        month_mapping = {
            'jan': 1, 'january': 1,
            'feb': 2, 'february': 2,
            'mar': 3, 'march': 3,
            'apr': 4, 'april': 4,
            'may': 5, 'may': 5,
            'jun': 6, 'june': 6,
            'jul': 7, 'july': 7,
            'aug': 8, 'august': 8,
            'sep': 9, 'september': 9,
            'oct': 10, 'october': 10,
            'nov': 11, 'november': 11,
            'dec': 12, 'december': 12,
        }
        
        # Try exact match first, then try first 3 characters
        if month_str in month_mapping:
            month = month_mapping[month_str]
        elif month_str[:3] in month_mapping:
            month = month_mapping[month_str[:3]]
        else:
            print(f"❌ Could not parse month: {month_str}")
            return None
            
        return datetime(year, month, 1).date()
    except Exception as e:
        print(f"❌ Error parsing time period '{period_str}': {str(e)}")
        return None


def import_immunization_data(apps, schema_editor):
    """Re-import immunization data with fixed time period parsing"""
    Facility = apps.get_model('facilities', 'Facility')
    VaccineProgram = apps.get_model('immunization', 'VaccineProgram')
    VaccineCategory = apps.get_model('immunization', 'VaccineCategory')
    ImmunizationData = apps.get_model('immunization', 'ImmunizationData')
    ImmunizationDataImport = apps.get_model('immunization', 'ImmunizationDataImport')

    # Column mapping from Excel headers to vaccine programs and categories
    COLUMN_MAPPINGS = {
        'Immunization Program-Children Immunized-Measles/Rubella-12-23 Months': ('MR', '12-23 Months'),
        'Immunization Program-Children Immunized-Measles/Rubella-9-11 Months': ('MR', '9-11 Months'),
        'Immunization Program-Vaccine Type-Children Immunized-BCG Doses': ('BCG', 'Single Dose'),
        'Immunization Program-Vaccine Type-Children Immunized-JE': ('JE', 'Single Dose'),
        'Immunization Program-Vaccine Type-Children Immunized-TD(Pregnant Women)-2': ('TD', '2nd'),
        'Immunization Program-Vaccine Type-Dose-Children Immunized-DPT-HepB-Hib-1st': ('DPT-HepB-Hib', '1st'),
        'Immunization Program-Vaccine Type-Dose-Children Immunized-DPT-HepB-Hib-2nd': ('DPT-HepB-Hib', '2nd'),
        'Immunization Program-Vaccine Type-Dose-Children Immunized-DPT-HepB-Hib-3rd': ('DPT-HepB-Hib', '3rd'),
        'Immunization Program-Vaccine Type-Dose-Children Immunized-FIPV-1st': ('FIPV', '1st'),
        'Immunization Program-Vaccine Type-Dose-Children Immunized-FIPV-2nd': ('FIPV', '2nd'),
        'Immunization Program-Vaccine Type-Dose-Children Immunized-OPV-1st': ('OPV', '1st'),
        'Immunization Program-Vaccine Type-Dose-Children Immunized-OPV-2nd': ('OPV', '2nd'),
        'Immunization Program-Vaccine Type-Dose-Children Immunized-OPV-3rd': ('OPV', '3rd'),
        'Immunization Program-Vaccine Type-Dose-Children Immunized-PCV-1st': ('PCV', '1st'),
        'Immunization Program-Vaccine Type-Dose-Children Immunized-PCV-2nd': ('PCV', '2nd'),
        'Immunization Program-Vaccine Type-Dose-Children Immunized-PCV-3rd': ('PCV', '3rd'),
        'Immunization Program-Vaccine Type-Dose-Children Immunized-Rota-1st': ('Rota', '1st'),
        'Immunization Program-Vaccine Type-Dose-Children Immunized-Rota-2nd': ('Rota', '2nd'),
        'Immunization-Fully immunized-Within 23 months': ('FI', 'Within 23 months'),
        'Immunization-TD-Pregnant Women-1': ('TD', '1st'),
        'Immunization-Vaccine-Children Immunized-TCV': ('TCV', 'Single Dose'),
    }

    # Load reference data
    vaccine_programs = {}
    vaccine_categories = {}
    print("🔄 Loading reference data...")
    for program in VaccineProgram.objects.all():
        vaccine_programs[program.code] = program
    for category in VaccineCategory.objects.all():
        key = (category.vaccine_program.code, category.category_name)
        vaccine_categories[key] = category
    facilities_by_hf_code = {}
    facilities_by_name = {}
    facilities_by_muni = {}
    facilities_by_dist = {}
    for f in Facility.objects.all():
        facilities_by_hf_code[str(f.hf_code)] = f
        facilities_by_name[f.name.strip().lower()] = f
        if f.municipality:
            key = (f.name.strip().lower(), f.municipality.name.strip().lower())
            facilities_by_muni[key] = f
        if f.district:
            key = (f.name.strip().lower(), f.district.name.strip().lower())
            facilities_by_dist[key] = f
    print(f"✅ Loaded {len(vaccine_programs)} vaccine programs, {len(vaccine_categories)} categories")
    print(f"✅ Loaded {len(facilities_by_hf_code)} facilities by HF_ID")
    
    # Read Excel file
    file_path = 'immunization/migrations/files/Immunization_Data.xlsx'
    if not os.path.exists(file_path):
        print(f"❌ File not found: {file_path}")
        return
    print(f"📊 Reading Excel file: {file_path}")
    df = pd.read_excel(file_path)
    print(f"✅ Loaded {len(df)} rows from Excel file")
    
    # Group data by time period and process each month separately
    time_periods = df['Time Period'].unique()
    print(f"📅 Found {len(time_periods)} unique time periods: {time_periods}")
    
    total_processed = 0
    total_failed = 0
    
    for time_period_str in time_periods:
        print(f"\n🔄 Processing time period: {time_period_str}")
        time_period = parse_time_period(time_period_str)
        if not time_period:
            print(f"❌ Skipping time period '{time_period_str}' - could not parse")
            continue
            
        print(f"📅 Parsed as: {time_period}")
        
        # Filter data for this time period
        period_df = df[df['Time Period'] == time_period_str]
        print(f"📊 Processing {len(period_df)} rows for {time_period_str}")
        
        # Create import record for this period
        import_record = ImmunizationDataImport.objects.create(
            file_name=f"{os.path.basename(file_path)} - {time_period_str}",
            file_size=os.path.getsize(file_path),
            uploaded_by_id=1,  # Default admin user
            time_period=time_period,
            total_records=len(period_df),
            status='processing'
        )
        
        processed_count = 0
        failed_count = 0
        batch_size = 10000
        immunization_data_batch = []
        
        for index, row in period_df.iterrows():
            try:
                # Find facility
                facility = None
                hf_id = str(row.get('HF_ID')).strip()
                facility_name = str(row.get('Health Facility', '')).strip().lower()
                muni_name = str(row.get('Municipality Name', '')).strip().lower()
                dist_name = str(row.get('District Name', '')).strip().lower()
                
                # 1. Try direct HF_ID lookup (if it's a valid hf_code)
                if hf_id and hf_id in facilities_by_hf_code:
                    facility = facilities_by_hf_code[hf_id]
                else:
                    # 2. Try to find facility by UUID if HF_ID looks like a UUID
                    try:
                        uuid.UUID(hf_id)  # Validate UUID format
                        # Try to find facility by UUID primary key
                        facility = Facility.objects.filter(id=hf_id).first()
                    except (ValueError, TypeError):
                        # HF_ID is not a UUID, continue with name-based matching
                        pass
                
                if not facility:
                    # 3. Fuzzy match on name (all facilities)
                    candidates = list(facilities_by_name.keys())
                    best_match = process.extractOne(facility_name, candidates, scorer=fuzz.ratio)
                    if best_match and best_match[1] >= 80:
                        facility = facilities_by_name[best_match[0]]
                    else:
                        # 4. Fuzzy match within same municipality
                        muni_key = (facility_name, muni_name)
                        if muni_key in facilities_by_muni:
                            facility = facilities_by_muni[muni_key]
                        else:
                            # 5. Fuzzy match within same district
                            dist_key = (facility_name, dist_name)
                            if dist_key in facilities_by_dist:
                                facility = facilities_by_dist[dist_key]
                            else:
                                # 6. As last resort, fuzzy match within all facilities with relaxed threshold
                                best_match = process.extractOne(facility_name, candidates, scorer=fuzz.ratio)
                                if best_match and best_match[1] >= 60:
                                    facility = facilities_by_name[best_match[0]]
                
                if not facility:
                    failed_count += 1
                    if failed_count <= 5:
                        print(f"  Row {index}: Facility not found - HF_ID: {hf_id}, Name: {facility_name}")
                    continue
                
                # Process each vaccine column
                for column_name, value in row.items():
                    if column_name in COLUMN_MAPPINGS and pd.notna(value):
                        try:
                            vaccine_code, category_name = COLUMN_MAPPINGS[column_name]
                            category_key = (vaccine_code, category_name)
                            if category_key not in vaccine_categories:
                                if failed_count <= 5:
                                    print(f"  Row {index}: Vaccine category not found - {vaccine_code}:{category_name}")
                                failed_count += 1
                                continue
                            vaccine_category = vaccine_categories[category_key]
                            children_immunized = int(value) if pd.notna(value) else 0
                            
                            # Add to batch
                            immunization_data_batch.append(ImmunizationData(
                                time_period=time_period,
                                facility=facility,
                                vaccine_category=vaccine_category,
                                children_immunized=children_immunized
                            ))
                            processed_count += 1
                            
                            # Process batch when it reaches batch_size
                            if len(immunization_data_batch) >= batch_size:
                                ImmunizationData.objects.bulk_create(
                                    immunization_data_batch,
                                    ignore_conflicts=True  # Skip duplicates
                                )
                                print(f"  ✅ Processed batch: {processed_count} records so far")
                                immunization_data_batch = []
                                
                        except (ValueError, TypeError) as e:
                            if failed_count <= 5:
                                print(f"  Row {index}, Column {column_name}: Invalid value '{value}' - {str(e)}")
                            failed_count += 1
                            
            except Exception as e:
                failed_count += 1
                if failed_count <= 5:
                    print(f"  Row {index}: {str(e)}")
        
        # Process remaining batch
        if immunization_data_batch:
            ImmunizationData.objects.bulk_create(
                immunization_data_batch,
                ignore_conflicts=True
            )
            print(f"  ✅ Processed final batch: {len(immunization_data_batch)} records")
        
        # Update import record
        import_record.processed_records = processed_count
        import_record.failed_records = failed_count
        import_record.status = 'completed' if failed_count == 0 else 'completed_with_errors'
        import_record.save()
        
        total_processed += processed_count
        total_failed += failed_count
        
        print(f"  ✅ {time_period_str}: {processed_count} records processed, {failed_count} failed")
    
    print(f"\n🎉 Re-import completed: {total_processed} total records processed, {total_failed} total failed")


def reverse_import_immunization_data(apps, schema_editor):
    """Reverse the re-import by deleting all immunization data"""
    ImmunizationData = apps.get_model('immunization', 'ImmunizationData')
    ImmunizationDataImport = apps.get_model('immunization', 'ImmunizationDataImport')
    
    # Delete all immunization data
    deleted_count = ImmunizationData.objects.all().delete()[0]
    print(f"🗑️ Deleted {deleted_count} immunization data records")
    
    # Delete import records
    import_deleted = ImmunizationDataImport.objects.all().delete()[0]
    print(f"🗑️ Deleted {import_deleted} import records")


class Migration(migrations.Migration):
    dependencies = [
        ('user', '0004_authority_level_and_location_implementation'),
        ('facilities', '0005_import_v1_nhfr_facilities'),
        ('immunization', '0004_initial_vaccine_programs_and_categories'),
        ('location', '0006_initial_wards'),
    ]
    operations = [
        migrations.RunPython(
            import_immunization_data,
            reverse_import_immunization_data
        ),
    ]
