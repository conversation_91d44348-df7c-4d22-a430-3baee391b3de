# Generated by Django 4.2.10 on 2025-06-29 19:12

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion


class Migration(migrations.Migration):

    dependencies = [
        ('facilities', '0005_import_v1_nhfr_facilities'),
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('immunization', '0002_upload_vaccine_storage_centers'),
    ]

    operations = [
        migrations.CreateModel(
            name='VaccineProgram',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('name', models.CharField(max_length=255, unique=True)),
                ('code', models.CharField(max_length=50, unique=True)),
                ('description', models.TextField(blank=True)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'db_table': 'vaccine_program',
            },
        ),
        migrations.CreateModel(
            name='VaccineCategory',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('category_name', models.CharField(max_length=100)),
                ('category_type', models.CharField(choices=[('age_group', 'Age Group'), ('dose', 'Dose'), ('target_population', 'Target Population'), ('special', 'Special Category')], max_length=50)),
                ('target_group', models.CharField(choices=[('children', 'Children'), ('pregnant_women', 'Pregnant Women'), ('general', 'General')], default='children', max_length=50)),
                ('age_min', models.IntegerField(blank=True, null=True)),
                ('age_max', models.IntegerField(blank=True, null=True)),
                ('dose_number', models.IntegerField(blank=True, null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('vaccine_program', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='categories', to='immunization.vaccineprogram')),
            ],
            options={
                'db_table': 'vaccine_category',
                'unique_together': {('vaccine_program', 'category_name')},
            },
        ),
        migrations.CreateModel(
            name='ImmunizationDataImport',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('file_name', models.CharField(max_length=255)),
                ('file_size', models.IntegerField()),
                ('time_period', models.DateField()),
                ('total_records', models.IntegerField(default=0)),
                ('processed_records', models.IntegerField(default=0)),
                ('failed_records', models.IntegerField(default=0)),
                ('status', models.CharField(choices=[('pending', 'Pending'), ('processing', 'Processing'), ('completed', 'Completed'), ('completed_with_errors', 'Completed with Error'), ('failed', 'Failed')], default='pending', max_length=50)),
                ('error_message', models.TextField(blank=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('uploaded_by', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='immunization_imports', to=settings.AUTH_USER_MODEL)),
            ],
            options={
                'db_table': 'immunization_data_import',
                'ordering': ['-created_at'],
            },
        ),
        migrations.CreateModel(
            name='ImmunizationData',
            fields=[
                ('id', models.BigAutoField(auto_created=True, primary_key=True, serialize=False, verbose_name='ID')),
                ('time_period', models.DateField()),
                ('children_immunized', models.IntegerField(default=0)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('facility', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='immunization_data', to='facilities.facility')),
                ('vaccine_category', models.ForeignKey(on_delete=django.db.models.deletion.CASCADE, related_name='immunization_data', to='immunization.vaccinecategory')),
            ],
            options={
                'db_table': 'immunization_data',
                'indexes': [models.Index(fields=['time_period'], name='immunizatio_time_pe_39becc_idx'), models.Index(fields=['facility', 'time_period'], name='immunizatio_facilit_03af1e_idx'), models.Index(fields=['vaccine_category', 'time_period'], name='immunizatio_vaccine_1e6c26_idx')],
                'unique_together': {('time_period', 'facility', 'vaccine_category')},
            },
        ),
    ]
