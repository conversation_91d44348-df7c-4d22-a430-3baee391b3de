from rest_framework import serializers
from .models import (
    Vaccine<PERSON>rogram, VaccineCategory, ImmunizationData, 
    ImmunizationDataImport, VaccineStorageCenter
)
from facilities.serializers import FacilitySerializer
from location.serializers import ProvinceSerializer, DistrictSerializer, MunicipalitySerializer


class VaccineProgramSerializer(serializers.ModelSerializer):
    class Meta:
        model = VaccineProgram
        fields = '__all__'


class VaccineCategorySerializer(serializers.ModelSerializer):
    vaccine_program = VaccineProgramSerializer(read_only=True)
    vaccine_program_id = serializers.IntegerField(write_only=True)
    
    class Meta:
        model = VaccineCategory
        fields = '__all__'


class ImmunizationDataSerializer(serializers.ModelSerializer):
    facility = FacilitySerializer(read_only=True)
    facility_id = serializers.IntegerField(write_only=True)
    vaccine_category = VaccineCategorySerializer(read_only=True)
    vaccine_category_id = serializers.IntegerField(write_only=True)
    
    class Meta:
        model = ImmunizationData
        fields = '__all__'


class ImmunizationDataImportSerializer(serializers.ModelSerializer):
    uploaded_by = serializers.StringRelatedField(read_only=True)
    uploaded_by_id = serializers.IntegerField(write_only=True)
    
    class Meta:
        model = ImmunizationDataImport
        fields = '__all__'
        read_only_fields = ['file_size', 'total_records', 'processed_records', 'failed_records', 'status', 'error_message']


class VaccineStorageCenterSerializer(serializers.ModelSerializer):
    country = serializers.StringRelatedField(read_only=True)
    country_id = serializers.IntegerField(write_only=True)
    province = ProvinceSerializer(read_only=True)
    province_id = serializers.IntegerField(write_only=True)
    district = DistrictSerializer(read_only=True)
    district_id = serializers.IntegerField(write_only=True)
    municipality = MunicipalitySerializer(read_only=True)
    municipality_id = serializers.IntegerField(write_only=True)
    
    class Meta:
        model = VaccineStorageCenter
        fields = '__all__'


class ImmunizationDataSummarySerializer(serializers.Serializer):
    """Serializer for immunization data summary/aggregation"""
    time_period = serializers.DateField()
    province = ProvinceSerializer(read_only=True)
    district = DistrictSerializer(read_only=True)
    municipality = MunicipalitySerializer(read_only=True)
    facility = FacilitySerializer(read_only=True)
    vaccine_program = VaccineProgramSerializer(read_only=True)
    vaccine_category = VaccineCategorySerializer(read_only=True)
    children_immunized = serializers.IntegerField()
    total_facilities = serializers.IntegerField()
    total_children = serializers.IntegerField()


class ImmunizationDataUploadSerializer(serializers.Serializer):
    """Serializer for file upload"""
    file = serializers.FileField()
    time_period = serializers.CharField(required=False, help_text="Time period in format 'Feb 2024' or 'February 2024'")
    
    def validate_file(self, value):
        """Validate uploaded file"""
        if not value.name.endswith(('.xlsx', '.xls')):
            raise serializers.ValidationError("Only Excel files (.xlsx, .xls) are allowed")
        
        if value.size > 10 * 1024 * 1024:  # 10MB limit
            raise serializers.ValidationError("File size must be less than 10MB")
        
        return value
