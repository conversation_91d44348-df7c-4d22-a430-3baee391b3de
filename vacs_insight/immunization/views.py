from django.shortcuts import render
from rest_framework import viewsets, status
from rest_framework.decorators import action, api_view
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from django.db.models import Q, Sum, Count, F
from .models import VaccineStorageCenter
from .serializers import VaccineStorageCenterSerializer
from location.models import Province, District, Municipality
from django.core.exceptions import ObjectDoesNotExist
import pandas as pd
import io
from django.db import transaction
from .services import import_vaccine_storage_centers
import os
import tempfile
from django.http import JsonResponse
from django.utils.decorators import method_decorator
from django.views.decorators.csrf import csrf_exempt
from rest_framework import filters
from rest_framework.parsers import MultiPartParser, FormParser
from django_filters.rest_framework import DjangoFilterBackend
from .models import (
    VaccineProgram, VaccineCategory, ImmunizationData, 
    ImmunizationDataImport
)
from .serializers import (
    VaccineProgramSerializer, VaccineCategorySerializer, ImmunizationDataSerializer,
    ImmunizationDataImportSerializer, ImmunizationDataSummarySerializer, ImmunizationDataUploadSerializer
)
from .services import ImmunizationDataImportService
from datetime import datetime, date


class VaccineStorageCenterViewSet(viewsets.ModelViewSet):
    queryset = VaccineStorageCenter.objects.all().select_related('country', 'province', 'district', 'municipality')
    serializer_class = VaccineStorageCenterSerializer
    permission_classes = [IsAuthenticated]
    http_method_names = ['get', 'post']
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['country', 'province', 'district', 'municipality']
    search_fields = ['name']
    ordering_fields = ['name', 'created_at']
    ordering = ['name']

    def get_queryset(self):
        queryset = super().get_queryset()
        
        # Search functionality
        search = self.request.query_params.get('search', '')
        if search:
            queryset = queryset.filter(
                Q(name__icontains=search) |
                Q(province__name__icontains=search) |
                Q(district__name__icontains=search) |
                Q(municipality__name__icontains=search)
            )

        # Filter by location IDs
        province_id = self.request.query_params.get('province_id')
        district_id = self.request.query_params.get('district_id')
        municipality_id = self.request.query_params.get('municipality_id')

        if province_id:
            queryset = queryset.filter(province_id=province_id)
        if district_id:
            queryset = queryset.filter(district_id=district_id)
        if municipality_id:
            queryset = queryset.filter(municipality_id=municipality_id)

        # Enhanced sorting with multiple fields support
        sort_by = self.request.query_params.get('sort_by', 'name')
        sort_order = self.request.query_params.get('sort_order', 'asc')
        
        # Define allowed sort fields
        allowed_sort_fields = {
            'name': 'name',
            'province': 'province__name',
            'district': 'district__name', 
            'municipality': 'municipality__name',
            'country': 'country__name',
            'created_at': 'created_at',
            'updated_at': 'updated_at',
            'latitude': 'latitude',
            'longitude': 'longitude'
        }

        if sort_by in allowed_sort_fields:
            field = allowed_sort_fields[sort_by]
            if sort_order.lower() == 'desc':
                field = f'-{field}'
            queryset = queryset.order_by(field)
        else:
            # Default sorting by name
            queryset = queryset.order_by('name')

        return queryset

    def list(self, request, *args, **kwargs):
        """
        List vaccine storage centers without pagination but with search and count.
        
        Query Parameters:
        - search: Search in name, province, district, municipality
        - province_id: Filter by province ID
        - district_id: Filter by district ID  
        - municipality_id: Filter by municipality ID
        - sort_by: Sort field (name, province, district, municipality, country, created_at, updated_at, latitude, longitude)
        - sort_order: Sort order (asc, desc)
        """
        queryset = self.get_queryset()
        search = request.query_params.get('search', '')
        
        # Get total count before serialization
        total_count = queryset.count()
        
        # Serialize all results
        serializer = self.get_serializer(queryset, many=True)
        
        return Response({
            'count': total_count,
            'search': search,
            'results': serializer.data
        })

    @action(detail=False, methods=['post'], url_path='upload')
    def upload(self, request):
        user = request.user
        # Authority/position check
        if not (getattr(user, 'authority_level', None) == 'center' and getattr(user.position, 'name', None) in ['Admin',
                                                                                                                'Manager']):
            return Response({'detail': 'Permission denied.'}, status=status.HTTP_403_FORBIDDEN)

        file = request.FILES.get('file')
        if not file:
            return Response({'detail': 'No file uploaded.'}, status=status.HTTP_400_BAD_REQUEST)
        is_excel = file.name.endswith('.xlsx')
        success, errors = import_vaccine_storage_centers(file, is_excel=is_excel)
        if not success:
            if errors and errors[0].startswith('Missing required column'):
                return Response({'detail': errors[0]}, status=status.HTTP_400_BAD_REQUEST)
            return Response({'detail': 'Errors in upload', 'errors': errors}, status=status.HTTP_400_BAD_REQUEST)
        return Response({'detail': 'Upload successful.'})


class VaccineProgramViewSet(viewsets.ModelViewSet):
    """ViewSet for vaccine programs"""
    queryset = VaccineProgram.objects.all()
    serializer_class = VaccineProgramSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['is_active']
    search_fields = ['name', 'code', 'description']
    ordering_fields = ['name', 'code', 'created_at']
    ordering = ['name']


class VaccineCategoryViewSet(viewsets.ModelViewSet):
    """ViewSet for vaccine categories"""
    queryset = VaccineCategory.objects.select_related('vaccine_program').all()
    serializer_class = VaccineCategorySerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['vaccine_program', 'category_type', 'target_group', 'is_active']
    search_fields = ['category_name', 'vaccine_program__name']
    ordering_fields = ['category_name', 'vaccine_program__name']
    ordering = ['vaccine_program__name', 'category_name']


class ImmunizationDataViewSet(viewsets.ModelViewSet):
    """ViewSet for immunization data"""
    queryset = ImmunizationData.objects.select_related(
        'facility', 'vaccine_category', 'vaccine_category__vaccine_program'
    ).all()
    serializer_class = ImmunizationDataSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.SearchFilter, filters.OrderingFilter]
    filterset_fields = ['time_period', 'facility', 'vaccine_category', 'vaccine_category__vaccine_program']
    search_fields = ['facility__name', 'vaccine_category__category_name']
    ordering_fields = ['time_period', 'children_immunized', 'created_at']
    ordering = ['-time_period', 'facility__name']

    @action(detail=False, methods=['get'])
    def summary(self, request):
        """Get immunization data summary by various dimensions"""
        queryset = self.get_queryset()
        
        # Apply filters
        time_period = request.query_params.get('time_period')
        province_id = request.query_params.get('province')
        district_id = request.query_params.get('district')
        municipality_id = request.query_params.get('municipality')
        vaccine_program_id = request.query_params.get('vaccine_program')
        
        if time_period:
            queryset = queryset.filter(time_period=time_period)
        if province_id:
            queryset = queryset.filter(facility__province_id=province_id)
        if district_id:
            queryset = queryset.filter(facility__district_id=district_id)
        if municipality_id:
            queryset = queryset.filter(facility__municipality_id=municipality_id)
        if vaccine_program_id:
            queryset = queryset.filter(vaccine_category__vaccine_program_id=vaccine_program_id)
        
        # Aggregate data
        summary = queryset.aggregate(
            total_children_immunized=Sum('children_immunized'),
            total_facilities=Count('facility', distinct=True),
            total_records=Count('id')
        )
        
        return Response({
            'total_children_immunized': summary['total_children_immunized'] or 0,
            'total_facilities': summary['total_facilities'],
            'total_records': summary['total_records']
        })

    @action(detail=False, methods=['get'])
    def by_vaccine_program(self, request):
        """Get immunization data grouped by vaccine program"""
        queryset = self.get_queryset()
        
        # Apply filters
        time_period = request.query_params.get('time_period')
        if time_period:
            queryset = queryset.filter(time_period=time_period)
        
        # Group by vaccine program
        data = queryset.values(
            'vaccine_category__vaccine_program__name',
            'vaccine_category__vaccine_program__code'
        ).annotate(
            total_children=Sum('children_immunized'),
            total_facilities=Count('facility', distinct=True)
        ).order_by('vaccine_category__vaccine_program__name')
        
        return Response(data)

    @action(detail=False, methods=['get'])
    def by_location(self, request):
        """Get immunization data grouped by location"""
        queryset = self.get_queryset()
        
        # Apply filters
        time_period = request.query_params.get('time_period')
        vaccine_program_id = request.query_params.get('vaccine_program')
        
        if time_period:
            queryset = queryset.filter(time_period=time_period)
        if vaccine_program_id:
            queryset = queryset.filter(vaccine_category__vaccine_program_id=vaccine_program_id)
        
        # Group by province
        data = queryset.values(
            'facility__province__name',
            'facility__province__id'
        ).annotate(
            total_children=Sum('children_immunized'),
            total_facilities=Count('facility', distinct=True)
        ).order_by('facility__province__name')
        
        return Response(data)


class ImmunizationDataImportViewSet(viewsets.ModelViewSet):
    """ViewSet for immunization data imports"""
    queryset = ImmunizationDataImport.objects.select_related('uploaded_by').all()
    serializer_class = ImmunizationDataImportSerializer
    permission_classes = [IsAuthenticated]
    filter_backends = [DjangoFilterBackend, filters.OrderingFilter]
    filterset_fields = ['status', 'time_period', 'uploaded_by']
    ordering_fields = ['created_at', 'time_period']
    ordering = ['-created_at']

    @action(detail=False, methods=['post'], parser_classes=[MultiPartParser, FormParser])
    def upload_file(self, request):
        """Upload and import immunization data file"""
        serializer = ImmunizationDataUploadSerializer(data=request.data)
        if serializer.is_valid():
            try:
                uploaded_file = serializer.validated_data['file']
                time_period = serializer.validated_data.get('time_period')
                
                # Save file temporarily
                with tempfile.NamedTemporaryFile(delete=False, suffix='.xlsx') as temp_file:
                    for chunk in uploaded_file.chunks():
                        temp_file.write(chunk)
                    temp_file_path = temp_file.name
                
                try:
                    # Import data
                    import_service = ImmunizationDataImportService()
                    import_record = import_service.import_data(
                        file_path=temp_file_path,
                        uploaded_by=request.user,
                        time_period_str=time_period
                    )
                    
                    return Response({
                        'message': 'File uploaded and processed successfully',
                        'import_id': import_record.id,
                        'status': import_record.status,
                        'processed_records': import_record.processed_records,
                        'failed_records': import_record.failed_records
                    }, status=status.HTTP_201_CREATED)
                    
                finally:
                    # Clean up temporary file
                    if os.path.exists(temp_file_path):
                        os.unlink(temp_file_path)
                        
            except Exception as e:
                return Response({
                    'error': str(e)
                }, status=status.HTTP_400_BAD_REQUEST)
        else:
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)


@method_decorator(csrf_exempt, name='dispatch')
class ImmunizationDataUploadView(viewsets.ViewSet):
    """Legacy upload view for backward compatibility"""
    permission_classes = [IsAuthenticated]
    parser_classes = [MultiPartParser, FormParser]

    def create(self, request):
        """Handle file upload"""
        if 'file' not in request.FILES:
            return Response({'error': 'No file provided'}, status=status.HTTP_400_BAD_REQUEST)
        
        uploaded_file = request.FILES['file']
        time_period = request.data.get('time_period')
        
        # Save file temporarily
        with tempfile.NamedTemporaryFile(delete=False, suffix='.xlsx') as temp_file:
            for chunk in uploaded_file.chunks():
                temp_file.write(chunk)
            temp_file_path = temp_file.name
        
        try:
            # Import data
            import_service = ImmunizationDataImportService()
            import_record = import_service.import_data(
                file_path=temp_file_path,
                uploaded_by=request.user,
                time_period_str=time_period
            )
            
            return JsonResponse({
                'success': True,
                'message': 'File uploaded and processed successfully',
                'import_id': import_record.id,
                'status': import_record.status,
                'processed_records': import_record.processed_records,
                'failed_records': import_record.failed_records
            })
            
        except Exception as e:
            return JsonResponse({
                'success': False,
                'error': str(e)
            }, status=400)
        finally:
            # Clean up temporary file
            if os.path.exists(temp_file_path):
                os.unlink(temp_file_path)


@api_view(['GET'])
def immunization_dataset(request):
    """
    Returns aggregated immunization data grouped by program and category.
    - If 'month' query param is provided (YYYY-MM), filter by that month.
    - If 'facility_id' is provided, filter by facility. Can be either:
      * Integer: filters by facility.facility_id field
      * UUID: filters by facility.id (primary key)
    - If 'province_id' is provided, filter by facility province.
    - If 'district_id' is provided, filter by facility district.
    - If 'municipality_id' is provided, filter by facility municipality.
    - If 'ward_id' is provided, filter by facility ward.
    - If no month is provided, aggregate for the year of the latest entry in the DB.
    - Always returns all vaccine programs and categories, even if no data exists.
    """
    month = request.query_params.get('month')
    facility_id = request.query_params.get('facility_id')
    province_id = request.query_params.get('province_id')
    district_id = request.query_params.get('district_id')
    municipality_id = request.query_params.get('municipality_id')
    ward_id = request.query_params.get('ward_id')
    qs = ImmunizationData.objects.select_related('vaccine_category', 'vaccine_category__vaccine_program', 'facility')
    
    if facility_id:
        try:
            # Try to parse as integer first (facility_id field)
            facility_id_int = int(facility_id)
            qs = qs.filter(facility__facility_id=facility_id_int)
        except (ValueError, TypeError):
            # If not an integer, try to parse as UUID (primary key)
            try:
                import uuid
                uuid.UUID(facility_id)  # Validate UUID format
                qs = qs.filter(facility__id=facility_id)
            except (ValueError, TypeError):
                return Response(
                    {'error': f'Invalid facility_id: {facility_id}. facility_id must be a valid integer or UUID.'}, 
                    status=status.HTTP_400_BAD_REQUEST
                )
    
    # Apply location filters
    if province_id:
        try:
            province_id_int = int(province_id)
            qs = qs.filter(facility__province_id=province_id_int)
        except (ValueError, TypeError):
            return Response(
                {'error': f'Invalid province_id: {province_id}. province_id must be a valid integer.'}, 
                status=status.HTTP_400_BAD_REQUEST
            )
    
    if district_id:
        try:
            district_id_int = int(district_id)
            qs = qs.filter(facility__district_id=district_id_int)
        except (ValueError, TypeError):
            return Response(
                {'error': f'Invalid district_id: {district_id}. district_id must be a valid integer.'}, 
                status=status.HTTP_400_BAD_REQUEST
            )
    
    if municipality_id:
        try:
            municipality_id_int = int(municipality_id)
            qs = qs.filter(facility__municipality_id=municipality_id_int)
        except (ValueError, TypeError):
            return Response(
                {'error': f'Invalid municipality_id: {municipality_id}. municipality_id must be a valid integer.'}, 
                status=status.HTTP_400_BAD_REQUEST
            )
    
    if ward_id:
        try:
            ward_id_int = int(ward_id)
            qs = qs.filter(facility__ward_id=ward_id_int)
        except (ValueError, TypeError):
            return Response(
                {'error': f'Invalid ward_id: {ward_id}. ward_id must be a valid integer.'}, 
                status=status.HTTP_400_BAD_REQUEST
            )
    
    # Determine time period for aggregation
    report_date_range = ""
    if month:
        try:
            # Parse month (e.g., "2024-02" -> 2024-02-01)
            year, month_num = month.split('-')
            start_date = datetime(int(year), int(month_num), 1)
            if int(month_num) == 12:
                end_date = datetime(int(year) + 1, 1, 1)
            else:
                end_date = datetime(int(year), int(month_num) + 1, 1)
            qs = qs.filter(time_period__gte=start_date, time_period__lt=end_date)
            report_date_range = f"{start_date.strftime('%B %Y')}"
        except ValueError:
            return Response(
                {'error': 'Invalid month format. Use YYYY-MM (e.g., 2024-02)'}, 
                status=status.HTTP_400_BAD_REQUEST
            )
    else:
        # Get the year of the latest entry
        latest_entry = ImmunizationData.objects.order_by('-time_period').first()
        if latest_entry:
            year = latest_entry.time_period.year
            qs = qs.filter(time_period__year=year)
            report_date_range = f"Jan {year} - Dec {year}"
        else:
            # No data exists, return empty structure with all programs
            return Response({
                'reportDate': f"No data available (as on {datetime.now().strftime('%d %B %Y %I:%M:%S %p')})",
                'data': _get_all_programs_structure()
            }, status=status.HTTP_200_OK)
    
    # Get aggregated data
    aggregated_data = qs.values(
        'vaccine_category__vaccine_program__name',
        'vaccine_category__vaccine_program__code',
        'vaccine_category__category_name',
        'vaccine_category__category_type'
    ).annotate(
        total=Sum('children_immunized')
    ).order_by('vaccine_category__vaccine_program__name', 'vaccine_category__category_name')
    
    # Get all vaccine programs and categories for complete structure
    all_programs = VaccineProgram.objects.prefetch_related('categories').order_by('name')
    
    result = []
    for program in all_programs:
        program_data = aggregated_data.filter(
            vaccine_category__vaccine_program__name=program.name
        )
        
        # Calculate total for this program
        program_total = sum(item['total'] for item in program_data)
        
        # Build categories list
        categories = []
        for category in program.categories.all():
            category_data = program_data.filter(
                vaccine_category__category_name=category.category_name
            ).first()
            
            category_total = category_data['total'] if category_data else 0
            categories.append({
                'label': f"Immunization Program - {category.category_type.title()} - {program.name} - {category.category_name}",
                'value': category_total
            })
        
        result.append({
            'program': program.name,
            'total': program_total,
            'categories': categories
        })
    
    # Create response with report date
    current_time = datetime.now().strftime('%d %B %Y %I:%M:%S %p')
    response_data = {
        'reportDate': f"{report_date_range} (as on {current_time})",
        'data': result
    }
    
    return Response(response_data, status=status.HTTP_200_OK)


def _get_all_programs_structure():
    """Helper function to return structure with all programs and zero values"""
    all_programs = VaccineProgram.objects.prefetch_related('categories').order_by('name')
    
    result = []
    for program in all_programs:
        categories = []
        for category in program.categories.all():
            categories.append({
                'label': f"Immunization Program - {category.category_type.title()} - {program.name} - {category.category_name}",
                'value': 0
            })
        
        result.append({
            'program': program.name,
            'total': 0,
            'categories': categories
        })
    
    return result
