import pandas as pd
from django.db import transaction
from location.models import Province, District, Municipality
from .models import VaccineStorageCenter
from django.core.exceptions import ObjectDoesNotExist
from rapidfuzz import process
import os
from datetime import datetime
from django.conf import settings
from django.utils import timezone
from rapidfuzz import fuzz

from .models import (
    VaccineProgram, VaccineCategory, ImmunizationData, 
    ImmunizationDataImport
)
from facilities.models import Facility


def fuzzy_match_location(name, queryset, threshold=75, district_match_threshold=50):
    """
    Fuzzy match a location name against a queryset.
    Returns (object, score, matched_name) or (None, 0, None)
    """
    if not name or not queryset.exists():
        return None, 0, None
    
    # Get all names from the queryset
    choices = list(queryset.values_list('name', flat=True))
    
    # Try exact match first (case insensitive)
    try:
        obj = queryset.get(name__iexact=name)
        return obj, 100, obj.name
    except queryset.model.DoesNotExist:
        pass
    
    # Try fuzzy match
    match, score, _ = process.extractOne(name, choices)
    
    # Determine threshold based on context
    # If we're matching municipalities and we have a district context,
    # we can be more lenient since municipalities in same district are unique
    if queryset.model == Municipality and hasattr(queryset, 'filter') and 'district' in queryset.query.where.children:
        # Municipality within a specific district - use lower threshold
        effective_threshold = district_match_threshold
    else:
        # Province or District matching - use higher threshold
        effective_threshold = threshold
    
    if score >= effective_threshold:
        obj = queryset.get(name=match)
        return obj, score, match
    
    return None, score, None


def import_vaccine_storage_centers(file_or_path, is_excel=False):
    """
    Import vaccine storage centers from a file-like object or file path.
    Returns (success: bool, errors: list of str)
    """
    # Read file
    try:
        if hasattr(file_or_path, 'read'):
            if is_excel or (hasattr(file_or_path, 'name') and file_or_path.name.endswith('.xlsx')):
                df = pd.read_excel(file_or_path)
            else:
                df = pd.read_csv(file_or_path)
        else:
            if str(file_or_path).endswith('.xlsx'):
                df = pd.read_excel(file_or_path)
            else:
                df = pd.read_csv(file_or_path)
        # Strip whitespace from column names
        df.columns = [col.strip() for col in df.columns]
    except Exception as e:
        return False, [f'File read error: {str(e)}']

    required_columns = ['Vaccine Storage Centers', 'Province', 'District', 'Municipality', 'Location Coordinates']
    for col in required_columns:
        if col not in df.columns:
            return False, [f'Missing required column: {col}']

    errors = []
    validated_rows = []
    for idx, row in df.iterrows():
        name = str(row['Vaccine Storage Centers']).strip()
        province_name = str(row['Province']).strip()
        district_name = str(row['District']).strip()
        municipality_name = str(row['Municipality']).strip()
        coord = str(row['Location Coordinates']).strip()
        
        try:
            # Province - fuzzy match
            province = None
            if province_name:
                province, score, matched_name = fuzzy_match_location(
                    province_name, Province.objects.all(), threshold=75
                )
                if not province:
                    errors.append(
                        f"Row {idx + 2}: Province not found (best match for '{province_name}' was '{matched_name}' with score {score:.1f}%)")
                    continue
                if score < 100:
                    print(f"Row {idx + 2}: Fuzzy matched province '{province_name}' to '{matched_name}' (score: {score:.1f}%)")
            
            # District - fuzzy match within province
            district = None
            if district_name and province:
                district, score, matched_name = fuzzy_match_location(
                    district_name, District.objects.filter(province=province), threshold=75
                )
                if not district:
                    errors.append(
                        f"Row {idx + 2}: District not found in {province.name} (best match for '{district_name}' was '{matched_name}' with score {score:.1f}%)")
                    continue
                if score < 100:
                    print(f"Row {idx + 2}: Fuzzy matched district '{district_name}' to '{matched_name}' (score: {score:.1f}%)")
            
            # Municipality - fuzzy match within district (more lenient threshold)
            municipality = None
            if municipality_name and district:
                municipality, score, matched_name = fuzzy_match_location(
                    municipality_name, Municipality.objects.filter(district=district), 
                    threshold=50, district_match_threshold=50
                )
                if not municipality:
                    errors.append(
                        f"Row {idx + 2}: Municipality not found in {district.name} (best match for '{municipality_name}' was '{matched_name}' with score {score:.1f}%)")
                    continue
                if score < 100:
                    print(f"Row {idx + 2}: Fuzzy matched municipality '{municipality_name}' to '{matched_name}' (score: {score:.1f}%)")
            
            country = province.country
            lat, lon = parse_coordinates(coord)
            validated_rows.append({
                'name': name,
                'country': country,
                'province': province,
                'district': district,
                'municipality': municipality,
                'latitude': lat,
                'longitude': lon,
            })
        except ObjectDoesNotExist as e:
            errors.append(f'Row {idx + 2}: Location not found ({e})')
        except Exception as e:
            errors.append(f'Row {idx + 2}: {str(e)}')
    
    if errors:
        return False, errors
    
    try:
        with transaction.atomic():
            for row in validated_rows:
                VaccineStorageCenter.objects.update_or_create(
                    name=row['name'],
                    defaults={
                        'country': row['country'],
                        'province': row['province'],
                        'district': row['district'],
                        'municipality': row['municipality'],
                        'latitude': row['latitude'],
                        'longitude': row['longitude'],
                    }
                )
        return True, []
    except Exception as e:
        return False, [f'Upload failed: {str(e)}']


def parse_coordinates(coord_str):
    # Example: 27.1667° N, 84.8933° E
    try:
        lat_str, lon_str = [x.strip() for x in coord_str.split(',')]
        lat = _parse_single_coord(lat_str)
        lon = _parse_single_coord(lon_str)
        return lat, lon
    except Exception:
        raise ValueError(f'Invalid coordinate format: {coord_str}')


def _parse_single_coord(s):
    import re
    match = re.match(r'([\d.]+)[^\d\w]*([NSWE])', s)
    if not match:
        raise ValueError(f'Invalid coordinate: {s}')
    value = float(match.group(1))
    direction = match.group(2)
    if direction in ['S', 'W']:
        value = -value
    return value


class ImmunizationDataImportService:
    """Service to handle immunization data import from Excel files"""
    
    # Column mapping from Excel headers to vaccine programs and categories
    COLUMN_MAPPINGS = {
        'Immunization Program-Children Immunized-Measles/Rubella-12-23 Months': ('MR', '12-23 Months'),
        'Immunization Program-Children Immunized-Measles/Rubella-9-11 Months': ('MR', '9-11 Months'),
        'Immunization Program-Vaccine Type-Children Immunized-BCG Doses': ('BCG', 'Single Dose'),
        'Immunization Program-Vaccine Type-Children Immunized-JE': ('JE', 'Single Dose'),
        'Immunization Program-Vaccine Type-Children Immunized-TD(Pregnant Women)-2': ('TD', '2nd'),
        'Immunization Program-Vaccine Type-Dose-Children Immunized-DPT-HepB-Hib-1st': ('DPT-HepB-Hib', '1st'),
        'Immunization Program-Vaccine Type-Dose-Children Immunized-DPT-HepB-Hib-2nd': ('DPT-HepB-Hib', '2nd'),
        'Immunization Program-Vaccine Type-Dose-Children Immunized-DPT-HepB-Hib-3rd': ('DPT-HepB-Hib', '3rd'),
        'Immunization Program-Vaccine Type-Dose-Children Immunized-FIPV-1st': ('FIPV', '1st'),
        'Immunization Program-Vaccine Type-Dose-Children Immunized-FIPV-2nd': ('FIPV', '2nd'),
        'Immunization Program-Vaccine Type-Dose-Children Immunized-OPV-1st': ('OPV', '1st'),
        'Immunization Program-Vaccine Type-Dose-Children Immunized-OPV-2nd': ('OPV', '2nd'),
        'Immunization Program-Vaccine Type-Dose-Children Immunized-OPV-3rd': ('OPV', '3rd'),
        'Immunization Program-Vaccine Type-Dose-Children Immunized-PCV-1st': ('PCV', '1st'),
        'Immunization Program-Vaccine Type-Dose-Children Immunized-PCV-2nd': ('PCV', '2nd'),
        'Immunization Program-Vaccine Type-Dose-Children Immunized-PCV-3rd': ('PCV', '3rd'),
        'Immunization Program-Vaccine Type-Dose-Children Immunized-Rota-1st': ('Rota', '1st'),
        'Immunization Program-Vaccine Type-Dose-Children Immunized-Rota-2nd': ('Rota', '2nd'),
        'Immunization-Fully immunized-Within 23 months': ('FI', 'Within 23 months'),
        'Immunization-TD-Pregnant Women-1': ('TD', '1st'),
        'Immunization-Vaccine-Children Immunized-TCV': ('TCV', 'Single Dose'),
    }
    
    def __init__(self):
        self.vaccine_programs = {}
        self.vaccine_categories = {}
        self.facilities = {}
        self.provinces = {}
        self.districts = {}
        self.municipalities = {}
        self._load_reference_data()
    
    def _load_reference_data(self):
        """Load all reference data for efficient lookup"""
        # Load vaccine programs and categories
        for program in VaccineProgram.objects.all():
            self.vaccine_programs[program.code] = program
        
        for category in VaccineCategory.objects.all():
            key = (category.vaccine_program.code, category.category_name)
            self.vaccine_categories[key] = category
        
        # Load location data
        for province in Province.objects.all():
            self.provinces[province.name.lower()] = province
        
        for district in District.objects.all():
            key = (district.province.name.lower(), district.name.lower())
            self.districts[key] = district
        
        for municipality in Municipality.objects.all():
            key = (municipality.district.name.lower(), municipality.name.lower())
            self.municipalities[key] = municipality
        
        # Load facilities
        for facility in Facility.objects.all():
            self.facilities[facility.hf_code] = facility
    
    def _parse_time_period(self, time_period_str):
        """Parse time period string to date object"""
        try:
            # Handle formats like "Feb 2024", "February 2024"
            return datetime.strptime(time_period_str, "%b %Y").date()
        except ValueError:
            try:
                return datetime.strptime(time_period_str, "%B %Y").date()
            except ValueError:
                raise ValueError(f"Invalid time period format: {time_period_str}")
    
    def _find_facility(self, row):
        """Find facility using HF_ID or facility name with fuzzy matching"""
        hf_id = row.get('HF_ID')
        facility_name = row.get('Health Facility', '').strip()
        
        # Try to find by HF_ID first
        if hf_id and str(hf_id) in self.facilities:
            return self.facilities[str(hf_id)]
        
        # Try to find by facility name
        if facility_name:
            # Exact match
            for facility in Facility.objects.all():
                if facility.name.lower() == facility_name.lower():
                    return facility
            
            # Fuzzy match
            facility_names = [f.name for f in Facility.objects.all()]
            best_match = process.extractOne(facility_name, facility_names, scorer=fuzz.ratio)
            if best_match and best_match[1] >= 80:
                return Facility.objects.get(name=best_match[0])
        
        return None
    
    def _find_location_entities(self, row):
        """Find province, district, municipality using fuzzy matching"""
        province_name = row.get('Province Name', '').strip()
        district_name = row.get('District Name', '').strip()
        municipality_name = row.get('Municipality Name', '').strip()
        
        # Find province
        province = None
        if province_name:
            province_lower = province_name.lower()
            if province_lower in self.provinces:
                province = self.provinces[province_lower]
            else:
                # Fuzzy match for province
                province_names = [p.name.lower() for p in Province.objects.all()]
                best_match = process.extractOne(province_lower, province_names, scorer=fuzz.ratio)
                if best_match and best_match[1] >= 80:
                    province = Province.objects.get(name__iexact=best_match[0])
        
        # Find district
        district = None
        if district_name and province:
            district_lower = district_name.lower()
            key = (province.name.lower(), district_lower)
            if key in self.districts:
                district = self.districts[key]
            else:
                # Fuzzy match within province
                province_districts = [d.name.lower() for d in District.objects.filter(province=province)]
                best_match = process.extractOne(district_lower, province_districts, scorer=fuzz.ratio)
                if best_match and best_match[1] >= 80:
                    district = District.objects.get(name__iexact=best_match[0], province=province)
        
        # Find municipality
        municipality = None
        if municipality_name and district:
            municipality_lower = municipality_name.lower()
            key = (district.name.lower(), municipality_lower)
            if key in self.municipalities:
                municipality = self.municipalities[key]
            else:
                # Fuzzy match within district
                district_municipalities = [m.name.lower() for m in Municipality.objects.filter(district=district)]
                best_match = process.extractOne(municipality_lower, district_municipalities, scorer=fuzz.ratio)
                if best_match and best_match[1] >= 80:
                    municipality = Municipality.objects.get(name__iexact=best_match[0], district=district)
        
        return province, district, municipality
    
    def import_data(self, file_path, uploaded_by, time_period_str=None):
        """Import immunization data from Excel file"""
        try:
            # Read Excel file
            df = pd.read_excel(file_path)
            print(f"📊 Loaded {len(df)} rows from {file_path}")
            
            # Parse time period
            if time_period_str:
                time_period = self._parse_time_period(time_period_str)
            else:
                # Try to get from first row
                first_time_period = df.iloc[0]['Time Period']
                time_period = self._parse_time_period(first_time_period)
            
            # Create import record
            import_record = ImmunizationDataImport.objects.create(
                file_name=os.path.basename(file_path),
                file_size=os.path.getsize(file_path),
                uploaded_by=uploaded_by,
                time_period=time_period,
                total_records=len(df),
                status='processing'
            )
            
            processed_count = 0
            failed_count = 0
            errors = []
            
            with transaction.atomic():
                for index, row in df.iterrows():
                    try:
                        # Find facility
                        facility = self._find_facility(row)
                        if not facility:
                            failed_count += 1
                            errors.append(f"Row {index}: Facility not found - HF_ID: {row.get('HF_ID')}, Name: {row.get('Health Facility')}")
                            continue
                        
                        # Process each vaccine column
                        for column_name, value in row.items():
                            if column_name in self.COLUMN_MAPPINGS and pd.notna(value):
                                try:
                                    vaccine_code, category_name = self.COLUMN_MAPPINGS[column_name]
                                    category_key = (vaccine_code, category_name)
                                    
                                    if category_key not in self.vaccine_categories:
                                        errors.append(f"Row {index}: Vaccine category not found - {vaccine_code}:{category_name}")
                                        continue
                                    
                                    vaccine_category = self.vaccine_categories[category_key]
                                    children_immunized = int(value) if pd.notna(value) else 0
                                    
                                    # Create or update immunization data
                                    immunization_data, created = ImmunizationData.objects.update_or_create(
                                        time_period=time_period,
                                        facility=facility,
                                        vaccine_category=vaccine_category,
                                        defaults={'children_immunized': children_immunized}
                                    )
                                    
                                    processed_count += 1
                                    
                                except (ValueError, TypeError) as e:
                                    errors.append(f"Row {index}, Column {column_name}: Invalid value '{value}' - {str(e)}")
                                    failed_count += 1
                    
                    except Exception as e:
                        failed_count += 1
                        errors.append(f"Row {index}: {str(e)}")
                
                # Update import record
                import_record.processed_records = processed_count
                import_record.failed_records = failed_count
                import_record.status = 'completed' if failed_count == 0 else 'completed_with_errors'
                if errors:
                    import_record.error_message = '\n'.join(errors[:10])  # Store first 10 errors
                import_record.save()
            
            print(f"✅ Import completed: {processed_count} records processed, {failed_count} failed")
            return import_record
            
        except Exception as e:
            if 'import_record' in locals():
                import_record.status = 'failed'
                import_record.error_message = str(e)
                import_record.save()
            raise e
