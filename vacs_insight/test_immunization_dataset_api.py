#!/usr/bin/env python3
"""
Test script to demonstrate the immunization dataset API with reportDate field
"""

import requests
import json
import sys

# Configuration
BASE_URL = "http://localhost:8000"
API_BASE = f"{BASE_URL}/api/v1"

def get_auth_token():
    """Get authentication token"""
    login_url = f"{API_BASE}/user/auth/login"
    login_data = {
        "username": "admin",
        "password": "admin123"
    }
    
    try:
        response = requests.post(login_url, json=login_data)
        response.raise_for_status()
        return response.json().get('accessToken')
    except requests.exceptions.RequestException as e:
        print(f"Error getting auth token: {e}")
        return None

def test_immunization_dataset_api():
    """Test the immunization dataset API"""
    token = get_auth_token()
    if not token:
        print("❌ Failed to get authentication token")
        return
    
    headers = {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }
    
    print("🏥 Testing Immunization Dataset API")
    print("=" * 50)
    
    # Test 1: Yearly aggregation (latest year)
    print("\n📊 Test 1: Yearly aggregation (latest year)")
    print("-" * 40)
    url = f"{API_BASE}/immunization/dataset"
    response = requests.get(url, headers=headers)
    
    if response.status_code == 200:
        data = response.json()
        print(f"✅ Status: {response.status_code}")
        print(f"📅 Report Date: {data.get('reportDate', 'N/A')}")
        print(f"📈 Programs: {len(data.get('data', []))}")
        
        # Show first few programs
        for i, program in enumerate(data.get('data', [])[:3]):
            print(f"  {i+1}. {program['program']}: {program['total']} immunizations")
            if program['categories']:
                print(f"     Categories: {len(program['categories'])}")
    else:
        print(f"❌ Error: {response.status_code} - {response.text}")
    
    # Test 2: Monthly aggregation
    print("\n📊 Test 2: Monthly aggregation (February 2024)")
    print("-" * 40)
    url = f"{API_BASE}/immunization/dataset?month=2024-02"
    response = requests.get(url, headers=headers)
    
    if response.status_code == 200:
        data = response.json()
        print(f"✅ Status: {response.status_code}")
        print(f"📅 Report Date: {data.get('reportDate', 'N/A')}")
        print(f"📈 Programs: {len(data.get('data', []))}")
        
        # Show first few programs
        for i, program in enumerate(data.get('data', [])[:3]):
            print(f"  {i+1}. {program['program']}: {program['total']} immunizations")
            if program['categories']:
                print(f"     Categories: {len(program['categories'])}")
    else:
        print(f"❌ Error: {response.status_code} - {response.text}")
    
    # Test 3: Facility-specific data
    print("\n📊 Test 3: Facility-specific data")
    print("-" * 40)
    # Use a sample facility ID (you may need to adjust this)
    facility_id = 1001  # Adjust based on your data
    url = f"{API_BASE}/immunization/dataset?facility_id={facility_id}"
    response = requests.get(url, headers=headers)
    
    if response.status_code == 200:
        data = response.json()
        print(f"✅ Status: {response.status_code}")
        print(f"📅 Report Date: {data.get('reportDate', 'N/A')}")
        print(f"📈 Programs: {len(data.get('data', []))}")
        
        # Show first few programs
        for i, program in enumerate(data.get('data', [])[:3]):
            print(f"  {i+1}. {program['program']}: {program['total']} immunizations")
            if program['categories']:
                print(f"     Categories: {len(program['categories'])}")
    else:
        print(f"❌ Error: {response.status_code} - {response.text}")
    
    # Test 4: Combined filters
    print("\n📊 Test 4: Combined filters (facility + month)")
    print("-" * 40)
    url = f"{API_BASE}/immunization/dataset?facility_id={facility_id}&month=2024-02"
    response = requests.get(url, headers=headers)
    
    if response.status_code == 200:
        data = response.json()
        print(f"✅ Status: {response.status_code}")
        print(f"📅 Report Date: {data.get('reportDate', 'N/A')}")
        print(f"📈 Programs: {len(data.get('data', []))}")
        
        # Show first few programs
        for i, program in enumerate(data.get('data', [])[:3]):
            print(f"  {i+1}. {program['program']}: {program['total']} immunizations")
            if program['categories']:
                print(f"     Categories: {len(program['categories'])}")
    else:
        print(f"❌ Error: {response.status_code} - {response.text}")

if __name__ == "__main__":
    test_immunization_dataset_api() 