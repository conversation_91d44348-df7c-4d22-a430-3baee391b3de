#!/bin/bash

# VacsInsight Docker Management Script
# This script provides easy commands to manage the VacsInsight Docker environment

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Function to check if Dock<PERSON> is running
check_docker() {
    if ! docker info > /dev/null 2>&1; then
        print_error "Docker is not running. Please start Docker and try again."
        exit 1
    fi
}

# Function to check if .env file exists
check_env_file() {
    if [ ! -f ".env" ]; then
        print_error ".env file not found. Please copy env.example to .env and configure it."
        exit 1
    fi
}

# Function to show usage
show_usage() {
    echo "VacsInsight Docker Management Script"
    echo ""
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  start       Start all services"
    echo "  stop        Stop all services"
    echo "  restart     Restart all services"
    echo "  build       Build and start all services"
    echo "  rebuild     Rebuild all containers and start services"
    echo "  logs        Show logs from all services"
    echo "  logs-backend Show logs from backend service"
    echo "  logs-database Show logs from database service"
    echo "  logs-redis  Show logs from redis service"
    echo "  shell       Open shell in backend container"
    echo "  db-shell    Open PostgreSQL shell"
    echo "  migrate     Run Django migrations"
    echo "  collectstatic Collect static files"
    echo "  createsuperuser Create Django superuser"
    echo "  test        Run Django tests"
    echo "  clean       Stop and remove all containers, networks, and volumes"
    echo "  status      Show status of all services"
    echo "  health      Check health of all services"
    echo "  help        Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 start"
    echo "  $0 build"
    echo "  $0 logs"
    echo "  $0 shell"
}

# Function to start services
start_services() {
    print_status "Starting VacsInsight services..."
    docker-compose up -d
    print_success "Services started successfully!"
    print_status "Backend: http://localhost"
    print_status "Database: localhost:5432"
    print_status "Redis: localhost:6379"
}

# Function to stop services
stop_services() {
    print_status "Stopping VacsInsight services..."
    docker-compose down
    print_success "Services stopped successfully!"
}

# Function to restart services
restart_services() {
    print_status "Restarting VacsInsight services..."
    docker-compose restart
    print_success "Services restarted successfully!"
}

# Function to build and start services
build_services() {
    print_status "Building and starting VacsInsight services..."
    docker-compose up -d --build
    print_success "Services built and started successfully!"
}

# Function to rebuild everything
rebuild_services() {
    print_status "Rebuilding all containers..."
    docker-compose down -v
    docker-compose up -d --build
    print_success "Services rebuilt and started successfully!"
}

# Function to show logs
show_logs() {
    print_status "Showing logs from all services..."
    docker-compose logs -f
}

# Function to show backend logs
show_backend_logs() {
    print_status "Showing backend logs..."
    docker-compose logs -f backend
}

# Function to show database logs
show_database_logs() {
    print_status "Showing database logs..."
    docker-compose logs -f database
}

# Function to show redis logs
show_redis_logs() {
    print_status "Showing redis logs..."
    docker-compose logs -f redis
}

# Function to open shell in backend
open_shell() {
    print_status "Opening shell in backend container..."
    docker-compose exec backend bash
}

# Function to open database shell
open_db_shell() {
    print_status "Opening PostgreSQL shell..."
    docker-compose exec database psql -U ${DB_USER:-healthscope_user} -d ${DB_NAME:-healthscope_dev}
}

# Function to run migrations
run_migrations() {
    print_status "Running Django migrations..."
    docker-compose exec backend python manage.py migrate
    print_success "Migrations completed successfully!"
}

# Function to collect static files
collect_static() {
    print_status "Collecting static files..."
    docker-compose exec backend python manage.py collectstatic --noinput
    print_success "Static files collected successfully!"
}

# Function to create superuser
create_superuser() {
    print_status "Creating Django superuser..."
    docker-compose exec backend python manage.py createsuperuser
}

# Function to run tests
run_tests() {
    print_status "Running Django tests..."
    docker-compose exec backend python manage.py test
}

# Function to clean everything
clean_all() {
    print_warning "This will remove all containers, networks, and volumes. Are you sure? (y/N)"
    read -r response
    if [[ "$response" =~ ^([yY][eE][sS]|[yY])$ ]]; then
        print_status "Cleaning all Docker resources..."
        docker-compose down -v --remove-orphans
        docker system prune -f
        print_success "Cleanup completed successfully!"
    else
        print_status "Cleanup cancelled."
    fi
}

# Function to show status
show_status() {
    print_status "Showing service status..."
    docker-compose ps
}

# Function to check health
check_health() {
    print_status "Checking service health..."
    
    # Check if containers are running
    if docker-compose ps | grep -q "Up"; then
        print_success "All services are running!"
        
        # Check backend health
        if curl -f http://localhost/health/ > /dev/null 2>&1; then
            print_success "Backend is healthy!"
        else
            print_warning "Backend health check failed"
        fi
        
        # Check database health
        if docker-compose exec database pg_isready -U ${DB_USER:-healthscope_user} -d ${DB_NAME:-healthscope_dev} > /dev/null 2>&1; then
            print_success "Database is healthy!"
        else
            print_warning "Database health check failed"
        fi
        
        # Check redis health
        if docker-compose exec redis redis-cli ping > /dev/null 2>&1; then
            print_success "Redis is healthy!"
        else
            print_warning "Redis health check failed"
        fi
    else
        print_error "Some services are not running. Use '$0 status' to check."
    fi
}

# Main script logic
main() {
    # Check if Docker is running
    check_docker
    
    # Check if .env file exists
    check_env_file
    
    # Parse command
    case "${1:-help}" in
        start)
            start_services
            ;;
        stop)
            stop_services
            ;;
        restart)
            restart_services
            ;;
        build)
            build_services
            ;;
        rebuild)
            rebuild_services
            ;;
        logs)
            show_logs
            ;;
        logs-backend)
            show_backend_logs
            ;;
        logs-database)
            show_database_logs
            ;;
        logs-redis)
            show_redis_logs
            ;;
        shell)
            open_shell
            ;;
        db-shell)
            open_db_shell
            ;;
        migrate)
            run_migrations
            ;;
        collectstatic)
            collect_static
            ;;
        createsuperuser)
            create_superuser
            ;;
        test)
            run_tests
            ;;
        clean)
            clean_all
            ;;
        status)
            show_status
            ;;
        health)
            check_health
            ;;
        help|--help|-h)
            show_usage
            ;;
        *)
            print_error "Unknown command: $1"
            echo ""
            show_usage
            exit 1
            ;;
    esac
}

# Run main function with all arguments
main "$@" 