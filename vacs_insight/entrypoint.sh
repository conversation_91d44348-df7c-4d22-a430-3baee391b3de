#!/bin/bash

# Exit on any error
set -e

echo "Starting HealthScope application..."

# Wait for database to be ready
echo "Waiting for database..."
while ! python manage.py check --database default 2>&1; do
    echo "Database is not ready yet. Waiting..."
    sleep 2
done

# Run migrations
echo "Running database migrations..."
python manage.py migrate --noinput

# Collect static files
echo "Collecting static files..."
python manage.py collectstatic --noinput

# Start the application
echo "Starting Gunicorn..."
exec gunicorn --bind 0.0.0.0:8000 --workers 3 --timeout 120 healthscope.wsgi:application 