# Authority Location Implementation

## Overview

This implementation adds hierarchical access control to the HealthScope backend based on user authority levels and location assignments. Users are now restricted to accessing data only within their assigned location scope.

## Changes Made

### 1. User Model Updates (`user/models.py`)

#### New Field Added:
- `authority_location_id`: PositiveIntegerField with default=1
  - Stores the location ID based on the user's authority level
  - Not null field with validation

#### Authority Level to Location ID Mapping:
- **center** → country_id (always 1)
- **province** → province_id
- **district** → district_id  
- **municipality** → municipality_id
- **ward** → ward_id

#### New Methods Added:
- `clean()`: Validates authority_location_id matches authority_level and exists in corresponding location table
- `get_accessible_location_ids()`: Returns dict of accessible location IDs based on user's authority level

#### Validation Logic:
- Center level users must have authority_location_id = 1
- Other authority levels must have a valid location ID that exists in the corresponding table
- All location IDs must reference active locations

### 2. Location API Updates (`location/views.py`)

#### Removed Operations:
- All create, update, delete operations removed from location ViewSets
- Only read operations (list, retrieve) remain
- This prevents unauthorized location modifications

#### Added Access Control:
- Each ViewSet now implements `get_queryset()` method
- Filters data based on user's accessible locations
- Uses `user.get_accessible_location_ids()` to determine access scope

#### Updated Endpoints:
- `/location/flat`: Returns only locations user has access to
- `/location/hierarchy`: Returns hierarchical data filtered by user access
- Individual location endpoints (province, district, municipality, ward): Filtered by access

### 3. User Serializer Updates (`user/serializers.py`)

#### Added Field:
- `authority_location_id` added to all user serializers
- Validation added to ensure location ID matches authority level

#### Updated Serializers:
- `UserSerializer`
- `UserCreateSerializer`
- `UserUpdateSerializer`
- `UserProfileSerializer`
- `UserListSerializer`

### 4. Migration Updates

#### Consolidated Migration:
- **File**: `user/migrations/0004_authority_level_and_location_implementation.py`
- **Changes**:
  - Removes old `role` field
  - Adds `authority_level` field with choices
  - Adds `authority_location_id` field with default=1
  - Updates `status` field choices
  - Updates `organization` field with default=1
  - Creates index on (authority_level, authority_location_id)
  - Creates initial admin user with center authority level and authority_location_id=1

#### Migration Cleanup:
- Removed `0004_rename_role_to_authority_level.py`
- Removed `0005_user_authority_location_id_alter_user_organization_and_more.py`
- Updated `0003_create_initial_data.py` to be empty (data creation moved to 0004)

## Access Control Logic

### Center Level Users
- Can access all locations (provinces, districts, municipalities, wards)
- authority_location_id = 1

### Province Level Users  
- Can access their assigned province and all districts, municipalities, wards within it
- authority_location_id = province_id

### District Level Users
- Can access their assigned district and all municipalities, wards within it
- authority_location_id = district_id

### Municipality Level Users
- Can access their assigned municipality and all wards within it
- authority_location_id = municipality_id

### Ward Level Users
- Can only access their assigned ward
- authority_location_id = ward_id

## Usage Examples

### Creating a User with Authority Location
```python
# Province level user
user = User.objects.create(
    username="province_user",
    email="<EMAIL>",
    authority_level="province",
    authority_location_id=1,  # Province ID 1
    # ... other fields
)

# District level user  
user = User.objects.create(
    username="district_user", 
    email="<EMAIL>",
    authority_level="district",
    authority_location_id=101,  # District ID 101
    # ... other fields
)
```

### Checking User Access
```python
# Get all accessible location IDs
accessible = user.get_accessible_location_ids()
# Returns: {'provinces': [1], 'districts': [101, 102], 'municipalities': [...], 'wards': [...]}

# API calls will automatically filter based on these accessible locations
```

## Security Benefits

1. **Data Isolation**: Users can only access data within their authority scope
2. **Prevents Unauthorized Access**: API endpoints automatically filter results
3. **Audit Trail**: All access is logged with user context
4. **No Location Modification**: Create/update/delete operations removed from location APIs

## Migration Instructions

1. Apply the consolidated migration:
   ```bash
   python manage.py migrate user
   ```

2. The migration will automatically:
   - Create the authority_level and authority_location_id fields
   - Set up proper indexes
   - Create initial admin user with center authority level and authority_location_id=1
   - Create default organization and user positions

3. For existing users (if any), update their authority_location_id values based on their roles

## API Behavior Changes

### Before Implementation:
- All users could access all location data
- Location data could be modified via API
- No hierarchical access control

### After Implementation:
- Users only see locations within their authority scope
- Location data is read-only via API
- Hierarchical access control enforced at API level

## Testing Recommendations

1. Test each authority level with appropriate location assignments
2. Verify API endpoints return only accessible data
3. Test validation of authority_location_id against authority_level
4. Verify center level users can access all data
5. Test edge cases with invalid location IDs

## Future Considerations

1. **Location Changes**: If location data needs to be updated, use Django migrations
2. **User Migration**: Bulk update users with correct authority_location_id values
3. **Performance**: Monitor query performance with large datasets
4. **Caching**: Consider caching accessible location IDs for performance 