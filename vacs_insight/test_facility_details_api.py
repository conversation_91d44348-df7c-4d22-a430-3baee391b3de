#!/usr/bin/env python3
"""
Test script to verify facility details API includes all missing fields
"""

import requests
import json
import sys

# Configuration
BASE_URL = "http://localhost:8000"
API_BASE = f"{BASE_URL}/api/v1"

def get_auth_token():
    """Get authentication token"""
    login_url = f"{API_BASE}/user/auth/login"
    login_data = {
        "username": "admin",
        "password": "admin123"
    }
    
    try:
        response = requests.post(login_url, json=login_data)
        response.raise_for_status()
        return response.json().get('access')
    except requests.exceptions.RequestException as e:
        print(f"Error getting auth token: {e}")
        return None

def test_facility_details_api():
    """Test facility details API for missing fields"""
    token = get_auth_token()
    if not token:
        print("Failed to get authentication token")
        return False
    
    headers = {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }
    
    # First get a list of facilities to get an ID
    list_url = f"{API_BASE}/facility/"
    try:
        response = requests.get(list_url, headers=headers)
        response.raise_for_status()
        facilities = response.json()
        
        if not facilities.get('results'):
            print("No facilities found in the system")
            return False
        
        # Get the first facility ID
        facility_id = facilities['results'][0]['id']
        print(f"Testing with facility ID: {facility_id}")
        
        # Get facility details
        detail_url = f"{API_BASE}/facility/{facility_id}/"
        response = requests.get(detail_url, headers=headers)
        response.raise_for_status()
        facility_detail = response.json()
        
        # Check for required fields
        required_fields = [
            'operational_status',
            'ownership',
            'facility_level',
            'hmis_code',
            'email',
            'website',
            'telephone',
            'contact_person',
            'contact_person_mobile',
            'oxygen',
            'plant_capacity',
            'cylinder',
            'concentrator',
            'ambulance',
            'ambulance_category',
            'ambulance_contact',
            'internet_facility',
            'established_date_bs'
        ]
        
        missing_fields = []
        for field in required_fields:
            if field not in facility_detail:
                missing_fields.append(field)
        
        if missing_fields:
            print(f"❌ Missing fields in facility details API: {missing_fields}")
            return False
        
        # Check that ownership and facility_level are objects, not strings
        if isinstance(facility_detail.get('ownership'), str):
            print("❌ ownership field should be an object, not a string")
            return False
        
        if isinstance(facility_detail.get('facility_level'), str):
            print("❌ facility_level field should be an object, not a string")
            return False
        
        print("✅ All required fields are present in facility details API")
        print(f"✅ ownership: {facility_detail.get('ownership')}")
        print(f"✅ facility_level: {facility_detail.get('facility_level')}")
        print(f"✅ operational_status: {facility_detail.get('operational_status')}")
        
        return True
        
    except requests.exceptions.RequestException as e:
        print(f"Error testing facility details API: {e}")
        return False

def test_facility_list_api():
    """Test facility list API for missing fields"""
    token = get_auth_token()
    if not token:
        print("Failed to get authentication token")
        return False
    
    headers = {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }
    
    list_url = f"{API_BASE}/facility/"
    try:
        response = requests.get(list_url, headers=headers)
        response.raise_for_status()
        facilities = response.json()
        
        if not facilities.get('results'):
            print("No facilities found in the system")
            return False
        
        # Check first facility for required fields
        first_facility = facilities['results'][0]
        
        required_fields = [
            'ownership',
            'operational_status',
            'hmis_code'
        ]
        
        missing_fields = []
        for field in required_fields:
            if field not in first_facility:
                missing_fields.append(field)
        
        if missing_fields:
            print(f"❌ Missing fields in facility list API: {missing_fields}")
            return False
        
        # Check that ownership is an object, not a string
        if isinstance(first_facility.get('ownership'), str):
            print("❌ ownership field should be an object, not a string")
            return False
        
        print("✅ All required fields are present in facility list API")
        print(f"✅ ownership: {first_facility.get('ownership')}")
        print(f"✅ operational_status: {first_facility.get('operational_status')}")
        
        return True
        
    except requests.exceptions.RequestException as e:
        print(f"Error testing facility list API: {e}")
        return False

def test_ownership_filter():
    """Test ownership filter"""
    token = get_auth_token()
    if not token:
        print("Failed to get authentication token")
        return False
    
    headers = {
        'Authorization': f'Bearer {token}',
        'Content-Type': 'application/json'
    }
    
    # Test with ownership_id filter
    list_url = f"{API_BASE}/facility/?ownership_id=1"
    try:
        response = requests.get(list_url, headers=headers)
        response.raise_for_status()
        facilities = response.json()
        
        print(f"✅ Ownership filter works: {facilities.get('count', 0)} facilities found")
        return True
        
    except requests.exceptions.RequestException as e:
        print(f"Error testing ownership filter: {e}")
        return False

if __name__ == "__main__":
    print("Testing Facility API with missing fields...")
    print("=" * 50)
    
    success = True
    
    print("\n1. Testing Facility Details API...")
    if not test_facility_details_api():
        success = False
    
    print("\n2. Testing Facility List API...")
    if not test_facility_list_api():
        success = False
    
    print("\n3. Testing Ownership Filter...")
    if not test_ownership_filter():
        success = False
    
    print("\n" + "=" * 50)
    if success:
        print("✅ All tests passed! Facility API now includes all missing fields.")
    else:
        print("❌ Some tests failed. Please check the implementation.")
        sys.exit(1) 