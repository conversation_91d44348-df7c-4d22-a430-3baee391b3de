# Generated by Django 4.2.10 on 2025-06-29 17:26

from django.conf import settings
from django.db import migrations, models
import django.db.models.deletion
import uuid


class Migration(migrations.Migration):

    initial = True

    dependencies = [
        migrations.swappable_dependency(settings.AUTH_USER_MODEL),
        ('location', '0006_initial_wards'),
    ]

    operations = [
        migrations.CreateModel(
            name='AuthorityLevel',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=50, unique=True)),
                ('code', models.CharField(max_length=20, unique=True)),
                ('description', models.TextField(blank=True, null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Authority Level',
                'verbose_name_plural': 'Authority Levels',
                'db_table': 'authority_level',
            },
        ),
        migrations.CreateModel(
            name='FacilityLevel',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=100, unique=True)),
                ('code', models.CharField(max_length=20, unique=True)),
                ('description', models.TextField(blank=True, null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Facility Level',
                'verbose_name_plural': 'Facility Levels',
                'db_table': 'facility_level',
            },
        ),
        migrations.CreateModel(
            name='FacilityType',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=255)),
                ('code', models.CharField(max_length=50, unique=True)),
                ('description', models.TextField(blank=True, null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Facility Type',
                'verbose_name_plural': 'Facility Types',
                'db_table': 'facility_type',
            },
        ),
        migrations.CreateModel(
            name='Ownership',
            fields=[
                ('id', models.AutoField(primary_key=True, serialize=False)),
                ('name', models.CharField(max_length=100, unique=True)),
                ('code', models.CharField(max_length=20, unique=True)),
                ('description', models.TextField(blank=True, null=True)),
                ('is_active', models.BooleanField(default=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
            ],
            options={
                'verbose_name': 'Ownership',
                'verbose_name_plural': 'Ownerships',
                'db_table': 'ownership',
            },
        ),
        migrations.CreateModel(
            name='Facility',
            fields=[
                ('uuid', models.UUIDField(default=uuid.uuid4, editable=False, unique=True)),
                ('created_at', models.DateTimeField(auto_now_add=True)),
                ('updated_at', models.DateTimeField(auto_now=True)),
                ('deleted_at', models.DateTimeField(blank=True, null=True)),
                ('id', models.UUIDField(default=uuid.uuid4, editable=False, primary_key=True, serialize=False)),
                ('facility_id', models.IntegerField(unique=True)),
                ('name', models.CharField(max_length=255)),
                ('hf_code', models.CharField(max_length=50, unique=True)),
                ('latitude', models.DecimalField(blank=True, decimal_places=6, max_digits=9, null=True)),
                ('longitude', models.DecimalField(blank=True, decimal_places=6, max_digits=9, null=True)),
                ('established_date_bs', models.CharField(blank=True, help_text='Establishment date in Bikram Sambat', max_length=20, null=True)),
                ('operational_status', models.CharField(blank=True, help_text='Operational status of the facility', max_length=50, null=True)),
                ('internet_facility', models.CharField(blank=True, choices=[('yes', 'Yes'), ('no', 'No'), ('n/a', 'N/A')], max_length=3, null=True)),
                ('hmis_code', models.CharField(blank=True, max_length=50, null=True)),
                ('email', models.EmailField(blank=True, max_length=254, null=True)),
                ('website', models.URLField(blank=True, null=True)),
                ('telephone', models.CharField(blank=True, max_length=20, null=True)),
                ('contact_person', models.CharField(blank=True, max_length=100, null=True)),
                ('contact_person_mobile', models.CharField(blank=True, max_length=20, null=True)),
                ('oxygen', models.CharField(blank=True, help_text='Oxygen availability', max_length=10, null=True)),
                ('plant_capacity', models.CharField(blank=True, help_text='Oxygen plant capacity', max_length=50, null=True)),
                ('cylinder', models.CharField(blank=True, help_text='Cylinder availability', max_length=10, null=True)),
                ('concentrator', models.CharField(blank=True, help_text='Concentrator availability', max_length=10, null=True)),
                ('ambulance', models.CharField(blank=True, choices=[('yes', 'Yes'), ('no', 'No')], max_length=3, null=True)),
                ('ambulance_category', models.CharField(blank=True, choices=[('A', 'Category A'), ('B', 'Category B'), ('C', 'Category C')], max_length=1, null=True)),
                ('ambulance_contact', models.CharField(blank=True, max_length=20, null=True)),
                ('parent_facility_nin', models.CharField(blank=True, max_length=50, null=True)),
                ('fru_d', models.BooleanField(default=False)),
                ('fru_f', models.BooleanField(default=False)),
                ('dp_d', models.BooleanField(default=False)),
                ('dp_f', models.BooleanField(default=False)),
                ('hwc_d', models.BooleanField(default=False)),
                ('hwc_f', models.BooleanField(default=False)),
                ('nbsu_d', models.BooleanField(default=False)),
                ('nbsu_f', models.BooleanField(default=False)),
                ('sncu_d', models.BooleanField(default=False)),
                ('sncu_f', models.BooleanField(default=False)),
                ('bed_count', models.IntegerField(blank=True, null=True)),
                ('functional_bed_count', models.IntegerField(blank=True, null=True)),
                ('authority', models.CharField(max_length=100)),
                ('is_active', models.BooleanField(default=True)),
                ('authority_level', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='facilities.authoritylevel')),
                ('country', models.ForeignKey(default=1, on_delete=django.db.models.deletion.PROTECT, to='location.country')),
                ('created_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_created', to=settings.AUTH_USER_MODEL)),
                ('deleted_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_deleted', to=settings.AUTH_USER_MODEL)),
                ('district', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='location.district')),
                ('facility_level', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, to='facilities.facilitylevel')),
                ('facility_type', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='facilities.facilitytype')),
                ('municipality', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='location.municipality')),
                ('ownership', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.PROTECT, to='facilities.ownership')),
                ('parent_facility_type', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='child_facilities', to='facilities.facilitytype')),
                ('province', models.ForeignKey(on_delete=django.db.models.deletion.PROTECT, to='location.province')),
                ('updated_by', models.ForeignKey(blank=True, null=True, on_delete=django.db.models.deletion.SET_NULL, related_name='%(class)s_updated', to=settings.AUTH_USER_MODEL)),
                ('ward', models.ForeignKey(blank=True, on_delete=django.db.models.deletion.PROTECT, to='location.ward')),
            ],
            options={
                'verbose_name': 'Facility',
                'verbose_name_plural': 'Facilities',
                'db_table': 'facility',
                'indexes': [models.Index(fields=['facility_id'], name='facility_facilit_638f96_idx'), models.Index(fields=['hf_code'], name='facility_hf_code_4259ab_idx'), models.Index(fields=['is_active'], name='facility_is_acti_b16253_idx'), models.Index(fields=['country', 'province', 'district', 'municipality', 'ward'], name='facility_country_75c45c_idx')],
            },
        ),
    ]
