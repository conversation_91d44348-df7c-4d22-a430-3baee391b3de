from django.db import migrations
import pandas as pd
import os
from django.conf import settings as django_settings
from rapidfuzz import fuzz, process


def import_v1_nhfr_facilities(apps, schema_editor):
    Facility = apps.get_model('facilities', 'Facility')
    FacilityType = apps.get_model('facilities', 'FacilityType')
    AuthorityLevel = apps.get_model('facilities', 'AuthorityLevel')
    Ownership = apps.get_model('facilities', 'Ownership')
    FacilityLevel = apps.get_model('facilities', 'FacilityLevel')
    Province = apps.get_model('location', 'Province')
    District = apps.get_model('location', 'District')
    Municipality = apps.get_model('location', 'Municipality')
    Ward = apps.get_model('location', 'Ward')
    
    # Try V1 file first
    v1_file_path = 'facilities/migrations/files/V1_Health_Facility_Registry.xlsx'
    
    if not os.path.exists(v1_file_path):
        print(f"❌ V1 file not found: {v1_file_path}")
        return
    
    print(f"📁 Reading V1 file: {os.path.abspath(v1_file_path)}")
    df = pd.read_excel(v1_file_path)
    print(f"📊 Loaded {len(df)} records from V1 file")
    print(f"📋 Columns: {list(df.columns)}")
    
    # Get all reference data
    provinces = list(Province.objects.all())
    districts = list(District.objects.all())
    municipalities = list(Municipality.objects.all())
    wards = list(Ward.objects.all())
    facility_types = list(FacilityType.objects.all())
    authority_levels = list(AuthorityLevel.objects.all())
    ownerships = list(Ownership.objects.all())
    facility_levels = list(FacilityLevel.objects.all())
    
    print(f"\n🗺️ Available data:")
    print(f"  Provinces: {len(provinces)}")
    print(f"  Districts: {len(districts)}")
    print(f"  Municipalities: {len(municipalities)}")
    print(f"  Wards: {len(wards)}")
    print(f"  Facility Types: {len(facility_types)}")
    print(f"  Authority Levels: {len(authority_levels)}")
    print(f"  Ownerships: {len(ownerships)}")
    print(f"  Facility Levels: {len(facility_levels)}")
    
    # Special handling for Rukum districts
    def normalize_district_name(name):
        if pd.isna(name):
            return name
        name = str(name).strip()
        if name == 'Rukumkot' or name == 'East Rukum':
            return 'Rukum (East)'
        elif name == 'Rukum' or name == 'West Rukum':
            return 'Rukum (West)'
        return name
    
    # Set relaxed fuzzy match thresholds
    STRICT_MATCH = 80
    RELAXED_MATCH = 35  # Accept 30-40% for relaxed match
    
    created_count = 0
    skipped_count = 0
    skip_reasons = {}
    
    print("\n🔄 Starting V1 import...")
    
    error_printed = 0
    max_error_prints = 5
    for index, row in df.iterrows():
        try:
            if pd.isna(row.get('hf_name')) or str(row['hf_name']).strip() == '':
                reason = "Missing facility name"
                skip_reasons[reason] = skip_reasons.get(reason, 0) + 1
                if skipped_count < 10:
                    print(f"Row {index}: Skipped - {reason}")
                skipped_count += 1
                continue
            
            # Province matching (unchanged)
            province_name = str(row.get('province_name', '')).strip()
            # Strip "Province" or "Pradesh" from province name
            province_name = province_name.replace('Province', '').replace('Pradesh', '').strip()
            
            if pd.isna(province_name) or province_name == '':
                reason = f"Missing province (row: {province_name})"
                skip_reasons[reason] = skip_reasons.get(reason, 0) + 1
                if skipped_count < 10:
                    print(f"Row {index}: Skipped - {reason}")
                skipped_count += 1
                continue
            
            # Find province
            province = None
            for p in provinces:
                if p.name.lower() == province_name.lower():
                    province = p
                    break
            
            if not province:
                # Try fuzzy matching with normalized strings
                province_names = [p.name.lower() for p in provinces]
                best_match = process.extractOne(province_name.lower(), province_names, scorer=fuzz.ratio)
                if best_match and best_match[1] >= STRICT_MATCH:
                    province = next(p for p in provinces if p.name.lower() == best_match[0])
                    if skipped_count < 10:
                        print(f"Row {index}: Province matched '{province_name}' -> '{province.name}' (score: {best_match[1]})")
                else:
                    reason = f"Province not found: {province_name}"
                    skip_reasons[reason] = skip_reasons.get(reason, 0) + 1
                    if skipped_count < 10:
                        print(f"Row {index}: Skipped - {reason}")
                    skipped_count += 1
                    continue
            
            # District matching with normalization
            district_name = normalize_district_name(row.get('district_name', ''))
            if pd.isna(district_name) or district_name == '':
                reason = f"Missing district"
                skip_reasons[reason] = skip_reasons.get(reason, 0) + 1
                if skipped_count < 10:
                    print(f"Row {index}: Skipped - {reason}")
                skipped_count += 1
                continue
            
            # Find district in the same province
            district = None
            province_districts = [d for d in districts if d.province == province]
            
            for d in province_districts:
                if d.name.lower() == district_name.lower():
                    district = d
                    break
            
            if not district:
                # Try fuzzy matching within the province with normalized strings
                district_names = [d.name.lower() for d in province_districts]
                best_match = process.extractOne(district_name.lower(), district_names, scorer=fuzz.ratio)
                if best_match and best_match[1] >= RELAXED_MATCH:  # Relaxed threshold for districts
                    district = next(d for d in province_districts if d.name.lower() == best_match[0])
                    if skipped_count < 10:
                        print(f"Row {index}: District matched '{district_name}' -> '{district.name}' (score: {best_match[1]})")
                else:
                    reason = f"District not found: {district_name} in {province.name}"
                    skip_reasons[reason] = skip_reasons.get(reason, 0) + 1
                    if skipped_count < 10:
                        print(f"Row {index}: Skipped - {reason}")
                    skipped_count += 1
                    continue
            
            # Municipality matching
            municipality_name = str(row.get('municipality_name', '')).strip()
            if pd.isna(municipality_name) or municipality_name == '':
                reason = f"Missing municipality"
                skip_reasons[reason] = skip_reasons.get(reason, 0) + 1
                if skipped_count < 10:
                    print(f"Row {index}: Skipped - {reason}")
                skipped_count += 1
                continue
            
            # Find municipality in the same district
            municipality = None
            district_municipalities = [m for m in municipalities if m.district == district]
            
            for m in district_municipalities:
                if m.name.lower() == municipality_name.lower():
                    municipality = m
                    break
            
            if not municipality:
                # Try fuzzy matching within the district with normalized strings
                municipality_names = [m.name.lower() for m in district_municipalities]
                best_match = process.extractOne(municipality_name.lower(), municipality_names, scorer=fuzz.ratio)
                if best_match and best_match[1] >= RELAXED_MATCH:  # Relaxed threshold for municipalities
                    municipality = next(m for m in district_municipalities if m.name.lower() == best_match[0])
                    if skipped_count < 10:
                        print(f"Row {index}: Municipality matched '{municipality_name}' -> '{municipality.name}' (score: {best_match[1]})")
                else:
                    reason = f"Municipality not found: {municipality_name} in {district.name}"
                    skip_reasons[reason] = skip_reasons.get(reason, 0) + 1
                    if skipped_count < 10:
                        print(f"Row {index}: Skipped - {reason}")
                    skipped_count += 1
                    continue
            
            # Match ward - create missing wards and handle blank/zero values
            ward_number = row.get('ward')
            if pd.isna(ward_number) or ward_number == 0 or ward_number == 0.0:
                # Set ward to None for blank/zero values
                ward = None
                if skipped_count < 10:
                    print(f"Row {index}: Setting ward to None (blank/zero value)")
            else:
                # Try to find existing ward
                try:
                    ward = Ward.objects.get(ward_number=int(ward_number), municipality=municipality)
                except Ward.DoesNotExist:
                    reason = f"Ward not found: {ward_number} in {municipality.name}"
                    ward = None
                    
                    # Create the missing ward
                    # try:
                    #     ward = Ward.objects.create(
                    #         ward_number=int(ward_number),
                    #         municipality=municipality,
                    #         name=f"Ward {int(ward_number)}",
                    #         is_active=True
                    #     )
                    #     print(f"Row {index}: Created missing ward {int(ward_number)} in {municipality.name}")
                    # except Exception as e:
                    #     # Show available wards for this municipality if creation fails
                    #     available_wards = list(Ward.objects.filter(municipality=municipality).values_list('ward_number', flat=True))
                    #     reason = f"Failed to create ward {ward_number} in {municipality.name} (available: {available_wards[:10]}{'...' if len(available_wards) > 10 else ''}) - Error: {str(e)}"
                    #     skip_reasons[reason] = skip_reasons.get(reason, 0) + 1
                    #     if skipped_count < 10:
                    #         print(f"Row {index}: Skipped - {reason}")
                    #     skipped_count += 1
                    #     continue
            
            # Match facility type
            facility_type_name = str(row.get('facility_type_name', '')).strip()
            facility_type = None
            
            if not pd.isna(facility_type_name) and facility_type_name != '':
                for ft in facility_types:
                    if ft.name.lower() == facility_type_name.lower():
                        facility_type = ft
                        break
                
                if not facility_type:
                    # Try fuzzy matching
                    best_match = process.extractOne(facility_type_name, [ft.name for ft in facility_types], scorer=fuzz.ratio)
                    if best_match and best_match[1] >= STRICT_MATCH:
                        facility_type = next(ft for ft in facility_types if ft.name == best_match[0])
            
            if not facility_type:
                # Use default facility type
                facility_type = facility_types[0]  # Hospital
                if skipped_count < 10:
                    print(f"Row {index}: Using default facility type: {facility_type.name}")
            
            # Match authority level
            authority_level_name = str(row.get('authlevel', '')).strip()
            authority_level = None
            
            if not pd.isna(authority_level_name) and authority_level_name != '':
                for al in authority_levels:
                    if al.name.lower() == authority_level_name.lower():
                        authority_level = al
                        break
                
                if not authority_level:
                    # Try fuzzy matching
                    best_match = process.extractOne(authority_level_name, [al.name for al in authority_levels], scorer=fuzz.ratio)
                    if best_match and best_match[1] >= STRICT_MATCH:
                        authority_level = next(al for al in authority_levels if al.name == best_match[0])
            
            if not authority_level:
                # Use default authority level
                authority_level = authority_levels[0]  # Federal
                if skipped_count < 10:
                    print(f"Row {index}: Using default authority level: {authority_level.name}")
            
            # Match ownership
            ownership_name = str(row.get('ownership_name', '')).strip()
            ownership = None
            
            if not pd.isna(ownership_name) and ownership_name != '':
                for o in ownerships:
                    if o.name.lower() == ownership_name.lower():
                        ownership = o
                        break
                
                if not ownership:
                    # Try fuzzy matching
                    best_match = process.extractOne(ownership_name, [o.name for o in ownerships], scorer=fuzz.ratio)
                    if best_match and best_match[1] >= STRICT_MATCH:
                        ownership = next(o for o in ownerships if o.name == best_match[0])
            
            # Match facility level
            facility_level_name = str(row.get('facility_level_name', '')).strip()
            facility_level = None
            
            if not pd.isna(facility_level_name) and facility_level_name != '':
                for fl in facility_levels:
                    if fl.name.lower() == facility_level_name.lower():
                        facility_level = fl
                        break
                
                if not facility_level:
                    # Try fuzzy matching
                    best_match = process.extractOne(facility_level_name, [fl.name for fl in facility_levels], scorer=fuzz.ratio)
                    if best_match and best_match[1] >= STRICT_MATCH:
                        facility_level = next(fl for fl in facility_levels if fl.name == best_match[0])
            
            # Create facility
            facility_data = {
                'facility_id': index + 1,  # Use index as facility_id since HF Code is too large
                'facility_type': facility_type,
                'name': str(row['hf_name']).strip(),
                'hf_code': str(row['hf_code']).strip(),
                'province': province,
                'district': district,
                'municipality': municipality,
                'authority_level': authority_level,
                'authority': str(row.get('authlevel', '')).strip() if not pd.isna(row.get('authlevel')) else '',
                'ownership': ownership,
                'facility_level': facility_level,
                'country_id': 1,  # Nepal
            }
            
            # Add ward only if it exists
            if ward is not None:
                facility_data['ward'] = ward
            
            # Add optional fields if they exist
            if not pd.isna(row.get('latitude')):
                try:
                    lat_value = float(row['latitude'])
                    if -999.999999 <= lat_value <= 999.999999:
                        facility_data['latitude'] = round(lat_value, 6)
                except (ValueError, TypeError):
                    pass  # Skip invalid latitude values
            
            if not pd.isna(row.get('longitude')):
                try:
                    lon_value = float(row['longitude'])
                    if -999.999999 <= lon_value <= 999.999999:
                        facility_data['longitude'] = round(lon_value, 6)
                except (ValueError, TypeError):
                    pass  # Skip invalid longitude values
            
            if not pd.isna(row.get('estd_date')):
                facility_data['established_date_bs'] = str(row['estd_date'])
            
            if not pd.isna(row.get('opstatus')):
                facility_data['operational_status'] = str(row['opstatus'])
            
            if not pd.isna(row.get('internet')):
                internet_value = str(row['internet']).lower()
                if internet_value in ['yes', 'no', 'n/a']:
                    facility_data['internet_facility'] = internet_value
            
            if not pd.isna(row.get('email')):
                facility_data['email'] = str(row['email'])
            
            if not pd.isna(row.get('website')):
                facility_data['website'] = str(row['website'])
            
            if not pd.isna(row.get('telephone')):
                facility_data['telephone'] = str(row['telephone'])
            
            if not pd.isna(row.get('contact_person')):
                facility_data['contact_person'] = str(row['contact_person'])
            
            if not pd.isna(row.get('contact_person_mobile')):
                facility_data['contact_person_mobile'] = str(row['contact_person_mobile'])
            
            if not pd.isna(row.get('oxygen')):
                oxygen = str(row['oxygen'])
                # Truncate to fit max_length=10
                facility_data['oxygen'] = oxygen[:10] if len(oxygen) > 10 else oxygen
            
            if not pd.isna(row.get('plant_capacity')):
                plant_capacity = str(row['plant_capacity'])
                facility_data['plant_capacity'] = plant_capacity[:50] if len(plant_capacity) > 50 else plant_capacity
            
            if not pd.isna(row.get('cylinder')):
                cylinder = str(row['cylinder'])
                # Truncate to fit max_length=10
                facility_data['cylinder'] = cylinder[:10] if len(cylinder) > 10 else cylinder
            
            if not pd.isna(row.get('concentrator')):
                concentrator = str(row['concentrator'])
                # Truncate to fit max_length=10
                facility_data['concentrator'] = concentrator[:10] if len(concentrator) > 10 else concentrator
            
            if not pd.isna(row.get('ambulance')):
                ambulance_value = str(row['ambulance']).lower()
                if ambulance_value in ['yes', 'no']:
                    facility_data['ambulance'] = ambulance_value
            
            if not pd.isna(row.get('ambulance_category')):
                category = str(row['ambulance_category']).upper()
                if category in ['A', 'B', 'C']:
                    facility_data['ambulance_category'] = category
            
            if not pd.isna(row.get('ambulance_contact')):
                ambulance_contact = str(row['ambulance_contact'])
                # Truncate to fit max_length=20
                facility_data['ambulance_contact'] = ambulance_contact[:20] if len(ambulance_contact) > 20 else ambulance_contact
            
            if not pd.isna(row.get('hmis_code')):
                hmis_code = str(row['hmis_code'])
                facility_data['hmis_code'] = hmis_code[:50] if len(hmis_code) > 50 else hmis_code
            
            # Create the facility
            Facility.objects.create(**facility_data)
            created_count += 1
            
        except Exception as e:
            reason = f"Error - {str(e)}"
            skip_reasons[reason] = skip_reasons.get(reason, 0) + 1
            if skipped_count < 10:
                print(f"Row {index}: {reason}")
            if error_printed < max_error_prints:
                print(f"Row {index} data: {row.to_dict()}")
                error_printed += 1
            skipped_count += 1
    
    print(f"\n🎉 V1 Import completed!")
    print(f"✅ Created: {created_count} facilities")
    print(f"❌ Skipped: {skipped_count} facilities")
    print(f"📊 Total processed: {len(df)}")
    
    print(f"\n📋 Skip reasons summary:")
    for reason, count in sorted(skip_reasons.items(), key=lambda x: x[1], reverse=True):
        print(f"  {reason}: {count}")


def reverse_import_v1_nhfr_facilities(apps, schema_editor):
    Facility = apps.get_model('facilities', 'Facility')
    Facility.objects.all().delete()


class Migration(migrations.Migration):
    dependencies = [
        ('facilities', '0004_make_ward_nullable'),
        ('location', '0006_initial_wards'),
    ]

    operations = [
        migrations.RunPython(import_v1_nhfr_facilities, reverse_import_v1_nhfr_facilities),
    ]