from django.db import migrations


def create_initial_reference_data(apps, schema_editor):
    """
    Create initial reference data for AuthorityLevel, FacilityType, FacilityTypeCategory, Ownership, and FacilityLevel
    based on the V1_Health_Facility_Registry.xlsx file data
    """
    AuthorityLevel = apps.get_model('facilities', 'AuthorityLevel')
    FacilityType = apps.get_model('facilities', 'FacilityType')
    FacilityTypeCategory = apps.get_model('facilities', 'FacilityTypeCategory')
    Ownership = apps.get_model('facilities', 'Ownership')
    FacilityLevel = apps.get_model('facilities', 'FacilityLevel')
    
    # Clear existing data to avoid duplicates
    print("🗑️ Clearing existing reference data...")
    AuthorityLevel.objects.all().delete()
    FacilityType.objects.all().delete()
    FacilityTypeCategory.objects.all().delete()
    FacilityLevel.objects.all().delete()
    Ownership.objects.all().delete()
    
    # Create AuthorityLevel data based on V1_Health_Facility_Registry.xlsx
    authority_levels = [
        {'name': 'Federal', 'code': 'FE', 'description': 'Federal level authority'},
        {'name': 'Provincial', 'code': 'PR', 'description': 'Provincial level authority'},
        {'name': 'Local', 'code': 'LO', 'description': 'Local level authority'},
    ]
    
    for level_data in authority_levels:
        authority_level = AuthorityLevel.objects.create(**level_data)
        print(f"✅ Created authority level: {authority_level.name}")
    
    # Create FacilityTypeCategory data based on V1_Health_Facility_Registry.xlsx
    facility_type_categories = [
        {'name': 'Allopathy', 'code': 'ALLOPATHY', 'description': 'Allopathic medicine facilities'},
        {'name': 'Ayurveda', 'code': 'AYURVEDA', 'description': 'Ayurvedic medicine facilities'},
        {'name': 'Integrated Services', 'code': 'INTEGRATED', 'description': 'Integrated health services'},
        {'name': 'Homeopathy', 'code': 'HOMEOPATHY', 'description': 'Homeopathic medicine facilities'},
        {'name': 'Unani', 'code': 'UNANI', 'description': 'Unani medicine facilities'},
        {'name': 'Sowarigpa', 'code': 'SOWARIGPA', 'description': 'Sowarigpa medicine facilities'},
        {'name': 'Naturopathy', 'code': 'NATUROPATHY', 'description': 'Naturopathic medicine facilities'},
        {'name': 'Acupuncture', 'code': 'ACUPUNCTURE', 'description': 'Acupuncture facilities'},
    ]
    
    for category_data in facility_type_categories:
        facility_type_category = FacilityTypeCategory.objects.create(**category_data)
        print(f"✅ Created facility type category: {facility_type_category.name}")
    
    # Create Ownership data based on V1_Health_Facility_Registry.xlsx
    ownership_types = [
        {'name': 'Local Government', 'code': 'LG', 'description': 'Local Government owned facilities'},
        {'name': 'Provincial Government', 'code': 'PG', 'description': 'Provincial Government owned facilities'},
        {'name': 'Ministry of Health and Population', 'code': 'MOHP', 'description': 'Ministry of Health and Population facilities'},
        {'name': 'Ministry of Home Affairs', 'code': 'MOHA', 'description': 'Ministry of Home Affairs facilities'},
        {'name': 'Ministry of Defense', 'code': 'MOD', 'description': 'Ministry of Defense facilities'},
        {'name': 'Ministry of Education', 'code': 'MOE', 'description': 'Ministry of Education facilities'},
        {'name': 'Ministry of Federal Affairs and General Administration', 'code': 'MOFAGA', 'description': 'Ministry of Federal Affairs and General Administration facilities'},
        {'name': 'Ministry of General Administration', 'code': 'MOGA', 'description': 'Ministry of General Administration facilities'},
        {'name': 'Private Enterprise', 'code': 'PRIVATE', 'description': 'Private enterprise owned facilities'},
        {'name': 'Non Governmental Organizations', 'code': 'NGO', 'description': 'Non-Governmental Organizations'},
        {'name': 'NGO/INGO', 'code': 'NGO_INGO', 'description': 'Non-Governmental Organizations/International NGOs'},
        {'name': 'Cooperative Organizations', 'code': 'COOP', 'description': 'Cooperative organization facilities'},
        {'name': 'Community/Co-operative Hospital', 'code': 'COMM_HOSP', 'description': 'Community/Co-operative hospitals'},
        {'name': 'Charitable/Trust', 'code': 'CHARITABLE', 'description': 'Charitable/Trust facilities'},
    ]
    
    for ownership_data in ownership_types:
        ownership = Ownership.objects.create(**ownership_data)
        print(f"✅ Created ownership: {ownership.name}")
    
    # Create FacilityLevel data based on V1_Health_Facility_Registry.xlsx
    facility_levels = [
        {'name': 'Academy of Health Science/ Teaching hospital/ Institute of Health Sciences', 'code': 'AHS', 'description': 'Academy of Health Science/Teaching hospital/Institute of Health Sciences'},
        {'name': 'Ayurveda Aushadhalaya', 'code': 'AA', 'description': 'Ayurveda aushadhalaya'},
        {'name': 'Ayurveda Clinic', 'code': 'AC', 'description': 'Ayurveda clinic'},
        {'name': 'Ayurveda Health Center (General Ayurveda Hospital)', 'code': 'AHC', 'description': 'Ayurveda health center'},
        {'name': 'Ayurveda Hospital', 'code': 'AH', 'description': 'Ayurveda hospital'},
        {'name': 'Basic Ayurveda Service Centre', 'code': 'BASC', 'description': 'Basic ayurveda service centre'},
        {'name': 'Basic Health Service Center', 'code': 'BHSC', 'description': 'Basic health service center'},
        {'name': 'Basic Hospital (5 to 15 beds)', 'code': 'BH_5_15', 'description': 'Basic hospital with 5-15 beds'},
        {'name': 'Central Hospital', 'code': 'CH_CENTRAL', 'description': 'Central hospital'},
        {'name': "Children's Hospital", 'code': 'CH', 'description': "Children's hospital"},
        {'name': 'Clinic (Dental/Eye/Medical)', 'code': 'CLINIC', 'description': 'Specialized clinics (Dental/Eye/Medical)'},
        {'name': 'Community Health Unit', 'code': 'CHU', 'description': 'Community health unit'},
        {'name': 'Dental Clinic', 'code': 'DC', 'description': 'Dental clinic'},
        {'name': 'Diagnostic Center (Radiology/Laboratory/X-ray etc.)', 'code': 'DC_DIAG', 'description': 'Diagnostic center'},
        {'name': 'District Ayurveda Health center', 'code': 'DAHC', 'description': 'District ayurveda health center'},
        {'name': 'District Clinic (Including Institutional)', 'code': 'DC_INST', 'description': 'District clinic (including institutional)'},
        {'name': 'District Hospital', 'code': 'DH', 'description': 'District hospital'},
        {'name': 'Eye Treatment Centre', 'code': 'ETC', 'description': 'Eye treatment centre'},
        {'name': 'General Hospital', 'code': 'GH', 'description': 'General hospital'},
        {'name': 'Health Clinic', 'code': 'HC', 'description': 'Health clinic'},
        {'name': 'Health Post', 'code': 'HP', 'description': 'Basic health post'},
        {'name': 'Homeopathy Clinic', 'code': 'HC_HOME', 'description': 'Homeopathy clinic'},
        {'name': 'Homeopathy Hospital', 'code': 'HH', 'description': 'Homeopathy hospital'},
        {'name': 'Laboratory "A"', 'code': 'LAB_A', 'description': 'Laboratory class A'},
        {'name': 'Laboratory "C"', 'code': 'LAB_C', 'description': 'Laboratory class C'},
        {'name': 'Laboratory "D"', 'code': 'LAB_D', 'description': 'Laboratory class D'},
        {'name': 'Laboratory "E"', 'code': 'LAB_E', 'description': 'Laboratory class E'},
        {'name': 'Nursing Home', 'code': 'NH', 'description': 'Nursing home'},
        {'name': 'Physiotherapy Centre', 'code': 'PC_PHYSIO', 'description': 'Physiotherapy centre'},
        {'name': 'Poly Clinic', 'code': 'PC', 'description': 'Poly clinic'},
        {'name': 'Primary Health Center', 'code': 'PHC', 'description': 'Primary health center'},
        {'name': 'Primary Hospital', 'code': 'PH', 'description': 'Primary hospital'},
        {'name': 'Radio Imaging  Centre B class', 'code': 'RIC_B', 'description': 'Radio imaging centre class B'},
        {'name': 'Regional Hospital', 'code': 'RH', 'description': 'Regional hospital'},
        {'name': 'Rehabilitation Centre (Physical)', 'code': 'RC_PHYSICAL', 'description': 'Rehabilitation centre (physical)'},
        {'name': 'Rehabilitation Centre', 'code': 'RC_PSYCHO', 'description': 'Rehabilitation centre (psycho social)'},
        {'name': 'Specialist Clinic', 'code': 'SC', 'description': 'Specialist clinic'},
        {'name': 'Specialist Hospital', 'code': 'SH', 'description': 'Specialist hospital'},
        {'name': 'Specialized Hospital', 'code': 'SPH', 'description': 'Specialized hospital'},
        {'name': 'Super Specialized Hospital', 'code': 'SSH', 'description': 'Super specialized hospital'},
        {'name': 'Teaching Hospital', 'code': 'TH', 'description': 'Teaching hospital'},
        {'name': 'Urban Health Center', 'code': 'UHC', 'description': 'Urban health center'},
        {'name': 'Zonal Ayurveda Aushadhalaya', 'code': 'ZAA', 'description': 'Zonal ayurveda aushadhalaya'},
    ]
    
    for level_data in facility_levels:
        facility_level = FacilityLevel.objects.create(**level_data)
        print(f"✅ Created facility level: {facility_level.name}")
    
    # Create FacilityType data based on V1_Health_Facility_Registry.xlsx
    facility_types = [
        {'name': 'Government', 'code': 'GOVERNMENT', 'description': 'Government owned facilities'},
        {'name': 'Non-Government', 'code': 'NON_GOVERNMENT', 'description': 'Non-government owned facilities'},
    ]
    
    for type_data in facility_types:
        facility_type = FacilityType.objects.create(**type_data)
        print(f"✅ Created facility type: {facility_type.name}")
    
    print(f"\n🎉 Created {len(authority_levels)} authority levels, {len(facility_type_categories)} facility type categories, {len(ownership_types)} ownership types, {len(facility_levels)} facility levels, and {len(facility_types)} facility types")


def reverse_create_initial_reference_data(apps, schema_editor):
    """Reverse the creation of reference data"""
    AuthorityLevel = apps.get_model('facilities', 'AuthorityLevel')
    FacilityType = apps.get_model('facilities', 'FacilityType')
    FacilityTypeCategory = apps.get_model('facilities', 'FacilityTypeCategory')
    Ownership = apps.get_model('facilities', 'Ownership')
    FacilityLevel = apps.get_model('facilities', 'FacilityLevel')
    
    AuthorityLevel.objects.all().delete()
    FacilityType.objects.all().delete()
    FacilityTypeCategory.objects.all().delete()
    FacilityLevel.objects.all().delete()
    Ownership.objects.all().delete()


class Migration(migrations.Migration):
    dependencies = [
        ('location', '0006_initial_wards'),
        ('facilities', '0002_add_facility_type_category'),
    ]

    operations = [
        migrations.RunPython(
            create_initial_reference_data,
            reverse_create_initial_reference_data
        ),
    ] 