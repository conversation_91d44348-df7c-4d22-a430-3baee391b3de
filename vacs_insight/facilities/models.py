from django.db import models
from core.models import AuditModel
from location.models import Country, Province, District, Municipality, Ward
import uuid


class AuthorityLevel(models.Model):
    id = models.AutoField(primary_key=True)
    name = models.CharField(max_length=50, unique=True)
    code = models.CharField(max_length=20, unique=True)
    description = models.TextField(blank=True, null=True)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return self.name

    class Meta:
        db_table = 'authority_level'
        verbose_name = 'Authority Level'
        verbose_name_plural = 'Authority Levels'


class FacilityType(models.Model):
    id = models.AutoField(primary_key=True)
    name = models.CharField(max_length=255)
    code = models.CharField(max_length=50, unique=True)
    description = models.TextField(blank=True, null=True)
    is_active = models.<PERSON>oleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.name} ({self.code})"

    class Meta:
        db_table = 'facility_type'
        verbose_name = 'Facility Type'
        verbose_name_plural = 'Facility Types'


class FacilityTypeCategory(models.Model):
    id = models.AutoField(primary_key=True)
    name = models.CharField(max_length=100, unique=True)
    code = models.CharField(max_length=20, unique=True)
    description = models.TextField(blank=True, null=True)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.name} ({self.code})"

    class Meta:
        db_table = 'facility_type_category'
        verbose_name = 'Facility Type Category'
        verbose_name_plural = 'Facility Type Categories'


class Ownership(models.Model):
    id = models.AutoField(primary_key=True)
    name = models.CharField(max_length=100, unique=True)
    code = models.CharField(max_length=20, unique=True)
    description = models.TextField(blank=True, null=True)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.name} ({self.code})"

    class Meta:
        db_table = 'ownership'
        verbose_name = 'Ownership'
        verbose_name_plural = 'Ownerships'


class FacilityLevel(models.Model):
    id = models.AutoField(primary_key=True)
    name = models.CharField(max_length=100, unique=True)
    code = models.CharField(max_length=20, unique=True)
    description = models.TextField(blank=True, null=True)
    is_active = models.BooleanField(default=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    def __str__(self):
        return f"{self.name} ({self.code})"

    class Meta:
        db_table = 'facility_level'
        verbose_name = 'Facility Level'
        verbose_name_plural = 'Facility Levels'


class Facility(AuditModel):
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    facility_id = models.IntegerField(unique=True)
    facility_type = models.ForeignKey(FacilityType, on_delete=models.PROTECT)
    facility_type_category = models.ForeignKey(FacilityTypeCategory, on_delete=models.PROTECT, null=True, blank=True)
    name = models.CharField(max_length=255)
    hf_code = models.CharField(max_length=50, unique=True)  # This will be hf_code from NHFR

    # Location fields
    country = models.ForeignKey(Country, on_delete=models.PROTECT, default=1)
    province = models.ForeignKey(Province, on_delete=models.PROTECT)
    district = models.ForeignKey(District, on_delete=models.PROTECT)
    municipality = models.ForeignKey(Municipality, on_delete=models.PROTECT)
    ward = models.ForeignKey(Ward, on_delete=models.PROTECT, null=True, blank=True)

    # Coordinates
    latitude = models.DecimalField(max_digits=9, decimal_places=6, null=True, blank=True)
    longitude = models.DecimalField(max_digits=9, decimal_places=6, null=True, blank=True)

    # New fields from NHFR
    established_date_bs = models.CharField(max_length=20, null=True, blank=True, help_text="Establishment date in Bikram Sambat")
    operational_status = models.CharField(max_length=50, null=True, blank=True, help_text="Operational status of the facility")
    
    # Internet facility with choices
    INTERNET_CHOICES = [
        ('yes', 'Yes'),
        ('no', 'No'),
        ('n/a', 'N/A'),
    ]
    internet_facility = models.CharField(max_length=3, choices=INTERNET_CHOICES, null=True, blank=True)
    
    # HMIS code
    hmis_code = models.CharField(max_length=50, null=True, blank=True)
    
    # Contact information
    email = models.EmailField(null=True, blank=True)
    website = models.URLField(null=True, blank=True)
    telephone = models.CharField(max_length=20, null=True, blank=True)
    contact_person = models.CharField(max_length=100, null=True, blank=True)
    contact_person_mobile = models.CharField(max_length=20, null=True, blank=True)
    
    # Oxygen related fields
    oxygen = models.CharField(max_length=10, null=True, blank=True, help_text="Oxygen availability")
    plant_capacity = models.CharField(max_length=50, null=True, blank=True, help_text="Oxygen plant capacity")
    cylinder = models.CharField(max_length=10, null=True, blank=True, help_text="Cylinder availability")
    concentrator = models.CharField(max_length=10, null=True, blank=True, help_text="Concentrator availability")
    
    # Ambulance related fields
    AMBULANCE_CHOICES = [
        ('yes', 'Yes'),
        ('no', 'No'),
    ]
    ambulance = models.CharField(max_length=3, choices=AMBULANCE_CHOICES, null=True, blank=True)
    
    AMBULANCE_CATEGORY_CHOICES = [
        ('A', 'Category A'),
        ('B', 'Category B'),
        ('C', 'Category C'),
    ]
    ambulance_category = models.CharField(max_length=1, choices=AMBULANCE_CATEGORY_CHOICES, null=True, blank=True)
    ambulance_contact = models.CharField(max_length=20, null=True, blank=True)

    # Parent facility information
    parent_facility_type = models.ForeignKey(
        FacilityType,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name='child_facilities'
    )
    parent_facility_nin = models.CharField(max_length=50, null=True, blank=True)

    # Service availability flags
    fru_d = models.BooleanField(default=False)  # First Referral Unit - Delivery
    fru_f = models.BooleanField(default=False)  # First Referral Unit - Family Planning
    dp_d = models.BooleanField(default=False)  # Delivery Point - Delivery
    dp_f = models.BooleanField(default=False)  # Delivery Point - Family Planning
    hwc_d = models.BooleanField(default=False)  # Health Worker - Delivery
    hwc_f = models.BooleanField(default=False)  # Health Worker - Family Planning
    nbsu_d = models.BooleanField(default=False)  # Newborn Stabilization Unit - Delivery
    nbsu_f = models.BooleanField(default=False)  # Newborn Stabilization Unit - Family Planning
    sncu_d = models.BooleanField(default=False)  # Special Newborn Care Unit - Delivery
    sncu_f = models.BooleanField(default=False)  # Special Newborn Care Unit - Family Planning

    # Capacity information
    bed_count = models.IntegerField(null=True, blank=True)
    functional_bed_count = models.IntegerField(null=True, blank=True)

    # Administrative information
    authority_level = models.ForeignKey(AuthorityLevel, on_delete=models.PROTECT)
    authority = models.CharField(max_length=100)
    ownership = models.ForeignKey(Ownership, on_delete=models.PROTECT, null=True, blank=True)
    facility_level = models.ForeignKey(FacilityLevel, on_delete=models.PROTECT, null=True, blank=True)

    # Status
    is_active = models.BooleanField(default=True)

    def __str__(self):
        return f"{self.name} ({self.hf_code})"

    class Meta:
        db_table = 'facility'
        verbose_name = 'Facility'
        verbose_name_plural = 'Facilities'
        indexes = [
            models.Index(fields=['facility_id']),
            models.Index(fields=['hf_code']),
            models.Index(fields=['is_active']),
            models.Index(fields=['country', 'province', 'district', 'municipality', 'ward']),
        ]
