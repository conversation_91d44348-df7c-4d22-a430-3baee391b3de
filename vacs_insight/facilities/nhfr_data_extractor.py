#!/usr/bin/env python3
"""
NHFR Data Extractor
Fetches health facility data from Nepal Health Facility Registry API
and exports to CSV and Excel formats with standardized column names.
"""

import requests
import pandas as pd
import json
from datetime import datetime
import time
import sys
import os

def fetch_nhfr_data():
    """
    Fetch data from NHFR API
    """
    url = "https://nhfr.mohp.gov.np/health-registry/search"
    
    print("Fetching data from NHFR API...")
    print(f"URL: {url}")
    
    try:
        # Make the API request
        response = requests.get(url, timeout=30)
        response.raise_for_status()
        
        # Parse JSON response
        data = response.json()
        
        print(f"✅ Successfully fetched data")
        print(f"📊 Total records: {len(data)}")
        
        return data
        
    except requests.exceptions.RequestException as e:
        print(f"❌ Error fetching data: {e}")
        return None
    except json.JSONDecodeError as e:
        print(f"❌ Error parsing JSON response: {e}")
        return None

def flatten_facility_data(facility):
    """
    Flatten nested facility data structure with standardized column names
    """
    flattened = {
        'id': facility.get('id'),
        'hf_code': facility.get('hf_code'),
        'hf_name': facility.get('hf_name'),
        'type': facility.get('type'),
        'authlevel': facility.get('authlevel'),
        'level': facility.get('level'),
        'opstatus': facility.get('opstatus'),
        'latitude': facility.get('latitude'),
        'longitude': facility.get('longitude'),
        'estd_date': facility.get('estd_date'),
        'email': facility.get('email'),
        'website': facility.get('website'),
        'telephone': facility.get('telephone'),
        'contact_person': facility.get('contact_person'),
        'contact_person_mobile': facility.get('contact_person_mobile'),
        'oxygen': facility.get('oxygen'),
        'plant_capacity': facility.get('plant_capacity'),
        'cylinder': facility.get('cylinder'),
        'concentrator': facility.get('concentrator'),
        'ambulance': facility.get('ambulance'),
        'ambulance_category': facility.get('ambulance_category'),
        'ambulance_contact': facility.get('ambulance_contact'),
        'internet': facility.get('internet'),
        'hmis_code': facility.get('hmis_code'),
        'province_id': facility.get('province'),
        'district_id': facility.get('district'),
        'municipality_id': facility.get('municipality'),
        'ward': facility.get('ward'),
        'ucode': facility.get('ucode'),
        'cbscode': facility.get('cbscode'),
    }
    
    # Extract nested data
    if facility.get('healthFacilityType'):
        flattened['facility_type_id'] = facility['healthFacilityType'].get('id')
        flattened['facility_type_code'] = facility['healthFacilityType'].get('code')
        flattened['facility_type_name'] = facility['healthFacilityType'].get('type_name')
    
    if facility.get('healthFacilityLevel'):
        flattened['facility_level_id'] = facility['healthFacilityLevel'].get('id')
        flattened['facility_level_code'] = facility['healthFacilityLevel'].get('code')
        flattened['facility_level_name'] = facility['healthFacilityLevel'].get('name')
    
    if facility.get('ownerships'):
        flattened['ownership_id'] = facility['ownerships'].get('id')
        flattened['ownership_code'] = facility['ownerships'].get('code')
        flattened['ownership_name'] = facility['ownerships'].get('name')
    
    if facility.get('provinces'):
        flattened['province_name'] = facility['provinces'].get('nameen')
    
    if facility.get('districts'):
        flattened['district_name'] = facility['districts'].get('nameen')
    
    if facility.get('municipalitys'):
        flattened['municipality_name'] = facility['municipalitys'].get('nameen')
        flattened['municipality_ward_count'] = facility['municipalitys'].get('numberofward')
    
    if facility.get('ftypes'):
        flattened['facility_type_category'] = facility['ftypes'].get('name')
    
    return flattened

def standardize_column_names(df):
    """
    Standardize column names to match our migration expectations
    """
    # Column name mapping from API format to standardized format
    column_mapping = {
        'id': 'id',
        'hf_code': 'hf_code',
        'hf_name': 'hf_name',
        'type': 'type',
        'authlevel': 'authlevel',
        'level': 'level',
        'opstatus': 'opstatus',
        'latitude': 'latitude',
        'longitude': 'longitude',
        'estd_date': 'estd_date',
        'email': 'email',
        'website': 'website',
        'telephone': 'telephone',
        'contact_person': 'contact_person',
        'contact_person_mobile': 'contact_person_mobile',
        'oxygen': 'oxygen',
        'plant_capacity': 'plant_capacity',
        'cylinder': 'cylinder',
        'concentrator': 'concentrator',
        'ambulance': 'ambulance',
        'ambulance_category': 'ambulance_category',
        'ambulance_contact': 'ambulance_contact',
        'internet': 'internet',
        'hmis_code': 'hmis_code',
        'province_id': 'province_id',
        'district_id': 'district_id',
        'municipality_id': 'municipality_id',
        'ward': 'ward',
        'ucode': 'ucode',
        'cbscode': 'cbscode',
        'facility_type_id': 'facility_type_id',
        'facility_type_code': 'facility_type_code',
        'facility_type_name': 'facility_type_name',
        'facility_level_id': 'facility_level_id',
        'facility_level_code': 'facility_level_code',
        'facility_level_name': 'facility_level_name',
        'ownership_id': 'ownership_id',
        'ownership_code': 'ownership_code',
        'ownership_name': 'ownership_name',
        'province_name': 'province_name',
        'district_name': 'district_name',
        'municipality_name': 'municipality_name',
        'municipality_ward_count': 'municipality_ward_count',
        'facility_type_category': 'facility_type_category',
    }
    
    # Rename columns to standardized names
    df = df.rename(columns=column_mapping)
    
    # Ensure all expected columns exist (fill with None if missing)
    expected_columns = [
        'id', 'hf_code', 'hf_name', 'type', 'authlevel', 'level', 'opstatus',
        'latitude', 'longitude', 'estd_date', 'email', 'website', 'telephone',
        'contact_person', 'contact_person_mobile', 'oxygen', 'plant_capacity',
        'cylinder', 'concentrator', 'ambulance', 'ambulance_category',
        'ambulance_contact', 'internet', 'hmis_code', 'province_id',
        'district_id', 'municipality_id', 'ward', 'ucode', 'cbscode',
        'facility_type_id', 'facility_type_code', 'facility_type_name',
        'facility_level_id', 'facility_level_code', 'facility_level_name',
        'ownership_id', 'ownership_code', 'ownership_name', 'province_name',
        'district_name', 'municipality_name', 'municipality_ward_count',
        'facility_type_category'
    ]
    
    for col in expected_columns:
        if col not in df.columns:
            df[col] = None
    
    return df[expected_columns]

def export_to_csv(data, filename):
    """
    Export data to CSV file
    """
    try:
        df = pd.DataFrame(data)
        df = standardize_column_names(df)
        df.to_csv(filename, index=False, encoding='utf-8')
        print(f"✅ CSV file saved: {filename}")
        return True
    except Exception as e:
        print(f"❌ Error saving CSV: {e}")
        return False

def export_to_excel(data, filename):
    """
    Export data to Excel file
    """
    try:
        df = pd.DataFrame(data)
        df = standardize_column_names(df)
        
        # Create Excel writer with xlsxwriter engine
        with pd.ExcelWriter(filename, engine='xlsxwriter') as writer:
            df.to_excel(writer, sheet_name='Health_Facilities', index=False)
            
            # Get the workbook and worksheet objects
            workbook = writer.book
            worksheet = writer.sheets['Health_Facilities']
            
            # Add some formatting
            header_format = workbook.add_format({
                'bold': True,
                'text_wrap': True,
                'valign': 'top',
                'fg_color': '#D7E4BC',
                'border': 1
            })
            
            # Write the column headers with the defined format
            for col_num, value in enumerate(df.columns.values):
                worksheet.write(0, col_num, value, header_format)
            
            # Auto-adjust columns width
            for i, col in enumerate(df.columns):
                max_len = max(
                    df[col].astype(str).apply(len).max(),
                    len(col)
                )
                worksheet.set_column(i, i, min(max_len + 2, 50))
        
        print(f"✅ Excel file saved: {filename}")
        return True
    except Exception as e:
        print(f"❌ Error saving Excel: {e}")
        return False

def print_summary(data):
    """
    Print summary statistics
    """
    if not data:
        return
    
    df = pd.DataFrame(data)
    df = standardize_column_names(df)
    
    print("\n" + "="*50)
    print("📊 DATA SUMMARY")
    print("="*50)
    
    # Basic stats
    print(f"Total Facilities: {len(df)}")
    
    # Province distribution
    if 'province_name' in df.columns:
        province_counts = df['province_name'].value_counts()
        print(f"\n🏥 Facilities by Province:")
        for province, count in province_counts.head(10).items():
            print(f"  {province}: {count}")
    
    # Facility level distribution
    if 'facility_level_name' in df.columns:
        level_counts = df['facility_level_name'].value_counts()
        print(f"\n🏥 Facilities by Level:")
        for level, count in level_counts.head(10).items():
            print(f"  {level}: {count}")
    
    # Operational status
    if 'opstatus' in df.columns:
        status_counts = df['opstatus'].value_counts()
        print(f"\n📈 Operational Status:")
        for status, count in status_counts.items():
            print(f"  {status}: {count}")
    
    # Oxygen availability
    if 'oxygen' in df.columns:
        oxygen_counts = df['oxygen'].value_counts()
        print(f"\n💨 Oxygen Availability:")
        for oxygen, count in oxygen_counts.items():
            print(f"  {oxygen}: {count}")
    
    # Internet availability
    if 'internet' in df.columns:
        internet_counts = df['internet'].value_counts()
        print(f"\n🌐 Internet Availability:")
        for internet, count in internet_counts.items():
            print(f"  {internet}: {count}")

def main():
    """
    Main function
    """
    print("🏥 NHFR Data Extractor")
    print("="*50)
    
    # Fetch data
    data = fetch_nhfr_data()
    
    if not data:
        print("❌ Failed to fetch data. Exiting.")
        sys.exit(1)
    
    # Flatten the data
    print("\n🔄 Processing data...")
    flattened_data = [flatten_facility_data(facility) for facility in data]
    print(f"✅ Processed {len(flattened_data)} facilities")
    
    # Create migrations/files directory if it doesn't exist
    migrations_files_dir = os.path.join(
        os.path.dirname(__file__), 
        'migrations', 
        'files'
    )
    os.makedirs(migrations_files_dir, exist_ok=True)
    
    # Export to Excel
    excel_filename = os.path.join(migrations_files_dir, "V1_Health_Facility_Registry.xlsx")
    export_to_excel(flattened_data, excel_filename)
    
    # Print summary
    print_summary(flattened_data)
    
    print("\n" + "="*50)
    print("🎉 Export completed successfully!")
    print(f"📁 Files created:")
    print(f"  - {excel_filename}")
    print("="*50)

if __name__ == "__main__":
    main() 