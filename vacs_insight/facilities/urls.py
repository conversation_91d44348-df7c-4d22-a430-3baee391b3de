from django.urls import path, include
from rest_framework.routers import <PERSON><PERSON>ult<PERSON><PERSON><PERSON>
from .views import FacilityViewSet, FacilityTypeViewSet, AuthorityLevelViewSet, FacilityLevelViewSet

router = DefaultRouter()
router.register(r'authority-level', AuthorityLevelViewSet)
router.register(r'facility-level', FacilityLevelViewSet)
router.register(r'facility-type', FacilityTypeViewSet)
router.register('', FacilityViewSet)

urlpatterns = [
    path('', include(router.urls)),
] 