from rest_framework import serializers
from .models import Facility, FacilityType, AuthorityLevel, FacilityLevel, Ownership, FacilityTypeCategory
from location.serializers import (
    ProvinceListSerializer, DistrictListSerializer, 
    MunicipalityListSerializer, WardListSerializer,
    ProvinceSerializer, DistrictSerializer, 
    MunicipalitySerializer, WardSerializer
)
from location.models import Province, District, Municipality, Ward, Country


class AuthorityLevelSerializer(serializers.ModelSerializer):
    class Meta:
        model = AuthorityLevel
        fields = [
            'id', 'name', 'code'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']


class FacilityLevelSerializer(serializers.ModelSerializer):
    class Meta:
        model = FacilityLevel
        fields = [
            'id', 'name', 'code'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']


class FacilityTypeSerializer(serializers.ModelSerializer):
    class Meta:
        model = FacilityType
        fields = [
            'id', 'name', 'code'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']


class FacilityTypeCategorySerializer(serializers.ModelSerializer):
    class Meta:
        model = FacilityTypeCategory
        fields = [
            'id', 'name', 'code'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']


class OwnershipSerializer(serializers.ModelSerializer):
    class Meta:
        model = Ownership
        fields = [
            'id', 'name', 'code'
        ]
        read_only_fields = ['id', 'created_at', 'updated_at']


class FacilityListSerializer(serializers.ModelSerializer):
    """Simplified serializer for facility list API"""
    facility_type = FacilityTypeSerializer(read_only=True)
    authority_level = AuthorityLevelSerializer(read_only=True)
    facility_level = FacilityLevelSerializer(read_only=True)
    ownership = OwnershipSerializer(read_only=True)
    country = serializers.SerializerMethodField()
    province = ProvinceListSerializer(read_only=True)
    district = DistrictListSerializer(read_only=True)
    municipality = MunicipalityListSerializer(read_only=True)
    ward = WardListSerializer(read_only=True)

    class Meta:
        model = Facility
        fields = [
            'id', 'facility_id', 'name', 'hf_code', 'hmis_code',
            'facility_type', 'authority_level', 'facility_level', 'ownership',
            'country', 'province', 'district', 'municipality', 'ward',
            'latitude', 'longitude', 'operational_status', 'is_active'
        ]
        read_only_fields = ['id']

    def get_country(self, obj):
        return {
            'id': obj.country.id,
            'name': obj.country.name,
            'code': obj.country.code
        }


class FacilitySerializer(serializers.ModelSerializer):
    facility_type = FacilityTypeSerializer(read_only=True)
    facility_type_id = serializers.PrimaryKeyRelatedField(
        queryset=FacilityType.objects.filter(is_active=True),
        source='facility_type',
        write_only=True
    )
    facility_type_category = FacilityTypeCategorySerializer(read_only=True)
    facility_type_category_id = serializers.PrimaryKeyRelatedField(
        queryset=FacilityTypeCategory.objects.filter(is_active=True),
        source='facility_type_category',
        write_only=True,
        required=False,
        allow_null=True
    )
    country = serializers.SerializerMethodField()
    country_id = serializers.PrimaryKeyRelatedField(
        queryset=Country.objects.filter(is_active=True),
        source='country',
        write_only=True
    )
    province = ProvinceSerializer(read_only=True)
    province_id = serializers.PrimaryKeyRelatedField(
        queryset=Province.objects.filter(is_active=True),
        source='province',
        write_only=True
    )
    district = DistrictSerializer(read_only=True)
    district_id = serializers.PrimaryKeyRelatedField(
        queryset=District.objects.filter(is_active=True),
        source='district',
        write_only=True
    )
    municipality = MunicipalitySerializer(read_only=True)
    municipality_id = serializers.PrimaryKeyRelatedField(
        queryset=Municipality.objects.filter(is_active=True),
        source='municipality',
        write_only=True
    )
    ward = WardSerializer(read_only=True)
    ward_id = serializers.PrimaryKeyRelatedField(
        queryset=Ward.objects.filter(is_active=True),
        source='ward',
        write_only=True,
        required=False,
        allow_null=True
    )
    parent_facility_type = FacilityTypeSerializer(read_only=True)
    parent_facility_type_id = serializers.PrimaryKeyRelatedField(
        queryset=FacilityType.objects.filter(is_active=True),
        source='parent_facility_type',
        write_only=True,
        required=False,
        allow_null=True
    )
    authority_level = AuthorityLevelSerializer(read_only=True)
    authority_level_id = serializers.PrimaryKeyRelatedField(
        queryset=AuthorityLevel.objects.filter(is_active=True),
        source='authority_level',
        write_only=True
    )
    facility_level = FacilityLevelSerializer(read_only=True)
    facility_level_id = serializers.PrimaryKeyRelatedField(
        queryset=FacilityLevel.objects.filter(is_active=True),
        source='facility_level',
        write_only=True,
        required=False,
        allow_null=True
    )
    ownership = OwnershipSerializer(read_only=True)
    ownership_id = serializers.PrimaryKeyRelatedField(
        queryset=Ownership.objects.filter(is_active=True),
        source='ownership',
        write_only=True,
        required=False,
        allow_null=True
    )

    class Meta:
        model = Facility
        fields = [
            'id', 'facility_id', 'facility_type', 'facility_type_id',
            'facility_type_category', 'facility_type_category_id',
            'country', 'country_id', 'name', 'hf_code', 'hmis_code',
            'established_date_bs', 'operational_status', 'internet_facility',
            'bed_count', 'functional_bed_count',
            'province', 'province_id', 'district', 'district_id',
            'municipality', 'municipality_id', 'ward', 'ward_id',
            'latitude', 'longitude', 
            'parent_facility_type', 'parent_facility_type_id',
            'parent_facility_nin', 'fru_d', 'fru_f', 'dp_d', 'dp_f',
            'hwc_d', 'hwc_f', 'nbsu_d', 'nbsu_f', 'sncu_d', 'sncu_f',
            'authority_level', 'authority_level_id', 'authority', 
            'ownership', 'ownership_id', 'facility_level', 'facility_level_id',
            'email', 'website', 'telephone', 'contact_person', 'contact_person_mobile',
            'oxygen', 'plant_capacity', 'cylinder', 'concentrator',
            'ambulance', 'ambulance_category', 'ambulance_contact',
            'is_active', 'created_at', 'updated_at', 'created_by', 'updated_by'
        ]
        read_only_fields = [
            'id', 'created_at', 'updated_at', 'created_by', 'updated_by'
        ]

    def get_country(self, obj):
        return {
            'id': obj.country.id,
            'name': obj.country.name,
            'code': obj.country.code
        }


class FacilityBulkCreateSerializer(serializers.Serializer):
    file = serializers.FileField()

    def validate_file(self, value):
        if not value.name.endswith('.csv'):
            raise serializers.ValidationError("Only CSV files are allowed")
        return value
