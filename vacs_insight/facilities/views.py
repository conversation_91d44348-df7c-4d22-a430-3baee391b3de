from django.shortcuts import render
from rest_framework import viewsets, status
from rest_framework.decorators import action
from rest_framework.response import Response
from rest_framework.parsers import MultiPartParser
from django.core.files.storage import default_storage
from django.core.files.base import ContentFile
import os
import csv
import io
from django.db import transaction
from rest_framework.permissions import IsAuthenticated
from django.db.models import Q

from .models import Facility, FacilityType, AuthorityLevel, FacilityLevel
from .serializers import (
    FacilitySerializer, FacilityListSerializer, FacilityTypeSerializer, AuthorityLevelSerializer,
    FacilityLevelSerializer, FacilityBulkCreateSerializer
)
from .services import DataIngestionService
from core.views import BaseModelViewSet
from location.models import Province, District, Municipality, Ward


# Create your views here.

class AuthorityLevelViewSet(BaseModelViewSet):
    queryset = AuthorityLevel.objects.filter(is_active=True)
    serializer_class = AuthorityLevelSerializer
    permission_classes = [IsAuthenticated]
    pagination_class = None


class FacilityLevelViewSet(BaseModelViewSet):
    queryset = FacilityLevel.objects.filter(is_active=True)
    serializer_class = FacilityLevelSerializer
    permission_classes = [IsAuthenticated]
    filterset_fields = ['name', 'code']
    search_fields = ['name', 'code', 'description']
    pagination_class = None


class FacilityTypeViewSet(BaseModelViewSet):
    queryset = FacilityType.objects.filter(is_active=True)
    serializer_class = FacilityTypeSerializer
    permission_classes = [IsAuthenticated]
    filterset_fields = ['name', 'code']
    search_fields = ['name', 'code', 'description']
    pagination_class = None

    @action(detail=False, methods=['post'], url_path='ingest-from-hfr')
    def ingest_from_hfr(self, request):
        """Ingest facility types from Health Facility Registry API"""
        try:
            service = DataIngestionService()
            result = service.ingest_facility_types_from_hfr()
            return Response(result, status=status.HTTP_200_OK)
        except Exception as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=False, methods=['post'], url_path='ingest-from-excel', parser_classes=[MultiPartParser])
    def ingest_from_excel(self, request):
        """Ingest facility types from Excel file"""
        if 'file' not in request.FILES:
            return Response(
                {'error': 'No file provided'},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            excel_file = request.FILES['file']
            # Save the file temporarily
            path = default_storage.save(f'temp/{excel_file.name}', ContentFile(excel_file.read()))
            temp_path = default_storage.path(path)

            service = DataIngestionService()
            result = service.ingest_facility_types_from_excel(temp_path)

            # Clean up the temporary file
            default_storage.delete(path)

            return Response(result, status=status.HTTP_200_OK)
        except Exception as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )


class FacilityViewSet(BaseModelViewSet):
    queryset = Facility.objects.filter(is_active=True)
    permission_classes = [IsAuthenticated]
    filterset_fields = ['name', 'code', 'facility_type', 'province', 'district', 'municipality', 'is_active']
    search_fields = ['name', 'code', 'province', 'district', 'municipality', 'address']
    http_method_names = ['get', 'head', 'options']  # Remove POST, PUT, PATCH, DELETE

    def get_serializer_class(self):
        """Use different serializers for list and detail operations"""
        if self.action == 'list':
            return FacilityListSerializer
        return FacilitySerializer

    def list(self, request, *args, **kwargs):
        """
        List facilities with search and filtering, without pagination
        """
        queryset = self.get_queryset()

        # Apply search
        search = request.query_params.get('search', '')
        if search:
            queryset = queryset.filter(
                Q(name__icontains=search) |
                Q(code__icontains=search) |
                Q(province__name__icontains=search) |
                Q(district__name__icontains=search) |
                Q(municipality__name__icontains=search)
            )

        # Apply filters
        queryset = self.filter_queryset(queryset)

        # Get count
        count = queryset.count()

        # Serialize data
        serializer = self.get_serializer(queryset, many=True)

        return Response({
            'count': count,
            'results': serializer.data
        })

    @action(detail=False, methods=['post'], url_path='ingest-from-hfr')
    def ingest_from_hfr(self, request):
        """Ingest facilities from Health Facility Registry API"""
        try:
            province_code = request.data.get('province_code')
            district_code = request.data.get('district_code')

            service = DataIngestionService()
            result = service.ingest_facilities_from_hfr(province_code, district_code)
            return Response(result, status=status.HTTP_200_OK)
        except Exception as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    @action(detail=False, methods=['post'], url_path='ingest-from-excel', parser_classes=[MultiPartParser])
    def ingest_from_excel(self, request):
        """Ingest facilities from Excel file"""
        if 'file' not in request.FILES:
            return Response(
                {'error': 'No file provided'},
                status=status.HTTP_400_BAD_REQUEST
            )

        try:
            excel_file = request.FILES['file']
            # Save the file temporarily
            path = default_storage.save(f'temp/{excel_file.name}', ContentFile(excel_file.read()))
            temp_path = default_storage.path(path)

            service = DataIngestionService()
            result = service.ingest_facilities_from_excel(temp_path)

            # Clean up the temporary file
            default_storage.delete(path)

            return Response(result, status=status.HTTP_200_OK)
        except Exception as e:
            return Response(
                {'error': str(e)},
                status=status.HTTP_500_INTERNAL_SERVER_ERROR
            )

    def get_queryset(self):
        queryset = super().get_queryset().select_related(
            'country', 'province', 'district', 'municipality', 'ward', 
            'facility_type', 'authority_level', 'facility_level', 'ownership'
        )

        # Filter by province
        province_id = self.request.query_params.get('province_id')
        if province_id:
            queryset = queryset.filter(province_id=province_id)

        # Filter by district
        district_id = self.request.query_params.get('district_id')
        if district_id:
            queryset = queryset.filter(district_id=district_id)

        # Filter by municipality
        municipality_id = self.request.query_params.get('municipality_id')
        if municipality_id:
            queryset = queryset.filter(municipality_id=municipality_id)

        # Filter by ward
        ward_id = self.request.query_params.get('ward_id')
        if ward_id:
            queryset = queryset.filter(ward_id=ward_id)

        # Filter by facility type
        facility_type_id = self.request.query_params.get('facility_type_id')
        if facility_type_id:
            queryset = queryset.filter(facility_type_id=facility_type_id)

        # Filter by authority level
        authority_level_id = self.request.query_params.get('authority_level_id')
        if authority_level_id:
            queryset = queryset.filter(authority_level_id=authority_level_id)

        # Filter by facility_level
        facility_level_id = self.request.query_params.get('facility_level_id')
        if facility_level_id:
            queryset = queryset.filter(facility_level_id=facility_level_id)

        # Filter by ownership
        ownership_id = self.request.query_params.get('ownership_id')
        if ownership_id:
            queryset = queryset.filter(ownership_id=ownership_id)

        return queryset

    @action(detail=False, methods=['post'])
    def bulk_create(self, request):
        serializer = FacilityBulkCreateSerializer(data=request.data)
        if not serializer.is_valid():
            return Response(serializer.errors, status=status.HTTP_400_BAD_REQUEST)

        csv_file = request.FILES['file']
        decoded_file = csv_file.read().decode('utf-8')
        io_string = io.StringIO(decoded_file)
        reader = csv.DictReader(io_string)

        created_count = 0
        updated_count = 0
        errors = []

        with transaction.atomic():
            for row in reader:
                try:
                    # Get or create facility type
                    facility_type, _ = FacilityType.objects.get_or_create(
                        id=row['Facility_Type'],
                        defaults={
                            'name': row.get('Facility_Type', 'Unknown'),
                            'code': str(row['Facility_Type']),
                            'is_active': True
                        }
                    )

                    # Get or create authority level
                    authority_level, _ = AuthorityLevel.objects.get_or_create(
                        name=row['Authority_Level'],
                        defaults={
                            'code': row['Authority_Level'].lower().replace(' ', '_'),
                            'is_active': True
                        }
                    )

                    # Get location objects
                    province = Province.objects.get(id=row['Province_ID'])
                    district = District.objects.get(id=row['District_ID'])
                    municipality = Municipality.objects.get(id=row['Palika_ID'])
                    ward = Ward.objects.get(id=row['Ward_ID'])

                    # Create or update facility
                    facility, created = Facility.objects.update_or_create(
                        facility_id=row['Facility_ID'],
                        defaults={
                            'facility_type': facility_type,
                            'name': row['Facility_Name'],
                            'code': row['Facility_Code'],
                            'province': province,
                            'district': district,
                            'municipality': municipality,
                            'ward': ward,
                            'latitude': row['Facility_Latitude'] if row['Facility_Latitude'] != 'NULL' else None,
                            'longitude': row['Facility_Longitude'] if row['Facility_Longitude'] != 'NULL' else None,
                            'authority_level': authority_level,
                            'authority': row['Authority'],
                            'ownership': row['Ownership'],
                            'facility_level': row['Facility_Level'],
                            'is_active': row['Facility_IsActive'] == '1',
                            'created_by': request.user,
                            'updated_by': request.user
                        }
                    )

                    if created:
                        created_count += 1
                    else:
                        updated_count += 1

                except Exception as e:
                    errors.append({
                        'row': row,
                        'error': str(e)
                    })

        return Response({
            'message': f'Successfully processed {created_count + updated_count} facilities',
            'created': created_count,
            'updated': updated_count,
            'errors': errors
        })
