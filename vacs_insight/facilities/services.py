import logging
import pandas as pd
import requests
from typing import List, Dict, Any
from django.conf import settings
from .models import Facility, FacilityType

logger = logging.getLogger(__name__)

class HFRService:
    """Service for interacting with Health Facility Registry API"""
    
    def __init__(self):
        self.base_url = settings.HFR_API_BASE_URL
        self.api_key = settings.HFR_API_KEY
        self.headers = {
            'Authorization': f'Bearer {self.api_key}',
            'Content-Type': 'application/json'
        }

    def fetch_facilities(self, province_code: str = None, district_code: str = None) -> List[Dict[str, Any]]:
        """Fetch facilities from HFR API with optional filtering"""
        try:
            params = {}
            if province_code:
                params['province_code'] = province_code
            if district_code:
                params['district_code'] = district_code

            response = requests.get(
                f"{self.base_url}/facilities",
                headers=self.headers,
                params=params
            )
            response.raise_for_status()
            return response.json()
        except Exception as e:
            logger.error(f"Error fetching facilities from HFR: {str(e)}")
            raise

    def fetch_facility_types(self) -> List[Dict[str, Any]]:
        """Fetch facility types from HFR API"""
        try:
            response = requests.get(
                f"{self.base_url}/facility-types",
                headers=self.headers
            )
            response.raise_for_status()
            return response.json()
        except Exception as e:
            logger.error(f"Error fetching facility types from HFR: {str(e)}")
            raise

class ExcelService:
    """Service for processing Excel files containing facility data"""

    @staticmethod
    def process_facility_excel(file_path: str) -> pd.DataFrame:
        """Process Excel file containing facility data"""
        try:
            df = pd.read_excel(file_path)
            required_columns = ['name', 'code', 'facility_type', 'province', 'district', 'municipality']
            
            # Validate required columns
            missing_columns = [col for col in required_columns if col not in df.columns]
            if missing_columns:
                raise ValueError(f"Missing required columns: {', '.join(missing_columns)}")
            
            return df
        except Exception as e:
            logger.error(f"Error processing facility Excel file: {str(e)}")
            raise

    @staticmethod
    def process_facility_type_excel(file_path: str) -> pd.DataFrame:
        """Process Excel file containing facility type data"""
        try:
            df = pd.read_excel(file_path)
            required_columns = ['name', 'code', 'description']
            
            # Validate required columns
            missing_columns = [col for col in required_columns if col not in df.columns]
            if missing_columns:
                raise ValueError(f"Missing required columns: {', '.join(missing_columns)}")
            
            return df
        except Exception as e:
            logger.error(f"Error processing facility type Excel file: {str(e)}")
            raise

class DataIngestionService:
    """Service for ingesting facility and facility type data"""

    def __init__(self):
        self.hfr_service = HFRService()
        self.excel_service = ExcelService()

    def ingest_facilities_from_hfr(self, province_code: str = None, district_code: str = None) -> Dict[str, Any]:
        """Ingest facilities from HFR API"""
        try:
            facilities = self.hfr_service.fetch_facilities(province_code, district_code)
            created = 0
            updated = 0
            errors = 0

            for facility_data in facilities:
                try:
                    facility, created_flag = Facility.objects.update_or_create(
                        code=facility_data['code'],
                        defaults={
                            'name': facility_data['name'],
                            'facility_type': self._get_or_create_facility_type(facility_data['facility_type']),
                            'province': facility_data['province'],
                            'district': facility_data['district'],
                            'municipality': facility_data['municipality'],
                            'latitude': facility_data.get('latitude'),
                            'longitude': facility_data.get('longitude'),
                            'address': facility_data.get('address'),
                            'contact_number': facility_data.get('contact_number'),
                            'email': facility_data.get('email'),
                            'is_active': facility_data.get('is_active', True)
                        }
                    )
                    if created_flag:
                        created += 1
                    else:
                        updated += 1
                except Exception as e:
                    logger.error(f"Error processing facility {facility_data.get('code')}: {str(e)}")
                    errors += 1

            return {
                'created': created,
                'updated': updated,
                'errors': errors
            }
        except Exception as e:
            logger.error(f"Error ingesting facilities from HFR: {str(e)}")
            raise

    def ingest_facility_types_from_hfr(self) -> Dict[str, Any]:
        """Ingest facility types from HFR API"""
        try:
            facility_types = self.hfr_service.fetch_facility_types()
            created = 0
            updated = 0
            errors = 0

            for type_data in facility_types:
                try:
                    facility_type, created_flag = FacilityType.objects.update_or_create(
                        code=type_data['code'],
                        defaults={
                            'name': type_data['name'],
                            'description': type_data.get('description', '')
                        }
                    )
                    if created_flag:
                        created += 1
                    else:
                        updated += 1
                except Exception as e:
                    logger.error(f"Error processing facility type {type_data.get('code')}: {str(e)}")
                    errors += 1

            return {
                'created': created,
                'updated': updated,
                'errors': errors
            }
        except Exception as e:
            logger.error(f"Error ingesting facility types from HFR: {str(e)}")
            raise

    def ingest_facilities_from_excel(self, file_path: str) -> Dict[str, Any]:
        """Ingest facilities from Excel file"""
        try:
            df = self.excel_service.process_facility_excel(file_path)
            created = 0
            updated = 0
            errors = 0

            for _, row in df.iterrows():
                try:
                    facility, created_flag = Facility.objects.update_or_create(
                        code=row['code'],
                        defaults={
                            'name': row['name'],
                            'facility_type': self._get_or_create_facility_type(row['facility_type']),
                            'province': row['province'],
                            'district': row['district'],
                            'municipality': row['municipality'],
                            'latitude': row.get('latitude'),
                            'longitude': row.get('longitude'),
                            'address': row.get('address'),
                            'contact_number': row.get('contact_number'),
                            'email': row.get('email'),
                            'is_active': row.get('is_active', True)
                        }
                    )
                    if created_flag:
                        created += 1
                    else:
                        updated += 1
                except Exception as e:
                    logger.error(f"Error processing facility {row.get('code')}: {str(e)}")
                    errors += 1

            return {
                'created': created,
                'updated': updated,
                'errors': errors
            }
        except Exception as e:
            logger.error(f"Error ingesting facilities from Excel: {str(e)}")
            raise

    def ingest_facility_types_from_excel(self, file_path: str) -> Dict[str, Any]:
        """Ingest facility types from Excel file"""
        try:
            df = self.excel_service.process_facility_type_excel(file_path)
            created = 0
            updated = 0
            errors = 0

            for _, row in df.iterrows():
                try:
                    facility_type, created_flag = FacilityType.objects.update_or_create(
                        code=row['code'],
                        defaults={
                            'name': row['name'],
                            'description': row.get('description', '')
                        }
                    )
                    if created_flag:
                        created += 1
                    else:
                        updated += 1
                except Exception as e:
                    logger.error(f"Error processing facility type {row.get('code')}: {str(e)}")
                    errors += 1

            return {
                'created': created,
                'updated': updated,
                'errors': errors
            }
        except Exception as e:
            logger.error(f"Error ingesting facility types from Excel: {str(e)}")
            raise

    def _get_or_create_facility_type(self, type_data: Dict[str, Any]) -> FacilityType:
        """Helper method to get or create a facility type"""
        if isinstance(type_data, dict):
            facility_type, _ = FacilityType.objects.get_or_create(
                code=type_data['code'],
                defaults={
                    'name': type_data['name'],
                    'description': type_data.get('description', '')
                }
            )
        else:
            facility_type, _ = FacilityType.objects.get_or_create(
                code=type_data,
                defaults={'name': type_data}
            )
        return facility_type 