# Facilities API Documentation

## Overview
The Facilities API provides endpoints for managing health facilities, facility types, facility levels, and authority levels in the HealthScope system.

## Base URL
```
/api/v1/facility/
```

## Authentication
All endpoints require JWT authentication. Include the token in the Authorization header:
```
Authorization: Bearer <your_jwt_token>
```

---

## Usage Examples

### Filtering Facilities by Facility Level

**Get all Health Posts:**
```bash
curl -X GET "http://localhost:8000/api/v1/facility/?facility_level_id=1" \
     -H "Authorization: Bearer <your_jwt_token>"
```

**Get all District Hospitals in a specific province:**
```bash
curl -X GET "http://localhost:8000/api/v1/facility/?facility_level_id=10&province_id=1" \
     -H "Authorization: Bearer <your_jwt_token>"
```

**Get all General Hospitals in a specific district:**
```bash
curl -X GET "http://localhost:8000/api/v1/facility/?facility_level_id=11&district_id=1" \
     -H "Authorization: Bearer <your_jwt_token>"
```

### Getting Facility Levels

**List all facility levels:**
```bash
curl -X GET "http://localhost:8000/api/v1/facility/facility-level/" \
     -H "Authorization: Bearer <your_jwt_token>"
```

**Search facility levels by name:**
```bash
curl -X GET "http://localhost:8000/api/v1/facility/facility-level/?search=hospital" \
     -H "Authorization: Bearer <your_jwt_token>"
```

### Combining Multiple Filters

**Get all Health Posts in a specific municipality:**
```bash
curl -X GET "http://localhost:8000/api/v1/facility/?facility_level_id=1&municipality_id=1" \
     -H "Authorization: Bearer <your_jwt_token>"
```

**Get all facilities with search and facility level filter:**
```bash
curl -X GET "http://localhost:8000/api/v1/facility/?search=health&facility_level_id=1" \
     -H "Authorization: Bearer <your_jwt_token>"
```

---

## Facility Endpoints

### List Facilities
**GET** `/api/v1/facility/`

Returns a list of all health facilities with search and filtering capabilities (no pagination).

**Query Parameters:**
- `search` (string): Search facilities by name, code, or location
- `facility_type` (int): Filter by facility type ID
- `facility_level_id` (int): Filter by facility level ID
- `authority_level` (int): Filter by authority level ID
- `ownership_id` (int): Filter by ownership ID
- `province_id` (int): Filter by province ID
- `district_id` (int): Filter by district ID
- `municipality_id` (int): Filter by municipality ID
- `ward_id` (int): Filter by ward ID
- `is_active` (boolean): Filter by active status

**Response:**
```json
{
  "count": 2,
  "results": [
    {
      "id": "9465bb26-27bf-48d0-90f4-d8068e48b64b",
      "facility_id": 1001,
      "name": "Central Hospital",
      "hf_code": "CH001",
      "hmis_code": "HMIS001",
      "facility_type": {
        "id": 1,
        "name": "Basic",
        "code": "BASIC"
      },
      "authority_level": {
        "id": 1,
        "name": "Central",
        "code": "CENT"
      },
      "facility_level": {
        "id": 1,
        "name": "Health Post",
        "code": "HP"
      },
      "ownership": {
        "id": 1,
        "name": "Public",
        "code": "PUBLIC"
      },
      "country": {
        "id": 1,
        "name": "Nepal",
        "code": "NPL"
      },
      "province": {
        "id": 1,
        "name": "Koshi",
        "code": "1"
      },
      "district": {
        "id": 1,
        "name": "Taplejung",
        "code": "101"
      },
      "municipality": {
        "id": 1,
        "name": "Phaktanlung Rural Municipality",
        "code": "10101"
      },
      "ward": {
        "id": 1,
        "name": "Ward 1",
        "code": "1010101"
      },
      "latitude": "27.717200",
      "longitude": "85.324000",
      "operational_status": "Operational",
      "is_active": true
    }
  ]
}
```

### Get Facility Details
**GET** `/api/v1/facility/{id}/`

Returns detailed information about a specific facility.

**Response:**
```json
{
  "id": "9465bb26-27bf-48d0-90f4-d8068e48b64b",
  "facility_id": 1001,
  "facility_type": {
    "id": 1,
    "name": "Basic",
    "code": "BASIC"
  },
  "facility_type_id": 1,
  "facility_type_category": {
    "id": 1,
    "name": "Government",
    "code": "GOV"
  },
  "facility_type_category_id": 1,
  "country": {
    "id": 1,
    "name": "Nepal",
    "code": "NPL"
  },
  "country_id": 1,
  "name": "Central Hospital",
  "hf_code": "CH001",
  "hmis_code": "HMIS001",
  "established_date_bs": "2075-01-01",
  "operational_status": "Operational",
  "internet_facility": "yes",
  "bed_count": 100,
  "functional_bed_count": 95,
  "province": {
    "id": 1,
    "country": 1,
    "country_name": "Nepal",
    "name": "Koshi",
    "code": "1",
    "is_active": true,
    "created_at": "2025-06-29T10:00:00Z",
    "updated_at": "2025-06-29T10:00:00Z"
  },
  "province_id": 1,
  "district": {
    "id": 1,
    "province": 1,
    "province_name": "Koshi",
    "country_name": "Nepal",
    "name": "Taplejung",
    "code": "101",
    "is_active": true,
    "created_at": "2025-06-29T10:00:00Z",
    "updated_at": "2025-06-29T10:00:00Z"
  },
  "district_id": 1,
  "municipality": {
    "id": 1,
    "district": 1,
    "district_name": "Taplejung",
    "province_name": "Koshi",
    "municipality_type": 1,
    "municipality_type_name": "Rural Municipality",
    "name": "Phaktanlung Rural Municipality",
    "code": "10101",
    "is_active": true,
    "created_at": "2025-06-29T10:00:00Z",
    "updated_at": "2025-06-29T10:00:00Z"
  },
  "municipality_id": 1,
  "ward": {
    "id": 1,
    "municipality": 1,
    "municipality_name": "Phaktanlung Rural Municipality",
    "district_name": "Taplejung",
    "province_name": "Koshi",
    "name": "Ward 1",
    "code": "1010101",
    "ward_number": 1,
    "is_active": true,
    "created_at": "2025-06-29T10:00:00Z",
    "updated_at": "2025-06-29T10:00:00Z"
  },
  "ward_id": 1,
  "latitude": "27.717200",
  "longitude": "85.324000",
  "parent_facility_type": null,
  "parent_facility_type_id": null,
  "parent_facility_nin": null,
  "fru_d": false,
  "fru_f": false,
  "dp_d": false,
  "dp_f": false,
  "hwc_d": false,
  "hwc_f": false,
  "nbsu_d": false,
  "nbsu_f": false,
  "sncu_d": false,
  "sncu_f": false,
  "authority_level": {
    "id": 1,
    "name": "Central",
    "code": "CENT"
  },
  "authority_level_id": 1,
  "authority": "Central Government",
  "ownership": {
    "id": 1,
    "name": "Public",
    "code": "PUBLIC"
  },
  "ownership_id": 1,
  "facility_level": {
    "id": 1,
    "name": "Tertiary",
    "code": "TERTIARY"
  },
  "facility_level_id": 1,
  "email": "<EMAIL>",
  "website": "https://hospital.example.com",
  "telephone": "01-1234567",
  "contact_person": "Dr. John Doe",
  "contact_person_mobile": "9851234567",
  "oxygen": "yes",
  "plant_capacity": "1000 LPM",
  "cylinder": "yes",
  "concentrator": "yes",
  "ambulance": "yes",
  "ambulance_category": "A",
  "ambulance_contact": "9851234568",
  "is_active": true,
  "created_at": "2025-06-29T10:00:00Z",
  "updated_at": "2025-06-29T10:00:00Z",
  "created_by": 1,
  "updated_by": 1
}
```

### Upload Facilities from Excel
**POST** `/api/v1/facility/facility/ingest-from-excel/`

Upload and import facilities from an Excel file.

**Request:**
- Content-Type: `multipart/form-data`
- Body: Form data with `file` field containing Excel file

**Response:**
```json
{
  "message": "Successfully imported 100 facilities",
  "imported": 100,
  "skipped": 5,
  "errors": []
}
```

### Import Facilities from HFR API
**POST** `/api/v1/facility/facility/ingest-from-hfr/`

Import facilities from the Health Facility Registry API.

**Request Body:**
```json
{
  "province_code": "1",
  "district_code": "101"
}
```

**Response:**
```json
{
  "message": "Successfully imported 50 facilities",
  "imported": 50,
  "skipped": 2,
  "errors": []
}
```

### Bulk Create Facilities from CSV
**POST** `/api/v1/facility/facility/bulk_create/`

Upload and import facilities from a CSV file.

**Request:**
- Content-Type: `multipart/form-data`
- Body: Form data with `file` field containing CSV file

**Response:**
```json
{
  "message": "Successfully processed 100 facilities",
  "created": 80,
  "updated": 20,
  "errors": []
}
```

---

## Facility Type Endpoints

### List Facility Types
**GET** `/api/v1/facility/facility-type/`

Returns a list of all facility types (no pagination).

**Query Parameters:**
- `search` (string): Search by name or code
- `name` (string): Filter by name
- `code` (string): Filter by code

**Response:**
```json
[
  {
    "id": 1,
    "name": "Government",
    "code": "GOVERNMENT"
  },
  {
    "id": 2,
    "name": "Non-Government",
    "code": "NON_GOVERNMENT"
  }
]
```

### Get Facility Type Details
**GET** `/api/v1/facility/facility-type/{id}/`

Returns detailed information about a specific facility type.

### Create Facility Type
**POST** `/api/v1/facility/facility-type/`

Create a new facility type.

**Request Body:**
```json
{
  "name": "New Facility Type",
  "code": "NEW_TYPE",
  "description": "Description of the facility type"
}
```

### Update Facility Type
**PUT** `/api/v1/facility/facility-type/{id}/`

Update an existing facility type.

### Delete Facility Type
**DELETE** `/api/v1/facility/facility-type/{id}/`

Delete a facility type.

### Import Facility Types from HFR
**POST** `/api/v1/facility/facility-type/ingest-from-hfr/`

Import facility types from the Health Facility Registry API.

### Import Facility Types from Excel
**POST** `/api/v1/facility/facility-type/ingest-from-excel/`

Import facility types from an Excel file.

---

## Authority Level Endpoints

### List Authority Levels
**GET** `/api/v1/facility/authority-level/`

Returns a list of all authority levels (no pagination).

**Response:**
```json
[
  {
    "id": 1,
    "name": "Federal",
    "code": "FE"
  },
  {
    "id": 2,
    "name": "Provincial",
    "code": "PR"
  },
  {
    "id": 3,
    "name": "Local",
    "code": "LO"
  }
]
```

### Get Authority Level Details
**GET** `/api/v1/facility/authority-level/{id}/`

Returns detailed information about a specific authority level.

### Create Authority Level
**POST** `/api/v1/facility/authority-level/`

Create a new authority level.

**Request Body:**
```json
{
  "name": "New Authority Level",
  "code": "NEW_AUTH",
  "description": "Description of the authority level"
}
```

### Update Authority Level
**PUT** `/api/v1/facility/authority-level/{id}/`

Update an existing authority level.

### Delete Authority Level
**DELETE** `/api/v1/facility/authority-level/{id}/`

Delete an authority level.

---

## Facility Level Endpoints

### List Facility Levels
**GET** `/api/v1/facility/facility-level/`

Returns a list of all facility levels (no pagination).

**Query Parameters:**
- `search` (string): Search by name, code, or description
- `name` (string): Filter by name
- `code` (string): Filter by code

**Response:**
```json
[
  {
    "id": 1,
    "name": "Health Post",
    "code": "HP"
  },
  {
    "id": 2,
    "name": "Basic Health Service Center",
    "code": "BHSC"
  },
  {
    "id": 3,
    "name": "Primary Health Center",
    "code": "PHC"
  },
  {
    "id": 4,
    "name": "District Hospital",
    "code": "DH"
  },
  {
    "id": 5,
    "name": "General Hospital",
    "code": "GH"
  }
]
```

### Get Facility Level Details
**GET** `/api/v1/facility/facility-level/{id}/`

Returns detailed information about a specific facility level.

**Response:**
```json
{
  "id": 1,
  "name": "Health Post",
  "code": "HP",
  "description": "Basic health post",
  "is_active": true,
  "created_at": "2025-06-29T10:00:00Z",
  "updated_at": "2025-06-29T10:00:00Z"
}
```

### Create Facility Level
**POST** `/api/v1/facility/facility-level/`

Create a new facility level.

**Request Body:**
```json
{
  "name": "New Facility Level",
  "code": "NEW_LEVEL",
  "description": "Description of the facility level"
}
```

### Update Facility Level
**PUT** `/api/v1/facility/facility-level/{id}/`

Update an existing facility level.

### Delete Facility Level
**DELETE** `/api/v1/facility/facility-level/{id}/`

Delete a facility level.

---

## Error Responses

### 400 Bad Request
```json
{
  "error": "Invalid request data",
  "details": {
    "field_name": ["Error message"]
  }
}
```

### 401 Unauthorized
```json
{
  "detail": "Authentication credentials were not provided."
}
```

### 403 Forbidden
```json
{
  "detail": "You do not have permission to perform this action."
}
```

### 404 Not Found
```json
{
  "detail": "Not found."
}
```

### 500 Internal Server Error
```json
{
  "error": "An unexpected error occurred"
}
```

---

## Notes

- **No Individual Facility Creation**: The facility endpoints do not support creating individual facilities via POST. Use the upload/import endpoints instead.
- **Location Data**: All location fields (country, province, district, municipality, ward) are simplified in list views with only id, name, and code.
- **Facility Level Data**: Facility level information is included in facility list responses and can be used for filtering.
- **Search**: The search functionality searches across facility name, code, and location names.
- **Filtering**: Multiple filters can be combined for precise data retrieval. Facility level filtering is particularly useful for categorizing facilities by their service level.
- **Pagination**: Facility list endpoint uses pagination, while reference data endpoints (facility types, authority levels, facility levels) return all results without pagination.
- **Authentication**: All endpoints require valid JWT authentication. 