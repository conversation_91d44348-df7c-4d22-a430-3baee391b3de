from django.shortcuts import render
from rest_framework import viewsets
from rest_framework.permissions import IsAuthenticated

# Create your views here.

class BaseModelViewSet(viewsets.ModelViewSet):
    """
    Base ViewSet that provides common functionality for all API versions.
    """
    permission_classes = [IsAuthenticated]
    
    def get_queryset(self):
        """
        Filter out soft-deleted records by default.
        Override this method in child classes if different behavior is needed.
        """
        queryset = self.queryset
        
        # Check if the model has is_deleted field
        if hasattr(self.queryset.model, 'is_deleted'):
            queryset = queryset.filter(is_deleted=False)
        # Check if the model has deleted_at field (AuditModel)
        elif hasattr(self.queryset.model, 'deleted_at'):
            queryset = queryset.filter(deleted_at__isnull=True)
        
        return queryset

    def perform_create(self, serializer):
        """Automatically set created_by to current user"""
        serializer.save(created_by=self.request.user)

    def perform_update(self, serializer):
        """Automatically set updated_by to current user"""
        serializer.save(updated_by=self.request.user)
