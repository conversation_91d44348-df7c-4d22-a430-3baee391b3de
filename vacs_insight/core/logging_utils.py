import logging
import functools
import time
from typing import Optional, Any, Dict, List
from django.conf import settings
import traceback

class HealthScopeLogger:
    """
    Common logging utility for HealthScope applications
    Provides consistent logging format and methods across all apps
    """
    
    def __init__(self, app_name: str, module_name: str = None):
        """
        Initialize logger for a specific app and module
        
        Args:
            app_name: Name of the Django app (e.g., 'user', 'location', 'facilities')
            module_name: Name of the module/component (e.g., 'views', 'models', 'services')
        """
        self.app_name = app_name
        self.module_name = module_name or 'general'
        self.logger = logging.getLogger(f'{app_name}.{module_name}')
    
    def _format_message(self, message: str, extra_data: Dict = None) -> str:
        """Format log message with app and module context"""
        formatted_msg = f"[{self.app_name.upper()}:{self.module_name.upper()}] {message}"
        if extra_data:
            formatted_msg += f" | Data: {extra_data}"
        return formatted_msg
    
    def info(self, message: str, extra_data: Dict = None, **kwargs):
        """Log info message"""
        self.logger.info(self._format_message(message, extra_data), **kwargs)
    
    def warning(self, message: str, extra_data: Dict = None, **kwargs):
        """Log warning message"""
        self.logger.warning(self._format_message(message, extra_data), **kwargs)
    
    def error(self, message: str, extra_data: Dict = None, exception: Exception = None, **kwargs):
        """Log error message with optional exception details"""
        if exception:
            extra_data = extra_data or {}
            extra_data['exception'] = str(exception)
            extra_data['traceback'] = traceback.format_exc()
        
        self.logger.error(self._format_message(message, extra_data), **kwargs)
    
    def debug(self, message: str, extra_data: Dict = None, **kwargs):
        """Log debug message"""
        self.logger.debug(self._format_message(message, extra_data), **kwargs)
    
    def critical(self, message: str, extra_data: Dict = None, exception: Exception = None, **kwargs):
        """Log critical message"""
        if exception:
            extra_data = extra_data or {}
            extra_data['exception'] = str(exception)
            extra_data['traceback'] = traceback.format_exc()
        
        self.logger.critical(self._format_message(message, extra_data), **kwargs)


def log_method_call(logger: HealthScopeLogger, method_name: str = None):
    """
    Decorator to log method calls with timing and result
    
    Usage:
        @log_method_call(logger, "create_user")
        def create_user(self, user_data):
            # method implementation
    """
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            method = method_name or func.__name__
            start_time = time.time()
            
            # Log method entry
            logger.info(f"Entering {method}", {
                'args_count': len(args),
                'kwargs_keys': list(kwargs.keys()) if kwargs else []
            })
            
            try:
                result = func(*args, **kwargs)
                execution_time = time.time() - start_time
                
                # Log successful completion
                logger.info(f"Completed {method} successfully", {
                    'execution_time': f"{execution_time:.3f}s",
                    'result_type': type(result).__name__
                })
                
                return result
                
            except Exception as e:
                execution_time = time.time() - start_time
                
                # Log error
                logger.error(f"Error in {method}", {
                    'execution_time': f"{execution_time:.3f}s",
                    'error_type': type(e).__name__
                }, exception=e)
                
                raise
        
        return wrapper
    return decorator


def log_api_request(logger: HealthScopeLogger, endpoint: str = None):
    """
    Decorator to log API request/response details
    
    Usage:
        @log_api_request(logger, "GET /api/v1/location/hierarchy")
        def hierarchy(self, request):
            # view method implementation
    """
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            method = endpoint or func.__name__
            start_time = time.time()
            
            # Extract request info
            request = None
            for arg in args:
                if hasattr(arg, 'method') and hasattr(arg, 'path'):
                    request = arg
                    break
            
            # Log request
            request_data = {}
            if request:
                request_data = {
                    'method': request.method,
                    'path': request.path,
                    'user': str(request.user) if hasattr(request, 'user') else 'Anonymous',
                    'ip': self._get_client_ip(request) if hasattr(self, '_get_client_ip') else 'Unknown'
                }
            
            logger.info(f"API Request: {method}", request_data)
            
            try:
                result = func(*args, **kwargs)
                execution_time = time.time() - start_time
                
                # Log response
                response_data = {
                    'execution_time': f"{execution_time:.3f}s",
                    'status_code': getattr(result, 'status_code', 'Unknown')
                }
                
                if hasattr(result, 'data'):
                    response_data['response_type'] = type(result.data).__name__
                
                logger.info(f"API Response: {method}", response_data)
                
                return result
                
            except Exception as e:
                execution_time = time.time() - start_time
                
                logger.error(f"API Error: {method}", {
                    'execution_time': f"{execution_time:.3f}s",
                    'error_type': type(e).__name__
                }, exception=e)
                
                raise
        
        return wrapper
    return decorator


def log_database_operation(logger: HealthScopeLogger, operation: str = None):
    """
    Decorator to log database operations
    
    Usage:
        @log_database_operation(logger, "create")
        def save(self, *args, **kwargs):
            # model save method
    """
    def decorator(func):
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            method = operation or func.__name__
            start_time = time.time()
            
            # Extract model info
            model_name = args[0].__class__.__name__ if args else 'Unknown'
            
            logger.info(f"Database {method} operation", {
                'model': model_name,
                'args_count': len(args),
                'kwargs_keys': list(kwargs.keys()) if kwargs else []
            })
            
            try:
                result = func(*args, **kwargs)
                execution_time = time.time() - start_time
                
                logger.info(f"Database {method} completed", {
                    'model': model_name,
                    'execution_time': f"{execution_time:.3f}s"
                })
                
                return result
                
            except Exception as e:
                execution_time = time.time() - start_time
                
                logger.error(f"Database {method} failed", {
                    'model': model_name,
                    'execution_time': f"{execution_time:.3f}s",
                    'error_type': type(e).__name__
                }, exception=e)
                
                raise
        
        return wrapper
    return decorator


# Convenience functions for common logging patterns
def get_app_logger(app_name: str, module_name: str = None) -> HealthScopeLogger:
    """Get a logger instance for a specific app and module"""
    return HealthScopeLogger(app_name, module_name)


def log_model_operations(model_class):
    """
    Class decorator to add logging to all model operations
    
    Usage:
        @log_model_operations
        class User(models.Model):
            # model definition
    """
    original_save = model_class.save
    original_delete = model_class.delete
    
    def logged_save(self, *args, **kwargs):
        logger = HealthScopeLogger('models', model_class.__name__.lower())
        return log_database_operation(logger, 'save')(original_save)(self, *args, **kwargs)
    
    def logged_delete(self, *args, **kwargs):
        logger = HealthScopeLogger('models', model_class.__name__.lower())
        return log_database_operation(logger, 'delete')(original_delete)(self, *args, **kwargs)
    
    model_class.save = logged_save
    model_class.delete = logged_delete
    
    return model_class


# Utility function to get client IP
def get_client_ip(request):
    """Extract client IP from request"""
    x_forwarded_for = request.META.get('HTTP_X_FORWARDED_FOR')
    if x_forwarded_for:
        ip = x_forwarded_for.split(',')[0]
    else:
        ip = request.META.get('REMOTE_ADDR')
    return ip 