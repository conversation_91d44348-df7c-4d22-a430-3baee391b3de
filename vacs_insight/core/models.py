from django.db import models
import uuid


class AuditModel(models.Model):
    """
    Abstract base model that provides audit fields for all models.
    """
    uuid = models.UUIDField(default=uuid.uuid4, editable=False, unique=True)
    created_at = models.DateTimeField(auto_now_add=True)
    created_by = models.ForeignKey('user.User', on_delete=models.SET_NULL, null=True, blank=True,
                                   related_name='%(class)s_created')
    updated_at = models.DateTimeField(auto_now=True)
    updated_by = models.ForeignKey('user.User', on_delete=models.SET_NULL, null=True, blank=True,
                                   related_name='%(class)s_updated')
    deleted_at = models.DateTimeField(null=True, blank=True)
    deleted_by = models.ForeignKey('user.User', on_delete=models.SET_NULL, null=True, blank=True,
                                   related_name='%(class)s_deleted')

    class Meta:
        abstract = True

# Location models have been moved to location/models.py
