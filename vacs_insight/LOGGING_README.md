# HealthScope Logging System

## Overview

The HealthScope logging system provides a centralized, consistent logging utility that can be used across all Django apps. It offers structured logging with context-aware formatting, performance monitoring, and comprehensive error tracking.

## Features

- **Structured Logging**: All log messages include app and module context
- **Performance Monitoring**: Built-in timing for API requests and database operations
- **Error Tracking**: Automatic exception capture with full stack traces
- **Request Context**: User, IP, and request details automatically included
- **Flexible Decorators**: Easy-to-use decorators for common logging patterns
- **Consistent Format**: Standardized log format across all applications

## Core Components

### 1. HealthScopeLogger Class

The main logging class that provides structured logging with context.

```python
from core.logging_utils import HealthScopeLogger

# Initialize logger for a specific app and module
logger = HealthScopeLogger('user', 'views')
logger = HealthScopeLogger('location', 'models')
logger = HealthScopeLogger('facilities', 'services')
```

#### Methods

- `info(message, extra_data=None, **kwargs)`: Log info messages
- `warning(message, extra_data=None, **kwargs)`: Log warning messages
- `error(message, extra_data=None, exception=None, **kwargs)`: Log error messages
- `debug(message, extra_data=None, **kwargs)`: Log debug messages
- `critical(message, extra_data=None, exception=None, **kwargs)`: Log critical messages

#### Example Usage

```python
logger.info("User logged in successfully", {
    'user_id': user.id,
    'email': user.email,
    'ip_address': request.META.get('REMOTE_ADDR')
})

logger.error("Database connection failed", {
    'database': 'postgresql',
    'operation': 'select'
}, exception=e)
```

### 2. Logging Decorators

#### @log_method_call

Logs method entry, execution time, and completion/error status.

```python
from core.logging_utils import log_method_call, HealthScopeLogger

logger = HealthScopeLogger('user', 'services')

@log_method_call(logger, "create_user")
def create_user(self, user_data):
    # Method implementation
    pass
```

#### @log_api_request

Logs API request/response details with timing and status codes.

```python
from core.logging_utils import log_api_request, HealthScopeLogger

logger = HealthScopeLogger('location', 'views')

@log_api_request(logger, "GET /api/v1/location/hierarchy")
def hierarchy(self, request):
    # View method implementation
    pass
```

#### @log_database_operation

Logs database operations with model information and timing.

```python
from core.logging_utils import log_database_operation, HealthScopeLogger

logger = HealthScopeLogger('user', 'models')

@log_database_operation(logger, "save")
def save(self, *args, **kwargs):
    # Model save method
    pass
```

### 3. Utility Functions

#### get_app_logger()

Convenience function to get a logger instance.

```python
from core.logging_utils import get_app_logger

logger = get_app_logger('user', 'views')
```

#### get_client_ip()

Extract client IP from request.

```python
from core.logging_utils import get_client_ip

ip = get_client_ip(request)
```

## Implementation Examples

### 1. Views Logging

```python
from core.logging_utils import HealthScopeLogger, get_client_ip

logger = HealthScopeLogger('location', 'views')

class LocationViewSet(BaseModelViewSet):
    
    def list(self, request, *args, **kwargs):
        logger.info("Listing locations", {
            'user': str(request.user),
            'ip': get_client_ip(request),
            'query_params': dict(request.query_params)
        })
        return super().list(request, *args, **kwargs)

    def create(self, request, *args, **kwargs):
        logger.info("Creating new location", {
            'user': str(request.user),
            'ip': get_client_ip(request),
            'data': request.data
        })
        return super().create(request, *args, **kwargs)
```

### 2. Models Logging

```python
from core.logging_utils import HealthScopeLogger

logger = HealthScopeLogger('location', 'models')

class Country(models.Model):
    
    def save(self, *args, **kwargs):
        logger.info(f"Saving country: {self.name}", {
            'country_id': self.id,
            'is_new': self.pk is None
        })
        super().save(*args, **kwargs)

    def clean(self):
        if not self.name:
            logger.error("Country validation failed: name is required")
            raise ValidationError("Country name is required")
```

### 3. Serializers Logging

```python
from core.logging_utils import HealthScopeLogger

logger = HealthScopeLogger('location', 'serializers')

class CountrySerializer(serializers.ModelSerializer):
    
    def to_representation(self, instance):
        logger.debug(f"Serializing country: {instance.name}", {
            'country_id': instance.id,
            'country_name': instance.name
        })
        return super().to_representation(instance)

    def validate(self, data):
        logger.debug("Validating country data", {
            'data_keys': list(data.keys()),
            'is_update': self.instance is not None
        })
        return super().validate(data)
```

### 4. Services Logging

```python
from core.logging_utils import HealthScopeLogger, log_method_call

logger = HealthScopeLogger('user', 'services')

class UserService:
    
    @log_method_call(logger, "create_user")
    def create_user(self, user_data):
        # Service implementation
        pass

    @log_method_call(logger, "update_user")
    def update_user(self, user_id, user_data):
        # Service implementation
        pass
```

## Log Format

All logs follow this consistent format:

```
[APP:MODULE] Message | Data: {'key': 'value'}
```

### Examples

```
[LOCATION:VIEWS] Listing countries | Data: {'user': '<EMAIL>', 'ip': '127.0.0.1'}
[USER:MODELS] Saving user: John Doe | Data: {'user_id': 123, 'is_new': True}
[FACILITIES:SERVICES] Database save operation | Data: {'model': 'Facility', 'args_count': 1}
```

## Configuration

### Django Settings

Add to your Django settings:

```python
LOGGING = {
    'version': 1,
    'disable_existing_loggers': False,
    'formatters': {
        'verbose': {
            'format': '{levelname} {asctime} {module} {process:d} {thread:d} {message}',
            'style': '{',
        },
        'simple': {
            'format': '{levelname} {message}',
            'style': '{',
        },
    },
    'handlers': {
        'file': {
            'level': 'INFO',
            'class': 'logging.FileHandler',
            'filename': 'logs/healthscope.log',
            'formatter': 'verbose',
        },
        'console': {
            'level': 'DEBUG',
            'class': 'logging.StreamHandler',
            'formatter': 'simple',
        },
    },
    'loggers': {
        'location': {
            'handlers': ['file', 'console'],
            'level': 'INFO',
            'propagate': True,
        },
        'user': {
            'handlers': ['file', 'console'],
            'level': 'INFO',
            'propagate': True,
        },
        'facilities': {
            'handlers': ['file', 'console'],
            'level': 'INFO',
            'propagate': True,
        },
    },
}
```

## Best Practices

### 1. Consistent Naming

Use consistent app and module names:
- Apps: `user`, `location`, `facilities`, `core`
- Modules: `views`, `models`, `serializers`, `services`, `utils`

### 2. Meaningful Context

Always include relevant context data:

```python
# Good
logger.info("User created", {
    'user_id': user.id,
    'email': user.email,
    'created_by': request.user.id
})

# Avoid
logger.info("User created")
```

### 3. Error Handling

Always include exception details for errors:

```python
try:
    # Some operation
    pass
except Exception as e:
    logger.error("Operation failed", {
        'operation': 'create_user',
        'user_data': user_data
    }, exception=e)
    raise
```

### 4. Performance Monitoring

Use decorators for automatic performance tracking:

```python
@log_method_call(logger, "process_data")
def process_data(self, data):
    # Method implementation
    pass
```

### 5. Security Considerations

- Never log sensitive data (passwords, tokens, personal information)
- Use debug level for detailed information
- Use info level for general operations
- Use warning level for potential issues
- Use error level for actual errors

## Migration Guide

### From Basic Logging

Replace:
```python
import logging
logger = logging.getLogger(__name__)
logger.info("Message")
```

With:
```python
from core.logging_utils import HealthScopeLogger
logger = HealthScopeLogger('app_name', 'module_name')
logger.info("Message", {'context': 'data'})
```

### Adding to Existing Views

```python
# Before
def list(self, request, *args, **kwargs):
    return super().list(request, *args, **kwargs)

# After
def list(self, request, *args, **kwargs):
    logger.info("Listing items", {
        'user': str(request.user),
        'ip': get_client_ip(request),
        'query_params': dict(request.query_params)
    })
    return super().list(request, *args, **kwargs)
```

## Troubleshooting

### Common Issues

1. **Logger not found**: Ensure the app is added to Django settings LOGGING configuration
2. **No output**: Check log level settings and handler configuration
3. **Performance impact**: Use debug level sparingly in production

### Debug Mode

Enable debug logging for troubleshooting:

```python
logger.debug("Debug information", {
    'debug_data': 'value'
})
```

## Conclusion

The HealthScope logging system provides a robust, consistent, and easy-to-use logging solution for all Django applications. It ensures that all operations are properly tracked, errors are captured with full context, and performance is monitored automatically.

For questions or issues, refer to the logging configuration in Django settings and ensure all apps are properly configured in the LOGGING dictionary. 