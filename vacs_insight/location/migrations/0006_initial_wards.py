from django.db import migrations
import csv
import os


def create_initial_wards(apps, schema_editor):
    Municipality = apps.get_model('location', 'Municipality')
    Ward = apps.get_model('location', 'Ward')
    
    # Read wards from CSV
    csv_file_path = os.path.join(os.path.dirname(__file__), 'files', 'ward_full.csv')
    
    with open(csv_file_path, 'r', encoding='utf-8') as file:
        reader = csv.DictReader(file)
        for row in reader:
            ward_code = row['Ward_ID']
            ward_number = int(row['Ward_Name'])
            municipality_code = row['Palika_ID']
            
            try:
                municipality = Municipality.objects.get(code=municipality_code)
                
                Ward.objects.get_or_create(
                    code=ward_code,
                    defaults={
                        'name': f'Ward {ward_number}',
                        'ward_number': ward_number,
                        'municipality': municipality,
                        'is_active': True
                    }
                )
            except Municipality.DoesNotExist:
                print(f"Municipality with code {municipality_code} not found for ward {ward_code}")


def reverse_wards(apps, schema_editor):
    Ward = apps.get_model('location', 'Ward')
    Ward.objects.all().delete()


class Migration(migrations.Migration):
    dependencies = [
        ('location', '0005_initial_municipalities'),
    ]

    operations = [
        migrations.RunPython(create_initial_wards, reverse_wards),
    ] 