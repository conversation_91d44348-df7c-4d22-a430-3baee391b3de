from django.db import migrations
import csv
import os


def create_initial_districts(apps, schema_editor):
    Province = apps.get_model('location', 'Province')
    District = apps.get_model('location', 'District')
    
    # Read districts from CSV
    csv_file_path = os.path.join(os.path.dirname(__file__), 'files', 'district_full.csv')
    
    with open(csv_file_path, 'r', encoding='utf-8') as file:
        reader = csv.DictReader(file)
        for row in reader:
            district_name = row['District_Name'].strip()
            district_code = row['District_ID']
            province_code = row['Province_ID']
            
            try:
                province = Province.objects.get(code=province_code)
                District.objects.get_or_create(
                    code=district_code,
                    defaults={
                        'name': district_name,
                        'province': province,
                        'is_active': True
                    }
                )
            except Province.DoesNotExist:
                print(f"Province with code {province_code} not found for district {district_name}")


def reverse_districts(apps, schema_editor):
    District = apps.get_model('location', 'District')
    District.objects.all().delete()


class Migration(migrations.Migration):
    dependencies = [
        ('location', '0003_initial_municipality_types'),
    ]

    operations = [
        migrations.RunPython(create_initial_districts, reverse_districts),
    ] 