from django.db import migrations
import csv
import os


def create_initial_country_and_provinces(apps, schema_editor):
    Country = apps.get_model('location', 'Country')
    Province = apps.get_model('location', 'Province')
    
    # Create Nepal as the default country
    nepal, created = Country.objects.get_or_create(
        code='NPL',
        defaults={
            'name': 'Nepal',
            'is_active': True
        }
    )
    
    # Read provinces from CSV
    csv_file_path = os.path.join(os.path.dirname(__file__), 'files', 'province.csv')
    
    with open(csv_file_path, 'r', encoding='utf-8') as file:
        reader = csv.DictReader(file)
        for row in reader:
            province_name = row['Province_Name'].strip()
            province_code = row['Province_ID']
            
            Province.objects.get_or_create(
                code=province_code,
                defaults={
                    'name': province_name,
                    'country': nepal,
                    'is_active': True
                }
            )


def reverse_country_and_provinces(apps, schema_editor):
    Country = apps.get_model('location', 'Country')
    Province = apps.get_model('location', 'Province')
    Province.objects.all().delete()
    Country.objects.all().delete()


class Migration(migrations.Migration):
    dependencies = [
        ('location', '0001_initial'),
    ]

    operations = [
        migrations.RunPython(create_initial_country_and_provinces, reverse_country_and_provinces),
    ] 