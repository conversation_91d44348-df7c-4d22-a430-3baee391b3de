from django.db import migrations
import csv
import os


def create_initial_municipalities(apps, schema_editor):
    District = apps.get_model('location', 'District')
    MunicipalityType = apps.get_model('location', 'MunicipalityType')
    Municipality = apps.get_model('location', 'Municipality')
    
    # Read municipalities from CSV
    csv_file_path = os.path.join(os.path.dirname(__file__), 'files', 'palika_full.csv')
    
    with open(csv_file_path, 'r', encoding='utf-8') as file:
        reader = csv.DictReader(file)
        for row in reader:
            municipality_name = row['Palika_Name'].strip()
            municipality_code = row['Palika_ID']
            district_code = row['District_ID']
            municipality_type_code = row['Palika_TypeID']
            
            try:
                district = District.objects.get(code=district_code)
                municipality_type = MunicipalityType.objects.get(code=municipality_type_code)
                
                Municipality.objects.get_or_create(
                    code=municipality_code,
                    defaults={
                        'name': municipality_name,
                        'district': district,
                        'municipality_type': municipality_type,
                        'is_active': True
                    }
                )
            except (District.DoesNotExist, MunicipalityType.DoesNotExist) as e:
                print(f"Error creating municipality {municipality_name}: {e}")


def reverse_municipalities(apps, schema_editor):
    Municipality = apps.get_model('location', 'Municipality')
    Municipality.objects.all().delete()


class Migration(migrations.Migration):
    dependencies = [
        ('location', '0004_initial_districts'),
    ]

    operations = [
        migrations.RunPython(create_initial_municipalities, reverse_municipalities),
    ] 