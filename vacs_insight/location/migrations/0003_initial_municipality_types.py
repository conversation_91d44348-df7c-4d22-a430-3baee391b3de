from django.db import migrations
import csv
import os


def create_initial_municipality_types(apps, schema_editor):
    MunicipalityType = apps.get_model('location', 'MunicipalityType')
    
    # Read municipality types from CSV
    csv_file_path = os.path.join(os.path.dirname(__file__), 'files', 'palikaType.csv')
    
    with open(csv_file_path, 'r', encoding='utf-8') as file:
        reader = csv.DictReader(file)
        for row in reader:
            type_name = row['PalikaType_Name'].strip()
            type_code = row['PalikaType_ID']
            
            MunicipalityType.objects.get_or_create(
                code=type_code,
                defaults={
                    'name': type_name,
                    'is_active': True
                }
            )


def reverse_municipality_types(apps, schema_editor):
    MunicipalityType = apps.get_model('location', 'MunicipalityType')
    MunicipalityType.objects.all().delete()


class Migration(migrations.Migration):
    dependencies = [
        ('location', '0002_initial_country_and_provinces'),
    ]

    operations = [
        migrations.RunPython(create_initial_municipality_types, reverse_municipality_types),
    ] 