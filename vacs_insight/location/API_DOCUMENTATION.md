# Location API Documentation

## Overview

The Location API provides endpoints for managing geographical data in a hierarchical structure: Country → Province → District → Municipality → Ward. This API is designed to support frontend applications that need to implement cascading dropdown filters.

## Base URL

```
/location/
```

## API Endpoints

### 1. Hierarchical Location Data (Recommended for Frontend)

#### GET `/location/hierarchy`

**Description**: Returns all location data in a nested hierarchical structure. This is the **recommended endpoint** for frontend applications as it allows client-side filtering without multiple API calls.

**Response Format**:
```json
{
    "status": "success",
    "message": "Hierarchical location data retrieved successfully",
    "data": {
        "countries": [
            {
                "id": 1,
                "name": "Nepal",
                "code": "NPL",
                "provinces": [
                    {
                        "id": 1,
                        "name": "Koshi",
                        "code": "1",
                        "districts": [
                            {
                                "id": 1,
                                "name": "Taplejung",
                                "code": "101",
                                "municipalities": [
                                    {
                                        "id": 1,
                                        "name": "Phaktanlung",
                                        "code": "10101",
                                        "municipality_type": "Rural Municipality",
                                        "wards": [
                                            {
                                                "id": 1,
                                                "name": "Ward 1",
                                                "code": "1010101",
                                                "ward_number": 1
                                            }
                                        ]
                                    }
                                ]
                            }
                        ]
                    }
                ]
            }
        ]
    }
}
```

**Frontend Usage Example**:
```javascript
// Fetch hierarchical data once
const response = await fetch('/location/hierarchy');
const data = await response.json();

// Store in state/context for filtering
const [locationData] = useState(data.data.countries);

// Filter provinces for dropdown
const provinces = locationData[0].provinces;

// Filter districts based on selected province
const getDistricts = (provinceId) => {
    const province = locationData[0].provinces.find(p => p.id === provinceId);
    return province ? province.districts : [];
};

// Filter municipalities based on selected district
const getMunicipalities = (provinceId, districtId) => {
    const province = locationData[0].provinces.find(p => p.id === provinceId);
    const district = province?.districts.find(d => d.id === districtId);
    return district ? district.municipalities : [];
};

// Filter wards based on selected municipality
const getWards = (provinceId, districtId, municipalityId) => {
    const province = locationData[0].provinces.find(p => p.id === provinceId);
    const district = province?.districts.find(d => d.id === districtId);
    const municipality = district?.municipalities.find(m => m.id === municipalityId);
    return municipality ? municipality.wards : [];
};
```

### 2. Flat Location Data

#### GET `/location/flat`

**Description**: Returns all location data in a flat structure with separate arrays for each level.

**Response Format**:
```json
{
    "status": "success",
    "message": "Flat location data retrieved successfully",
    "data": {
        "provinces": [
            {
                "id": 1,
                "country": 1,
                "country_name": "Nepal",
                "name": "Koshi",
                "code": "1",
                "is_active": true
            }
        ],
        "districts": [
            {
                "id": 1,
                "province": 1,
                "province_name": "Koshi",
                "country_name": "Nepal",
                "name": "Taplejung",
                "code": "101",
                "is_active": true
            }
        ],
        "municipalities": [
            {
                "id": 1,
                "district": 1,
                "district_name": "Taplejung",
                "province_name": "Koshi",
                "municipality_type": 1,
                "municipality_type_name": "Rural Municipality",
                "name": "Phaktanlung",
                "code": "10101",
                "is_active": true
            }
        ],
        "wards": [
            {
                "id": 1,
                "municipality": 1,
                "municipality_name": "Phaktanlung",
                "district_name": "Taplejung",
                "province_name": "Koshi",
                "name": "Ward 1",
                "code": "1010101",
                "ward_number": 1,
                "is_active": true
            }
        ]
    }
}
```

### 3. Location Counts

#### GET `/location/count`

**Description**: Returns counts of locations (provinces, districts, municipalities, wards) with optional filtering based on user authority level.

**Query Parameters**:
- `province_id` (optional) - Filter counts by specific province
- `district_id` (optional) - Filter counts by specific district
- `municipality_id` (optional) - Filter counts by specific municipality
- `ward_id` (optional) - Filter counts by specific ward

**Authority Validation**: Users can only filter by locations within their authority scope. For example:
- A province-level user can only filter by their assigned province
- A district-level user can only filter by their assigned district
- Center-level users can filter by any location

**Response Format**:
```json
{
    "status": "success",
    "message": "Location counts retrieved successfully",
    "data": {
        "total_provinces": 7,
        "total_districts": 77,
        "total_municipalities": 753,
        "total_wards": 6747
    }
}
```

**Usage Examples**:

1. **No filters (uses user's authority level)**:
   ```
   GET /api/location/count/
   ```

2. **Filter by province**:
   ```
   GET /api/location/count/?province_id=5
   ```

3. **Filter by district**:
   ```
   GET /api/location/count/?district_id=1
   ```

4. **Filter by municipality**:
   ```
   GET /api/location/count/?municipality_id=101
   ```

5. **Filter by ward**:
   ```
   GET /api/location/count/?ward_id=1001
   ```

**Error Responses**:

- **403 Forbidden** - When user tries to filter by unauthorized location:
  ```json
  {
    "status": "error",
    "message": "You are not authorized to access province_id: 1"
  }
  ```

- **400 Bad Request** - When invalid filter format is provided:
  ```json
  {
    "status": "error",
    "message": "Invalid province_id format"
  }
  ```

**Frontend Usage Example**:
```javascript
// Get counts for user's authority level
const getLocationCounts = async (filters = {}) => {
    const params = new URLSearchParams(filters);
    const response = await fetch(`/api/location/count/?${params}`);
    const data = await response.json();
    
    if (data.status === 'success') {
        return data.data;
    } else {
        throw new Error(data.message);
    }
};

// Usage examples
const allCounts = await getLocationCounts();
const provinceCounts = await getLocationCounts({ province_id: 5 });
const districtCounts = await getLocationCounts({ district_id: 1 });
```

### 4. Filtered Location Data

#### GET `/api/v1/location`

**Description**: Returns paginated location data with hierarchical filtering based on user authority level. The API returns different types of data based on the filter provided.

**Query Parameters**:
- `province_id` (optional) - Filter by specific province (returns districts)
- `district_id` (optional) - Filter by specific district (returns municipalities)
- `municipality_id` (optional) - Filter by specific municipality (returns wards)
- `ward_id` (optional) - Filter by specific ward (returns single ward)
- `page_number` (optional) - Page number (default: 1, minimum: 1)
- `page_size` (optional) - Items per page (default: 10, range: 1-100)

**Authority Validation**: Users can only filter by locations within their authority scope.

**Response Behavior**:
- **No filters**: Returns provinces based on user's authority level
- **province_id**: Returns districts in that province
- **district_id**: Returns municipalities in that district
- **municipality_id**: Returns wards in that municipality
- **ward_id**: Returns single ward details

**Response Format**:
```json
{
    "status": "success",
    "message": "Provinces retrieved successfully",
    "data": {
        "type": "provinces",
        "items": [
            {
                "id": 1,
                "country": 1,
                "country_name": "Nepal",
                "name": "Koshi",
                "code": "1",
                "is_active": true
            }
        ],
        "total_count": 7,
        "page_number": 1,
        "page_size": 10,
        "total_pages": 1
    }
}
```

**Usage Examples**:

1. **No filters (returns provinces)**:
   ```
   GET /api/v1/location
   GET /api/v1/location?page_number=1&page_size=5
   ```

2. **Filter by province (returns districts)**:
   ```
   GET /api/v1/location?province_id=5
   GET /api/v1/location?province_id=5&page_number=2&page_size=20
   ```

3. **Filter by district (returns municipalities)**:
   ```
   GET /api/v1/location?district_id=1
   GET /api/v1/location?district_id=1&page_number=1&page_size=15
   ```

4. **Filter by municipality (returns wards)**:
   ```
   GET /api/v1/location?municipality_id=101
   GET /api/v1/location?municipality_id=101&page_number=1&page_size=32
   ```

5. **Filter by ward (returns single ward)**:
   ```
   GET /api/v1/location?ward_id=1001
   ```

**Error Responses**:

- **403 Forbidden** - When user tries to filter by unauthorized location:
  ```json
  {
    "status": "error",
    "message": "You are not authorized to access province_id: 1"
  }
  ```

- **400 Bad Request** - When invalid parameters are provided:
  ```json
  {
    "status": "error",
    "message": "page_number must be greater than 0"
  }
  ```

- **404 Not Found** - When specified location doesn't exist:
  ```json
  {
    "status": "error",
    "message": "Ward with id 9999 not found"
  }
  ```

**Frontend Usage Example**:
```javascript
// Get filtered location data
const getFilteredLocations = async (filters = {}, pagination = {}) => {
    const params = new URLSearchParams({
        ...filters,
        page_number: pagination.page_number || 1,
        page_size: pagination.page_size || 10
    });
    
    const response = await fetch(`/api/v1/location?${params}`);
    const data = await response.json();
    
    if (data.status === 'success') {
        return data.data;
    } else {
        throw new Error(data.message);
    }
};

// Usage examples
const provinces = await getFilteredLocations();
const districts = await getFilteredLocations({ province_id: 5 });
const municipalities = await getFilteredLocations({ district_id: 1 }, { page_number: 2, page_size: 20 });
const wards = await getFilteredLocations({ municipality_id: 101 });
const ward = await getFilteredLocations({ ward_id: 1001 });
```

### 5. Individual Location Endpoints

#### Countries

- **GET** `/location/country` - List all countries
- **GET** `/location/country/{id}` - Get specific country
- **POST** `/location/country` - Create new country
- **PUT** `/location/country/{id}` - Update country
- **DELETE** `/location/country/{id}` - Delete country

#### Provinces

- **GET** `/location/province` - List all provinces
- **GET** `/location/province/{id}` - Get specific province
- **POST** `/location/province` - Create new province
- **PUT** `/location/province/{id}` - Update province
- **DELETE** `/location/province/{id}` - Delete province

**Query Parameters**:
- `country` - Filter by country ID
- `search` - Search in name and code
- `is_active` - Filter by active status

#### Districts

- **GET** `/location/district` - List all districts
- **GET** `/location/district/{id}` - Get specific district
- **POST** `/location/district` - Create new district
- **PUT** `/location/district/{id}` - Update district
- **DELETE** `/location/district/{id}` - Delete district

**Query Parameters**:
- `province` - Filter by province ID
- `search` - Search in name and code
- `is_active` - Filter by active status

#### Municipalities

- **GET** `/location/municipality` - List all municipalities
- **GET** `/location/municipality/{id}` - Get specific municipality
- **POST** `/location/municipality` - Create new municipality
- **PUT** `/location/municipality/{id}` - Update municipality
- **DELETE** `/location/municipality/{id}` - Delete municipality

**Query Parameters**:
- `district` - Filter by district ID
- `municipality_type` - Filter by municipality type ID
- `search` - Search in name and code
- `is_active` - Filter by active status

#### Wards

- **GET** `/location/ward` - List all wards
- **GET** `/location/ward/{id}` - Get specific ward
- **POST** `/location/ward` - Create new ward
- **PUT** `/location/ward/{id}` - Update ward
- **DELETE** `/location/ward/{id}` - Delete ward

**Query Parameters**:
- `municipality` - Filter by municipality ID
- `ward_number` - Filter by ward number
- `search` - Search in name and code
- `is_active` - Filter by active status

#### Municipality Types

- **GET** `/location/municipality-type` - List all municipality types
- **GET** `/location/municipality-type/{id}` - Get specific municipality type
- **POST** `/location/municipality-type` - Create new municipality type
- **PUT** `/location/municipality-type/{id}` - Update municipality type
- **DELETE** `/location/municipality-type/{id}` - Delete municipality type

## Frontend Implementation Guide

### Recommended Approach: Hierarchical Data

1. **Initial Load**: Call `/location/hierarchy` once when the application loads
2. **Store Data**: Cache the response in your application state/context
3. **Client-side Filtering**: Implement filtering logic in your frontend

### Example React Implementation

```jsx
import React, { useState, useEffect } from 'react';

const LocationFilter = () => {
    const [locationData, setLocationData] = useState(null);
    const [selectedProvince, setSelectedProvince] = useState(null);
    const [selectedDistrict, setSelectedDistrict] = useState(null);
    const [selectedMunicipality, setSelectedMunicipality] = useState(null);
    const [selectedWard, setSelectedWard] = useState(null);

    useEffect(() => {
        // Fetch hierarchical data once
        fetch('/location/hierarchy')
            .then(response => response.json())
            .then(data => setLocationData(data.data.countries[0]));
    }, []);

    if (!locationData) return <div>Loading...</div>;

    const provinces = locationData.provinces;
    const districts = selectedProvince 
        ? provinces.find(p => p.id === selectedProvince)?.districts || []
        : [];
    const municipalities = selectedDistrict
        ? districts.find(d => d.id === selectedDistrict)?.municipalities || []
        : [];
    const wards = selectedMunicipality
        ? municipalities.find(m => m.id === selectedMunicipality)?.wards || []
        : [];

    return (
        <div>
            <select 
                value={selectedProvince || ''} 
                onChange={(e) => {
                    setSelectedProvince(Number(e.target.value));
                    setSelectedDistrict(null);
                    setSelectedMunicipality(null);
                    setSelectedWard(null);
                }}
            >
                <option value="">Select Province</option>
                {provinces.map(province => (
                    <option key={province.id} value={province.id}>
                        {province.name}
                    </option>
                ))}
            </select>

            <select 
                value={selectedDistrict || ''} 
                onChange={(e) => {
                    setSelectedDistrict(Number(e.target.value));
                    setSelectedMunicipality(null);
                    setSelectedWard(null);
                }}
                disabled={!selectedProvince}
            >
                <option value="">Select District</option>
                {districts.map(district => (
                    <option key={district.id} value={district.id}>
                        {district.name}
                    </option>
                ))}
            </select>

            <select 
                value={selectedMunicipality || ''} 
                onChange={(e) => {
                    setSelectedMunicipality(Number(e.target.value));
                    setSelectedWard(null);
                }}
                disabled={!selectedDistrict}
            >
                <option value="">Select Municipality</option>
                {municipalities.map(municipality => (
                    <option key={municipality.id} value={municipality.id}>
                        {municipality.name} ({municipality.municipality_type})
                    </option>
                ))}
            </select>

            <select 
                value={selectedWard || ''} 
                onChange={(e) => setSelectedWard(Number(e.target.value))}
                disabled={!selectedMunicipality}
            >
                <option value="">Select Ward</option>
                {wards.map(ward => (
                    <option key={ward.id} value={ward.id}>
                        {ward.name}
                    </option>
                ))}
            </select>
        </div>
    );
};

export default LocationFilter;
```

## Benefits of This Approach

1. **Single API Call**: Only one request needed to get all location data
2. **Fast Filtering**: Client-side filtering is instant
3. **Reduced Server Load**: No multiple requests for each dropdown change
4. **Better UX**: No loading states between dropdown selections
5. **Caching**: Data can be cached and reused across sessions
6. **Offline Support**: Data can be stored locally for offline use

## Error Handling

All endpoints return consistent error responses:

```json
{
    "status": "error",
    "message": "Error description",
    "error": "Detailed error information"
}
```

## Authentication

All endpoints require authentication. Include the JWT token in the Authorization header:

```
Authorization: Bearer <your-jwt-token>
``` 