from django.core.management.base import BaseCommand
from django.core.cache import cache
from core.logging_utils import HealthScopeLogger

logger = HealthScopeLogger('location', 'management')


class Command(BaseCommand):
    help = 'Clear location cache for all users'

    def add_arguments(self, parser):
        parser.add_argument(
            '--force',
            action='store_true',
            help='Force clear cache without confirmation',
        )

    def handle(self, *args, **options):
        if not options['force']:
            confirm = input('Are you sure you want to clear all location cache? (yes/no): ')
            if confirm.lower() != 'yes':
                self.stdout.write(self.style.WARNING('Cache clear cancelled.'))
                return

        # Increment cache version to invalidate all cached data
        current_version = cache.get('location_cache_version', 0)
        new_version = current_version + 1
        cache.set('location_cache_version', new_version, timeout=None)
        
        logger.info(f"Location cache manually cleared", {
            'old_version': current_version,
            'new_version': new_version
        })
        
        self.stdout.write(
            self.style.SUCCESS(f'Successfully cleared location cache. Version updated from {current_version} to {new_version}')
        ) 