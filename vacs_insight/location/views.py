from rest_framework import viewsets, status
from rest_framework.response import Response
from rest_framework.decorators import action
from core.views import BaseModelViewSet
from .models import Country, Province, District, MunicipalityType, Municipality, Ward
from .serializers import (
    CountrySerializer, ProvinceSerializer, DistrictSerializer,
    MunicipalityTypeSerializer, MunicipalitySerializer, WardSerializer
)
from core.logging_utils import <PERSON><PERSON>cope<PERSON>ogger, log_api_request, get_client_ip
from django.core.cache import cache
from django.utils.decorators import method_decorator
from django.views.decorators.cache import cache_page
from functools import wraps
import hashlib
import json
from django.db.models import Q

# Initialize logger for location views
logger = HealthScopeLogger('location', 'views')


def user_specific_cache(timeout=3600):
    """
    Custom cache decorator that creates user-specific cache keys
    """
    def decorator(func):
        @wraps(func)
        def wrapper(self, request, *args, **kwargs):
            # Create a unique cache key based on user ID, authority level, and cache version
            user = request.user
            cache_version = cache.get('location_cache_version', 0)
            
            # Determine endpoint type from function name
            endpoint_type = 'flat' if func.__name__ == 'flat' else 'hierarchy'
            cache_key = f"location_{endpoint_type}_{user.id}_{user.authority_level}_{user.authority_location_id}_v{cache_version}"
            
            # Try to get from cache first
            cached_data = cache.get(cache_key)
            if cached_data is not None:
                logger.info(f"Cache hit for location {endpoint_type} data", {
                    'user': str(request.user),
                    'cache_key': cache_key,
                    'ip': get_client_ip(request)
                })
                return Response(cached_data, status=status.HTTP_200_OK)
            
            # If not in cache, execute the function
            logger.info(f"Cache miss for location {endpoint_type} data", {
                'user': str(request.user),
                'cache_key': cache_key,
                'ip': get_client_ip(request)
            })
            
            response = func(self, request, *args, **kwargs)
            
            # Cache the response data if it's successful
            if response.status_code == 200:
                cache.set(cache_key, response.data, timeout)
                logger.info(f"Cached location {endpoint_type} data", {
                    'user': str(request.user),
                    'cache_key': cache_key,
                    'timeout': timeout,
                    'ip': get_client_ip(request)
                })
            
            return response
        return wrapper
    return decorator


class CountryViewSet(BaseModelViewSet):
    queryset = Country.objects.all()
    serializer_class = CountrySerializer
    filterset_fields = ['name', 'code', 'is_active']
    search_fields = ['name', 'code']

    def get_queryset(self):
        """Filter queryset based on user authority level"""
        queryset = super().get_queryset()
        
        # Center level users can access all countries
        if self.request.user.authority_level == "center":
            return queryset.filter(is_active=True)
        
        # Other authority levels don't need country-level access for now
        # Return empty queryset for non-center users
        return queryset.none()

    def list(self, request, *args, **kwargs):
        """
        Get paginated country data with filtering and search
        
        Query Parameters:
        - page_number: Page number (default: 1)
        - page_size: Items per page (default: 10, max: 100)
        - search: Search string for name, code
        - is_active: Filter by active status
        - sort_by: Field to sort by (name, code, created_at, updated_at)
        - sort_order: Sort order (asc, desc) - default: asc
        """
        try:
            logger.info("Fetching country list", {
                'user': str(request.user),
                'ip': get_client_ip(request),
                'query_params': dict(request.query_params)
            })

            # Get pagination parameters
            try:
                page_number = int(request.query_params.get('page_number', 1))
                page_size = int(request.query_params.get('page_size', 10))
                
                if page_number < 1:
                    return Response({
                        'status': 'error',
                        'message': 'page_number must be greater than 0'
                    }, status=status.HTTP_400_BAD_REQUEST)
                
                if page_size < 1 or page_size > 100:
                    return Response({
                        'status': 'error',
                        'message': 'page_size must be between 1 and 100'
                    }, status=status.HTTP_400_BAD_REQUEST)
                    
            except ValueError:
                return Response({
                    'status': 'error',
                    'message': 'Invalid page_number or page_size format'
                }, status=status.HTTP_400_BAD_REQUEST)

            # Get the filtered queryset
            queryset = self.get_queryset()
            
            # Apply search if provided
            search_query = request.query_params.get('search', '')
            if search_query:
                queryset = queryset.filter(
                    Q(name__icontains=search_query) |
                    Q(code__icontains=search_query)
                )
            
            # Apply sorting
            sort_by = request.query_params.get('sort_by', 'name')
            sort_order = request.query_params.get('sort_order', 'asc').lower()
            
            # Validate sort_by field
            allowed_sort_fields = ['name', 'code', 'created_at', 'updated_at', 'is_active']
            
            if sort_by not in allowed_sort_fields:
                return Response({
                    'status': 'error',
                    'message': f'Invalid sort_by field. Allowed fields: {", ".join(allowed_sort_fields)}'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # Validate sort_order
            if sort_order not in ['asc', 'desc']:
                return Response({
                    'status': 'error',
                    'message': 'sort_order must be either "asc" or "desc"'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # Apply sorting
            if sort_order == 'desc':
                sort_by = f'-{sort_by}'
            
            queryset = queryset.order_by(sort_by)
            
            # Get total count
            total_count = queryset.count()
            
            # Apply pagination
            start_index = (page_number - 1) * page_size
            end_index = start_index + page_size
            paginated_queryset = queryset[start_index:end_index]
            
            # Serialize the data
            serializer = self.get_serializer(paginated_queryset, many=True)
            total_pages = (total_count + page_size - 1) // page_size
            
            logger.info(f"Successfully fetched country list", {
                'total_count': total_count,
                'page_number': page_number,
                'page_size': page_size,
                'sort_by': sort_by,
                'sort_order': sort_order,
                'user': str(request.user),
                'ip': get_client_ip(request)
            })
            
            return Response({
                'status': 'success',
                'message': 'Countries retrieved successfully',
                'data': {
                    'type': 'countries',
                    'items': serializer.data,
                    'total_count': total_count,
                    'page_number': page_number,
                    'page_size': page_size,
                    'total_pages': total_pages,
                    'sort_by': sort_by.replace('-', '') if sort_by.startswith('-') else sort_by,
                    'sort_order': sort_order
                }
            }, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error(f"Error fetching country list", {
                'user': str(request.user),
                'ip': get_client_ip(request),
                'error_type': type(e).__name__
            }, exception=e)
            return Response({
                'status': 'error',
                'message': 'Failed to fetch country list',
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def retrieve(self, request, *args, **kwargs):
        logger.info(f"Retrieving country with id: {kwargs.get('pk')}", {
            'user': str(request.user),
            'ip': get_client_ip(request),
            'country_id': kwargs.get('pk')
        })
        return super().retrieve(request, *args, **kwargs)


class ProvinceViewSet(BaseModelViewSet):
    queryset = Province.objects.all()
    serializer_class = ProvinceSerializer
    filterset_fields = ['country', 'name', 'code', 'is_active']
    search_fields = ['name', 'code']

    def get_queryset(self):
        """Filter queryset based on user authority level"""
        queryset = super().get_queryset()
        
        user = self.request.user
        accessible_locations = user.get_accessible_location_ids()
        
        # Filter provinces based on user's accessible locations
        if accessible_locations['provinces']:
            return queryset.filter(id__in=accessible_locations['provinces'], is_active=True)
        
        return queryset.none()

    def list(self, request, *args, **kwargs):
        """
        Get paginated province data with filtering and search
        
        Query Parameters:
        - page_number: Page number (default: 1)
        - page_size: Items per page (default: 10, max: 100)
        - search: Search string for name, code
        - country: Filter by country ID
        - is_active: Filter by active status
        - sort_by: Field to sort by (name, code, created_at, updated_at)
        - sort_order: Sort order (asc, desc) - default: asc
        """
        try:
            logger.info("Fetching province list", {
                'user': str(request.user),
                'ip': get_client_ip(request),
                'query_params': dict(request.query_params)
            })

            # Get pagination parameters
            try:
                page_number = int(request.query_params.get('page_number', 1))
                page_size = int(request.query_params.get('page_size', 10))
                
                if page_number < 1:
                    return Response({
                        'status': 'error',
                        'message': 'page_number must be greater than 0'
                    }, status=status.HTTP_400_BAD_REQUEST)
                
                if page_size < 1 or page_size > 100:
                    return Response({
                        'status': 'error',
                        'message': 'page_size must be between 1 and 100'
                    }, status=status.HTTP_400_BAD_REQUEST)
                    
            except ValueError:
                return Response({
                    'status': 'error',
                    'message': 'Invalid page_number or page_size format'
                }, status=status.HTTP_400_BAD_REQUEST)

            # Get the filtered queryset
            queryset = self.get_queryset()
            
            # Apply search if provided
            search_query = request.query_params.get('search', '')
            if search_query:
                queryset = queryset.filter(
                    Q(name__icontains=search_query) |
                    Q(code__icontains=search_query)
                )
            
            # Apply sorting
            sort_by = request.query_params.get('sort_by', 'name')
            sort_order = request.query_params.get('sort_order', 'asc').lower()
            
            # Validate sort_by field
            allowed_sort_fields = ['name', 'code', 'created_at', 'updated_at', 'is_active', 'country__name']
            
            if sort_by not in allowed_sort_fields:
                return Response({
                    'status': 'error',
                    'message': f'Invalid sort_by field. Allowed fields: {", ".join(allowed_sort_fields)}'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # Validate sort_order
            if sort_order not in ['asc', 'desc']:
                return Response({
                    'status': 'error',
                    'message': 'sort_order must be either "asc" or "desc"'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # Apply sorting
            if sort_order == 'desc':
                sort_by = f'-{sort_by}'
            
            queryset = queryset.order_by(sort_by)
            
            # Get total count
            total_count = queryset.count()
            
            # Apply pagination
            start_index = (page_number - 1) * page_size
            end_index = start_index + page_size
            paginated_queryset = queryset[start_index:end_index]
            
            # Serialize the data
            serializer = self.get_serializer(paginated_queryset, many=True)
            total_pages = (total_count + page_size - 1) // page_size
            
            logger.info(f"Successfully fetched province list", {
                'total_count': total_count,
                'page_number': page_number,
                'page_size': page_size,
                'sort_by': sort_by,
                'sort_order': sort_order,
                'user': str(request.user),
                'ip': get_client_ip(request)
            })
            
            return Response({
                'status': 'success',
                'message': 'Provinces retrieved successfully',
                'data': {
                    'type': 'provinces',
                    'items': serializer.data,
                    'total_count': total_count,
                    'page_number': page_number,
                    'page_size': page_size,
                    'total_pages': total_pages,
                    'sort_by': sort_by.replace('-', '') if sort_by.startswith('-') else sort_by,
                    'sort_order': sort_order
                }
            }, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error(f"Error fetching province list", {
                'user': str(request.user),
                'ip': get_client_ip(request),
                'error_type': type(e).__name__
            }, exception=e)
            return Response({
                'status': 'error',
                'message': 'Failed to fetch province list',
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def retrieve(self, request, *args, **kwargs):
        logger.info(f"Retrieving province with id: {kwargs.get('pk')}", {
            'user': str(request.user),
            'ip': get_client_ip(request),
            'province_id': kwargs.get('pk')
        })
        return super().retrieve(request, *args, **kwargs)


class DistrictViewSet(BaseModelViewSet):
    queryset = District.objects.all()
    serializer_class = DistrictSerializer
    filterset_fields = ['province', 'name', 'code', 'is_active']
    search_fields = ['name', 'code']

    def get_queryset(self):
        """Filter queryset based on user authority level"""
        queryset = super().get_queryset()
        
        user = self.request.user
        accessible_locations = user.get_accessible_location_ids()
        
        # Filter districts based on user's accessible locations
        if accessible_locations['districts']:
            return queryset.filter(id__in=accessible_locations['districts'], is_active=True)
        
        return queryset.none()

    def list(self, request, *args, **kwargs):
        """
        Get paginated district data with filtering and search
        
        Query Parameters:
        - page_number: Page number (default: 1)
        - page_size: Items per page (default: 10, max: 100)
        - search: Search string for name, code
        - province: Filter by province ID
        - is_active: Filter by active status
        - sort_by: Field to sort by (name, code, created_at, updated_at)
        - sort_order: Sort order (asc, desc) - default: asc
        """
        try:
            logger.info("Fetching district list", {
                'user': str(request.user),
                'ip': get_client_ip(request),
                'query_params': dict(request.query_params)
            })

            # Get pagination parameters
            try:
                page_number = int(request.query_params.get('page_number', 1))
                page_size = int(request.query_params.get('page_size', 10))
                
                if page_number < 1:
                    return Response({
                        'status': 'error',
                        'message': 'page_number must be greater than 0'
                    }, status=status.HTTP_400_BAD_REQUEST)
                
                if page_size < 1 or page_size > 100:
                    return Response({
                        'status': 'error',
                        'message': 'page_size must be between 1 and 100'
                    }, status=status.HTTP_400_BAD_REQUEST)
                    
            except ValueError:
                return Response({
                    'status': 'error',
                    'message': 'Invalid page_number or page_size format'
                }, status=status.HTTP_400_BAD_REQUEST)

            # Get the filtered queryset
            queryset = self.get_queryset()
            
            # Apply search if provided
            search_query = request.query_params.get('search', '')
            if search_query:
                queryset = queryset.filter(
                    Q(name__icontains=search_query) |
                    Q(code__icontains=search_query)
                )
            
            # Apply sorting
            sort_by = request.query_params.get('sort_by', 'name')
            sort_order = request.query_params.get('sort_order', 'asc').lower()
            
            # Validate sort_by field
            allowed_sort_fields = ['name', 'code', 'created_at', 'updated_at', 'is_active', 'province__name']
            
            if sort_by not in allowed_sort_fields:
                return Response({
                    'status': 'error',
                    'message': f'Invalid sort_by field. Allowed fields: {", ".join(allowed_sort_fields)}'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # Validate sort_order
            if sort_order not in ['asc', 'desc']:
                return Response({
                    'status': 'error',
                    'message': 'sort_order must be either "asc" or "desc"'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # Apply sorting
            if sort_order == 'desc':
                sort_by = f'-{sort_by}'
            
            queryset = queryset.order_by(sort_by)
            
            # Get total count
            total_count = queryset.count()
            
            # Apply pagination
            start_index = (page_number - 1) * page_size
            end_index = start_index + page_size
            paginated_queryset = queryset[start_index:end_index]
            
            # Serialize the data
            serializer = self.get_serializer(paginated_queryset, many=True)
            total_pages = (total_count + page_size - 1) // page_size
            
            logger.info(f"Successfully fetched district list", {
                'total_count': total_count,
                'page_number': page_number,
                'page_size': page_size,
                'sort_by': sort_by,
                'sort_order': sort_order,
                'user': str(request.user),
                'ip': get_client_ip(request)
            })
            
            return Response({
                'status': 'success',
                'message': 'Districts retrieved successfully',
                'data': {
                    'type': 'districts',
                    'items': serializer.data,
                    'total_count': total_count,
                    'page_number': page_number,
                    'page_size': page_size,
                    'total_pages': total_pages,
                    'sort_by': sort_by.replace('-', '') if sort_by.startswith('-') else sort_by,
                    'sort_order': sort_order
                }
            }, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error(f"Error fetching district list", {
                'user': str(request.user),
                'ip': get_client_ip(request),
                'error_type': type(e).__name__
            }, exception=e)
            return Response({
                'status': 'error',
                'message': 'Failed to fetch district list',
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def retrieve(self, request, *args, **kwargs):
        logger.info(f"Retrieving district with id: {kwargs.get('pk')}", {
            'user': str(request.user),
            'ip': get_client_ip(request),
            'district_id': kwargs.get('pk')
        })
        return super().retrieve(request, *args, **kwargs)


class MunicipalityTypeViewSet(BaseModelViewSet):
    queryset = MunicipalityType.objects.all()
    serializer_class = MunicipalityTypeSerializer
    filterset_fields = ['name', 'code', 'is_active']
    search_fields = ['name', 'code']

    def get_queryset(self):
        """Filter queryset based on user authority level"""
        queryset = super().get_queryset()
        
        # Municipality types are accessible to all users as they are reference data
        return queryset.filter(is_active=True)

    def list(self, request, *args, **kwargs):
        logger.info("Listing municipality types", {
            'user': str(request.user),
            'ip': get_client_ip(request),
            'query_params': dict(request.query_params)
        })
        return super().list(request, *args, **kwargs)

    def retrieve(self, request, *args, **kwargs):
        logger.info(f"Retrieving municipality type with id: {kwargs.get('pk')}", {
            'user': str(request.user),
            'ip': get_client_ip(request),
            'municipality_type_id': kwargs.get('pk')
        })
        return super().retrieve(request, *args, **kwargs)


class MunicipalityViewSet(BaseModelViewSet):
    queryset = Municipality.objects.all()
    serializer_class = MunicipalitySerializer
    filterset_fields = ['district', 'municipality_type', 'name', 'code', 'is_active']
    search_fields = ['name', 'code']

    def get_queryset(self):
        """Filter queryset based on user authority level"""
        queryset = super().get_queryset()
        
        user = self.request.user
        accessible_locations = user.get_accessible_location_ids()
        
        # Filter municipalities based on user's accessible locations
        if accessible_locations['municipalities']:
            return queryset.filter(id__in=accessible_locations['municipalities'], is_active=True)
        
        return queryset.none()

    def list(self, request, *args, **kwargs):
        logger.info("Listing municipalities", {
            'user': str(request.user),
            'ip': get_client_ip(request),
            'query_params': dict(request.query_params)
        })
        return super().list(request, *args, **kwargs)

    def retrieve(self, request, *args, **kwargs):
        logger.info(f"Retrieving municipality with id: {kwargs.get('pk')}", {
            'user': str(request.user),
            'ip': get_client_ip(request),
            'municipality_id': kwargs.get('pk')
        })
        return super().retrieve(request, *args, **kwargs)


class WardViewSet(BaseModelViewSet):
    queryset = Ward.objects.all()
    serializer_class = WardSerializer
    filterset_fields = ['municipality', 'ward_number', 'name', 'code', 'is_active']
    search_fields = ['name', 'code']

    def get_queryset(self):
        """Filter queryset based on user authority level"""
        queryset = super().get_queryset()
        
        user = self.request.user
        accessible_locations = user.get_accessible_location_ids()
        
        # Filter wards based on user's accessible locations
        if accessible_locations['wards']:
            return queryset.filter(id__in=accessible_locations['wards'], is_active=True)
        
        return queryset.none()

    def list(self, request, *args, **kwargs):
        logger.info("Listing wards", {
            'user': str(request.user),
            'ip': get_client_ip(request),
            'query_params': dict(request.query_params)
        })
        return super().list(request, *args, **kwargs)

    def retrieve(self, request, *args, **kwargs):
        logger.info(f"Retrieving ward with id: {kwargs.get('pk')}", {
            'user': str(request.user),
            'ip': get_client_ip(request),
            'ward_id': kwargs.get('pk')
        })
        return super().retrieve(request, *args, **kwargs)


class LocationHierarchyViewSet(BaseModelViewSet):
    """ViewSet for location hierarchy operations"""
    
    def list(self, request):
        """
        Get paginated location data with filtering based on user authority level
        
        Query Parameters:
        - province_id: Filter by specific province (returns districts)
        - district_id: Filter by specific district (returns municipalities)
        - municipality_id: Filter by specific municipality (returns wards)
        - ward_id: Filter by specific ward (returns single ward)
        - page_number: Page number (default: 1)
        - page_size: Items per page (default: 10)
        - sort_by: Field to sort by (name, code, created_at, updated_at)
        - sort_order: Sort order (asc, desc) - default: asc
        
        Returns:
        - No filters: List of provinces based on authority level
        - province_id: List of districts in that province
        - district_id: List of municipalities in that district
        - municipality_id: List of wards in that municipality
        - ward_id: Single ward details
        """
        try:
            logger.info("Fetching filtered location data", {
                'user': str(request.user),
                'ip': get_client_ip(request),
                'endpoint': 'list',
                'query_params': dict(request.query_params)
            })

            user = request.user
            accessible_locations = user.get_accessible_location_ids()
            
            # Get filter parameters
            province_id = request.query_params.get('province_id')
            district_id = request.query_params.get('district_id')
            municipality_id = request.query_params.get('municipality_id')
            ward_id = request.query_params.get('ward_id')
            
            # Get pagination parameters
            try:
                page_number = int(request.query_params.get('page_number', 1))
                page_size = int(request.query_params.get('page_size', 10))
                
                if page_number < 1:
                    return Response({
                        'status': 'error',
                        'message': 'page_number must be greater than 0'
                    }, status=status.HTTP_400_BAD_REQUEST)
                
                if page_size < 1 or page_size > 100:
                    return Response({
                        'status': 'error',
                        'message': 'page_size must be between 1 and 100'
                    }, status=status.HTTP_400_BAD_REQUEST)
                    
            except ValueError:
                return Response({
                    'status': 'error',
                    'message': 'Invalid page_number or page_size format'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # Get sorting parameters
            sort_by = request.query_params.get('sort_by', 'name')
            sort_order = request.query_params.get('sort_order', 'asc').lower()
            
            # Validate sort_by field
            allowed_sort_fields = ['name', 'code', 'created_at', 'updated_at']
            
            if sort_by not in allowed_sort_fields:
                return Response({
                    'status': 'error',
                    'message': f'Invalid sort_by field. Allowed fields: {", ".join(allowed_sort_fields)}'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # Validate sort_order
            if sort_order not in ['asc', 'desc']:
                return Response({
                    'status': 'error',
                    'message': 'sort_order must be either "asc" or "desc"'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # Validate filters against user authority
            if province_id:
                try:
                    province_id = int(province_id)
                    if province_id not in accessible_locations['provinces']:
                        logger.warning(f"User {user.email} attempted to filter by unauthorized province_id: {province_id}")
                        return Response({
                            'status': 'error',
                            'message': f'You are not authorized to access province_id: {province_id}'
                        }, status=status.HTTP_403_FORBIDDEN)
                except ValueError:
                    return Response({
                        'status': 'error',
                        'message': 'Invalid province_id format'
                    }, status=status.HTTP_400_BAD_REQUEST)
            
            if district_id:
                try:
                    district_id = int(district_id)
                    if district_id not in accessible_locations['districts']:
                        logger.warning(f"User {user.email} attempted to filter by unauthorized district_id: {district_id}")
                        return Response({
                            'status': 'error',
                            'message': f'You are not authorized to access district_id: {district_id}'
                        }, status=status.HTTP_403_FORBIDDEN)
                except ValueError:
                    return Response({
                        'status': 'error',
                        'message': 'Invalid district_id format'
                    }, status=status.HTTP_400_BAD_REQUEST)
            
            if municipality_id:
                try:
                    municipality_id = int(municipality_id)
                    if municipality_id not in accessible_locations['municipalities']:
                        logger.warning(f"User {user.email} attempted to filter by unauthorized municipality_id: {municipality_id}")
                        return Response({
                            'status': 'error',
                            'message': f'You are not authorized to access municipality_id: {municipality_id}'
                        }, status=status.HTTP_403_FORBIDDEN)
                except ValueError:
                    return Response({
                        'status': 'error',
                        'message': 'Invalid municipality_id format'
                    }, status=status.HTTP_400_BAD_REQUEST)
            
            if ward_id:
                try:
                    ward_id = int(ward_id)
                    if ward_id not in accessible_locations['wards']:
                        logger.warning(f"User {user.email} attempted to filter by unauthorized ward_id: {ward_id}")
                        return Response({
                            'status': 'error',
                            'message': f'You are not authorized to access ward_id: {ward_id}'
                        }, status=status.HTTP_403_FORBIDDEN)
                except ValueError:
                    return Response({
                        'status': 'error',
                        'message': 'Invalid ward_id format'
                    }, status=status.HTTP_400_BAD_REQUEST)
            
            # Determine what data to return based on filters
            if ward_id:
                # Return single ward details
                try:
                    ward = Ward.objects.get(id=ward_id, is_active=True)
                    serializer = WardSerializer(ward)
                    
                    logger.info(f"Successfully fetched ward details", {
                        'ward_id': ward_id,
                        'user': str(request.user),
                        'ip': get_client_ip(request)
                    })
                    
                    return Response({
                        'status': 'success',
                        'message': 'Ward details retrieved successfully',
                        'data': {
                            'type': 'ward',
                            'items': [serializer.data],
                            'total_count': 1,
                            'page_number': 1,
                            'page_size': 1,
                            'total_pages': 1
                        }
                    }, status=status.HTTP_200_OK)
                    
                except Ward.DoesNotExist:
                    return Response({
                        'status': 'error',
                        'message': f'Ward with id {ward_id} not found'
                    }, status=status.HTTP_404_NOT_FOUND)
                    
            elif municipality_id:
                # Return wards in the municipality
                wards = Ward.objects.filter(municipality_id=municipality_id, is_active=True)
                
                # Apply sorting
                if sort_order == 'desc':
                    sort_by = f'-{sort_by}'
                wards = wards.order_by(sort_by)
                
                total_count = wards.count()
                
                # Apply pagination
                start_index = (page_number - 1) * page_size
                end_index = start_index + page_size
                paginated_wards = wards[start_index:end_index]
                
                serializer = WardSerializer(paginated_wards, many=True)
                total_pages = (total_count + page_size - 1) // page_size
                
                logger.info(f"Successfully fetched wards for municipality", {
                    'municipality_id': municipality_id,
                    'total_count': total_count,
                    'page_number': page_number,
                    'page_size': page_size,
                    'sort_by': sort_by,
                    'sort_order': sort_order,
                    'user': str(request.user),
                    'ip': get_client_ip(request)
                })
                
                return Response({
                    'status': 'success',
                    'message': 'Wards retrieved successfully',
                    'data': {
                        'type': 'wards',
                        'items': serializer.data,
                        'total_count': total_count,
                        'page_number': page_number,
                        'page_size': page_size,
                        'total_pages': total_pages,
                        'sort_by': sort_by.replace('-', '') if sort_by.startswith('-') else sort_by,
                        'sort_order': sort_order
                    }
                }, status=status.HTTP_200_OK)
                
            elif district_id:
                # Return municipalities in the district
                municipalities = Municipality.objects.filter(district_id=district_id, is_active=True)
                
                # Apply sorting
                if sort_order == 'desc':
                    sort_by = f'-{sort_by}'
                municipalities = municipalities.order_by(sort_by)
                
                total_count = municipalities.count()
                
                # Apply pagination
                start_index = (page_number - 1) * page_size
                end_index = start_index + page_size
                paginated_municipalities = municipalities[start_index:end_index]
                
                serializer = MunicipalitySerializer(paginated_municipalities, many=True)
                total_pages = (total_count + page_size - 1) // page_size
                
                logger.info(f"Successfully fetched municipalities for district", {
                    'district_id': district_id,
                    'total_count': total_count,
                    'page_number': page_number,
                    'page_size': page_size,
                    'sort_by': sort_by,
                    'sort_order': sort_order,
                    'user': str(request.user),
                    'ip': get_client_ip(request)
                })
                
                return Response({
                    'status': 'success',
                    'message': 'Municipalities retrieved successfully',
                    'data': {
                        'type': 'municipalities',
                        'items': serializer.data,
                        'total_count': total_count,
                        'page_number': page_number,
                        'page_size': page_size,
                        'total_pages': total_pages,
                        'sort_by': sort_by.replace('-', '') if sort_by.startswith('-') else sort_by,
                        'sort_order': sort_order
                    }
                }, status=status.HTTP_200_OK)
                
            elif province_id:
                # Return districts in the province
                districts = District.objects.filter(province_id=province_id, is_active=True)
                
                # Apply sorting
                if sort_order == 'desc':
                    sort_by = f'-{sort_by}'
                districts = districts.order_by(sort_by)
                
                total_count = districts.count()
                
                # Apply pagination
                start_index = (page_number - 1) * page_size
                end_index = start_index + page_size
                paginated_districts = districts[start_index:end_index]
                
                serializer = DistrictSerializer(paginated_districts, many=True)
                total_pages = (total_count + page_size - 1) // page_size
                
                logger.info(f"Successfully fetched districts for province", {
                    'province_id': province_id,
                    'total_count': total_count,
                    'page_number': page_number,
                    'page_size': page_size,
                    'sort_by': sort_by,
                    'sort_order': sort_order,
                    'user': str(request.user),
                    'ip': get_client_ip(request)
                })
                
                return Response({
                    'status': 'success',
                    'message': 'Districts retrieved successfully',
                    'data': {
                        'type': 'districts',
                        'items': serializer.data,
                        'total_count': total_count,
                        'page_number': page_number,
                        'page_size': page_size,
                        'total_pages': total_pages,
                        'sort_by': sort_by.replace('-', '') if sort_by.startswith('-') else sort_by,
                        'sort_order': sort_order
                    }
                }, status=status.HTTP_200_OK)
                
            else:
                # No filters provided, return provinces based on user authority
                if accessible_locations['provinces']:
                    provinces = Province.objects.filter(id__in=accessible_locations['provinces'], is_active=True)
                else:
                    provinces = Province.objects.none()
                
                # Apply sorting
                if sort_order == 'desc':
                    sort_by = f'-{sort_by}'
                provinces = provinces.order_by(sort_by)
                
                total_count = provinces.count()
                
                # Apply pagination
                start_index = (page_number - 1) * page_size
                end_index = start_index + page_size
                paginated_provinces = provinces[start_index:end_index]
                
                serializer = ProvinceSerializer(paginated_provinces, many=True)
                total_pages = (total_count + page_size - 1) // page_size
                
                logger.info(f"Successfully fetched provinces for user authority", {
                    'total_count': total_count,
                    'page_number': page_number,
                    'page_size': page_size,
                    'sort_by': sort_by,
                    'sort_order': sort_order,
                    'user': str(request.user),
                    'ip': get_client_ip(request)
                })
                
                return Response({
                    'status': 'success',
                    'message': 'Provinces retrieved successfully',
                    'data': {
                        'type': 'provinces',
                        'items': serializer.data,
                        'total_count': total_count,
                        'page_number': page_number,
                        'page_size': page_size,
                        'total_pages': total_pages,
                        'sort_by': sort_by.replace('-', '') if sort_by.startswith('-') else sort_by,
                        'sort_order': sort_order
                    }
                }, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error(f"Error fetching filtered location data", {
                'user': str(request.user),
                'ip': get_client_ip(request),
                'error_type': type(e).__name__
            }, exception=e)
            return Response({
                'status': 'error',
                'message': 'Failed to fetch filtered location data',
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=False, methods=['get'], url_path='hierarchy')
    @user_specific_cache(timeout=3600)  # Cache for 1 hour
    def hierarchy(self, request):
        """
        Get hierarchical location data based on user authority level
        
        Returns:
        {
            "countries": [
                {
                    "id": 1,
                    "name": "Nepal",
                    "code": "NPL",
                    "provinces": [
                        {
                            "id": 1,
                            "name": "Koshi",
                            "code": "1",
                            "districts": [
                                {
                                    "id": 1,
                                    "name": "Taplejung",
                                    "code": "101",
                                    "municipalities": [
                                        {
                                            "id": 1,
                                            "name": "Phaktanlung",
                                            "code": "10101",
                                            "municipality_type": "Rural Municipality",
                                            "wards": [
                                                {
                                                    "id": 1,
                                                    "name": "Ward 1",
                                                    "code": "1010101",
                                                    "ward_number": 1
                                                }
                                            ]
                                        }
                                    ]
                                }
                            ]
                        }
                    ]
                }
            ]
        }
        """
        try:
            logger.info("Fetching hierarchical location data", {
                'user': str(request.user),
                'ip': get_client_ip(request),
                'endpoint': 'hierarchy'
            })

            user = request.user
            accessible_locations = user.get_accessible_location_ids()

            # Get all active countries with related data
            countries = Country.objects.filter(is_active=True).prefetch_related(
                'provinces__districts__municipalities__wards',
                'provinces__districts__municipalities__municipality_type'
            )

            hierarchy_data = []

            for country in countries:
                country_data = {
                    'id': country.id,
                    'name': country.name,
                    'code': country.code,
                    'provinces': []
                }

                # Get active provinces for this country, filtered by user access
                provinces = country.provinces.filter(is_active=True)
                if accessible_locations['provinces']:
                    provinces = provinces.filter(id__in=accessible_locations['provinces'])

                for province in provinces:
                    province_data = {
                        'id': province.id,
                        'name': province.name,
                        'code': province.code,
                        'districts': []
                    }

                    # Get active districts for this province, filtered by user access
                    districts = province.districts.filter(is_active=True)
                    if accessible_locations['districts']:
                        districts = districts.filter(id__in=accessible_locations['districts'])

                    for district in districts:
                        district_data = {
                            'id': district.id,
                            'name': district.name,
                            'code': district.code,
                            'municipalities': []
                        }

                        # Get active municipalities for this district, filtered by user access
                        municipalities = district.municipalities.filter(is_active=True)
                        if accessible_locations['municipalities']:
                            municipalities = municipalities.filter(id__in=accessible_locations['municipalities'])

                        for municipality in municipalities:
                            municipality_data = {
                                'id': municipality.id,
                                'name': municipality.name,
                                'code': municipality.code,
                                'municipality_type': municipality.municipality_type.name,
                                'wards': []
                            }

                            # Get active wards for this municipality, filtered by user access
                            wards = municipality.wards.filter(is_active=True).order_by('ward_number')
                            if accessible_locations['wards']:
                                wards = wards.filter(id__in=accessible_locations['wards'])

                            for ward in wards:
                                ward_data = {
                                    'id': ward.id,
                                    'name': ward.name,
                                    'code': ward.code,
                                    'ward_number': ward.ward_number
                                }
                                municipality_data['wards'].append(ward_data)

                            district_data['municipalities'].append(municipality_data)

                        province_data['districts'].append(district_data)

                    country_data['provinces'].append(province_data)

                hierarchy_data.append(country_data)

            logger.info(f"Successfully fetched hierarchical data for {len(hierarchy_data)} countries", {
                'countries_count': len(hierarchy_data),
                'user': str(request.user),
                'ip': get_client_ip(request)
            })

            return Response({
                'status': 'success',
                'message': 'Hierarchical location data retrieved successfully',
                'data': {
                    'countries': hierarchy_data
                }
            }, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error(f"Error fetching hierarchical location data", {
                'user': str(request.user),
                'ip': get_client_ip(request),
                'error_type': type(e).__name__
            }, exception=e)
            return Response({
                'status': 'error',
                'message': 'Failed to fetch hierarchical location data',
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=False, methods=['get'], url_path='flat')
    @user_specific_cache(timeout=3600)  # Cache for 1 hour
    def flat(self, request):
        """
        Get flat location data for simple filtering based on user authority level
        
        Returns:
        {
            "provinces": [...],
            "districts": [...],
            "municipalities": [...],
            "wards": [...]
        }
        """
        try:
            logger.info("Fetching flat location data", {
                'user': str(request.user),
                'ip': get_client_ip(request),
                'endpoint': 'flat'
            })

            user = request.user
            accessible_locations = user.get_accessible_location_ids()

            # Get all active data filtered by user access
            provinces = Province.objects.filter(is_active=True).select_related('country')
            districts = District.objects.filter(is_active=True).select_related('province')
            municipalities = Municipality.objects.filter(is_active=True).select_related('district')
            wards = Ward.objects.filter(is_active=True).select_related('municipality')

            # Apply user access filters
            if accessible_locations['provinces']:
                provinces = provinces.filter(id__in=accessible_locations['provinces'])
            else:
                provinces = provinces.none()

            if accessible_locations['districts']:
                districts = districts.filter(id__in=accessible_locations['districts'])
            else:
                districts = districts.none()

            if accessible_locations['municipalities']:
                municipalities = municipalities.filter(id__in=accessible_locations['municipalities'])
            else:
                municipalities = municipalities.none()

            if accessible_locations['wards']:
                wards = wards.filter(id__in=accessible_locations['wards'])
            else:
                wards = wards.none()

            # Serialize data
            province_serializer = ProvinceSerializer(provinces, many=True)
            district_serializer = DistrictSerializer(districts, many=True)
            municipality_serializer = MunicipalitySerializer(municipalities, many=True)
            ward_serializer = WardSerializer(wards, many=True)

            logger.info(
                f"Successfully fetched flat data: {len(provinces)} provinces, {len(districts)} districts, {len(municipalities)} municipalities, {len(wards)} wards", {
                    'provinces_count': len(provinces),
                    'districts_count': len(districts),
                    'municipalities_count': len(municipalities),
                    'wards_count': len(wards),
                    'user': str(request.user),
                    'ip': get_client_ip(request)
                })

            return Response({
                'status': 'success',
                'message': 'Flat location data retrieved successfully',
                'data': {
                    'provinces': province_serializer.data,
                    'districts': district_serializer.data,
                    'municipalities': municipality_serializer.data,
                    'wards': ward_serializer.data
                }
            }, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error(f"Error fetching flat location data", {
                'user': str(request.user),
                'ip': get_client_ip(request),
                'error_type': type(e).__name__
            }, exception=e)
            return Response({
                'status': 'error',
                'message': 'Failed to fetch flat location data',
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=False, methods=['get'], url_path='count')
    def count(self, request):
        """
        Get location counts with filtering based on user authority level
        
        Query Parameters:
        - province_id: Filter by specific province
        - district_id: Filter by specific district
        - municipality_id: Filter by specific municipality
        - ward_id: Filter by specific ward
        
        Returns:
        {
            "total_provinces": 7,
            "total_districts": 77,
            "total_municipalities": 753,
            "total_wards": 6747
        }
        """
        try:
            logger.info("Fetching location counts", {
                'user': str(request.user),
                'ip': get_client_ip(request),
                'endpoint': 'count',
                'query_params': dict(request.query_params)
            })

            user = request.user
            accessible_locations = user.get_accessible_location_ids()
            
            # Get filter parameters
            province_id = request.query_params.get('province_id')
            district_id = request.query_params.get('district_id')
            municipality_id = request.query_params.get('municipality_id')
            ward_id = request.query_params.get('ward_id')
            
            # Validate filters against user authority
            if province_id:
                try:
                    province_id = int(province_id)
                    if province_id not in accessible_locations['provinces']:
                        logger.warning(f"User {user.email} attempted to filter by unauthorized province_id: {province_id}")
                        return Response({
                            'status': 'error',
                            'message': f'You are not authorized to access province_id: {province_id}'
                        }, status=status.HTTP_403_FORBIDDEN)
                except ValueError:
                    return Response({
                        'status': 'error',
                        'message': 'Invalid province_id format'
                    }, status=status.HTTP_400_BAD_REQUEST)
            
            if district_id:
                try:
                    district_id = int(district_id)
                    if district_id not in accessible_locations['districts']:
                        logger.warning(f"User {user.email} attempted to filter by unauthorized district_id: {district_id}")
                        return Response({
                            'status': 'error',
                            'message': f'You are not authorized to access district_id: {district_id}'
                        }, status=status.HTTP_403_FORBIDDEN)
                except ValueError:
                    return Response({
                        'status': 'error',
                        'message': 'Invalid district_id format'
                    }, status=status.HTTP_400_BAD_REQUEST)
            
            if municipality_id:
                try:
                    municipality_id = int(municipality_id)
                    if municipality_id not in accessible_locations['municipalities']:
                        logger.warning(f"User {user.email} attempted to filter by unauthorized municipality_id: {municipality_id}")
                        return Response({
                            'status': 'error',
                            'message': f'You are not authorized to access municipality_id: {municipality_id}'
                        }, status=status.HTTP_403_FORBIDDEN)
                except ValueError:
                    return Response({
                        'status': 'error',
                        'message': 'Invalid municipality_id format'
                    }, status=status.HTTP_400_BAD_REQUEST)
            
            if ward_id:
                try:
                    ward_id = int(ward_id)
                    if ward_id not in accessible_locations['wards']:
                        logger.warning(f"User {user.email} attempted to filter by unauthorized ward_id: {ward_id}")
                        return Response({
                            'status': 'error',
                            'message': f'You are not authorized to access ward_id: {ward_id}'
                        }, status=status.HTTP_403_FORBIDDEN)
                except ValueError:
                    return Response({
                        'status': 'error',
                        'message': 'Invalid ward_id format'
                    }, status=status.HTTP_400_BAD_REQUEST)
            
            # Build filters based on parameters
            province_filter = {}
            district_filter = {}
            municipality_filter = {}
            ward_filter = {}
            
            if ward_id:
                # If ward_id is provided, get the specific ward's hierarchy
                ward = Ward.objects.get(id=ward_id, is_active=True)
                province_filter['id'] = ward.municipality.district.province_id
                district_filter['id'] = ward.municipality.district_id
                municipality_filter['id'] = ward.municipality_id
                ward_filter['id'] = ward_id
                
            elif municipality_id:
                # If municipality_id is provided, get the municipality's hierarchy
                municipality = Municipality.objects.get(id=municipality_id, is_active=True)
                province_filter['id'] = municipality.district.province_id
                district_filter['id'] = municipality.district_id
                municipality_filter['id'] = municipality_id
                ward_filter['municipality_id'] = municipality_id
                
            elif district_id:
                # If district_id is provided, get the district's hierarchy
                district = District.objects.get(id=district_id, is_active=True)
                province_filter['id'] = district.province_id
                district_filter['id'] = district_id
                municipality_filter['district_id'] = district_id
                ward_filter['municipality__district_id'] = district_id
                
            elif province_id:
                # If province_id is provided, get the province's hierarchy
                province_filter['id'] = province_id
                district_filter['province_id'] = province_id
                municipality_filter['district__province_id'] = province_id
                ward_filter['municipality__district__province_id'] = province_id
                
            else:
                # No filters provided, use user's accessible locations
                if accessible_locations['provinces']:
                    province_filter['id__in'] = accessible_locations['provinces']
                else:
                    province_filter['id__in'] = []
                    
                if accessible_locations['districts']:
                    district_filter['id__in'] = accessible_locations['districts']
                else:
                    district_filter['id__in'] = []
                    
                if accessible_locations['municipalities']:
                    municipality_filter['id__in'] = accessible_locations['municipalities']
                else:
                    municipality_filter['id__in'] = []
                    
                if accessible_locations['wards']:
                    ward_filter['id__in'] = accessible_locations['wards']
                else:
                    ward_filter['id__in'] = []
            
            # Get counts
            total_provinces = Province.objects.filter(is_active=True, **province_filter).count()
            total_districts = District.objects.filter(is_active=True, **district_filter).count()
            total_municipalities = Municipality.objects.filter(is_active=True, **municipality_filter).count()
            total_wards = Ward.objects.filter(is_active=True, **ward_filter).count()
            
            logger.info(f"Successfully fetched location counts", {
                'total_provinces': total_provinces,
                'total_districts': total_districts,
                'total_municipalities': total_municipalities,
                'total_wards': total_wards,
                'user': str(request.user),
                'ip': get_client_ip(request),
                'filters_applied': {
                    'province_id': province_id,
                    'district_id': district_id,
                    'municipality_id': municipality_id,
                    'ward_id': ward_id
                }
            })

            return Response({
                'status': 'success',
                'message': 'Location counts retrieved successfully',
                'data': {
                    'total_provinces': total_provinces,
                    'total_districts': total_districts,
                    'total_municipalities': total_municipalities,
                    'total_wards': total_wards
                }
            }, status=status.HTTP_200_OK)

        except Exception as e:
            logger.error(f"Error fetching location counts", {
                'user': str(request.user),
                'ip': get_client_ip(request),
                'error_type': type(e).__name__
            }, exception=e)
            return Response({
                'status': 'error',
                'message': 'Failed to fetch location counts',
                'error': str(e)
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)
