import csv
import pandas as pd
import logging
from pathlib import Path

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

# Read from the provided CSV file
csv_file = Path('files/palika_full.csv')

class MunicipalityDataGenerator:
    def __init__(self, csv_file=None):
        """Initialize the MunicipalityDataGenerator.
        
        Args:
            csv_file (str or Path, optional): Path to the CSV file. If None, will use the default data file.
        """
        self.csv_file = Path(csv_file) if csv_file else None
        self.municipality_data = []
        
        # If no CSV file provided, use the default data file
        if not self.csv_file:
            self.csv_file = Path(__file__).parent / 'municipality_data.py'
            logger.info(f"Using default municipality data file: {self.csv_file}")

    def get_municipality_data(self):
        """Get municipality data from the CSV file or default data file."""
        try:
            if self.csv_file.suffix == '.py':
                logger.info("Reading from Python data file")
                return self._read_from_py_file()
            else:
                logger.info("Reading from CSV file")
                return self._read_from_csv()
        except Exception as e:
            logger.error(f"Error reading municipality data: {str(e)}")
            return []

    def _read_from_py_file(self):
        """Read municipality data from the Python data file."""
        try:
            with open(self.csv_file, 'r') as f:
                content = f.read()
                # Extract tuples from the file content
                tuples = eval(f"[{content}]")
                logger.info(f"Successfully read {len(tuples)} municipalities from Python file")
                return tuples
        except Exception as e:
            logger.error(f"Error reading Python data file: {str(e)}")
            return []

    def _read_from_csv(self):
        """Read municipality data from the CSV file."""
        try:
            # Read the CSV file in chunks
            chunk_size = 1000  # Process 1000 rows at a time
            chunks = pd.read_csv(self.csv_file, chunksize=chunk_size)
            
            # Process each chunk
            for chunk in chunks:
                # Filter out rows with empty municipality names
                chunk = chunk[chunk['municipality_name'].notna()]
                
                # Convert to list of dictionaries
                data = chunk.to_dict('records')
                
                # Process each record
                for record in data:
                    # Clean and validate the data
                    record['municipality_name'] = str(record['municipality_name']).strip()
                    record['district_name'] = str(record['district_name']).strip()
                    record['province_name'] = str(record['province_name']).strip()
                    
                    # Skip if any required field is empty
                    if not all([record['municipality_name'], record['district_name'], record['province_name']]):
                        continue
                    
                    # Add to the list
                    self.municipality_data.append(record)
            
            logger.info(f"Successfully read {len(self.municipality_data)} municipalities from CSV")
            return self.municipality_data
        except Exception as e:
            logger.error(f"Error reading CSV file: {str(e)}")
            return []

if __name__ == "__main__":
    # Try reading from CSV first
    logger.info("Attempting to read from CSV file...")
    generator = MunicipalityDataGenerator(csv_file)
    municipalities = generator.get_municipality_data()
    
    if not municipalities:
        # If CSV reading fails, try reading from Python data file
        logger.info("CSV reading failed, falling back to Python data file...")
        generator = MunicipalityDataGenerator()
        municipalities = generator.get_municipality_data()
    
    logger.info(f"Total municipalities loaded: {len(municipalities)}")
    
    # Print first few municipalities as sample
    if municipalities:
        logger.info("Sample of loaded municipalities:")
        for i, muni in enumerate(municipalities[:5]):
            logger.info(f"{i+1}. {muni}") 