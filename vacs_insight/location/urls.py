from django.urls import path, include
from rest_framework.routers import DefaultRouter
from .views import (
    CountryViewSet, ProvinceViewSet, DistrictViewSet,
    MunicipalityTypeViewSet, MunicipalityViewSet, WardViewSet,
    LocationHierarchyViewSet
)

router = DefaultRouter()
router.register(r'country', CountryViewSet)
router.register(r'province', ProvinceViewSet)
router.register(r'district', DistrictViewSet)
router.register(r'municipality-type', MunicipalityTypeViewSet)
router.register(r'municipality', MunicipalityViewSet)
router.register(r'ward', WardViewSet)
router.register(r'', LocationHierarchyViewSet, basename='location')

urlpatterns = [
    path('', include(router.urls)),
] 