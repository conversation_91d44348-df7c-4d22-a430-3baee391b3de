from rest_framework import serializers
from .models import Country, Province, District, MunicipalityType, Municipality, Ward
from core.logging_utils import HealthScopeLogger

# Initialize logger for location serializers
logger = HealthScopeLogger('location', 'serializers')


class CountrySerializer(serializers.ModelSerializer):
    class Meta:
        model = Country
        fields = ['id', 'name', 'code', 'is_active', 'created_at', 'updated_at']
        read_only_fields = ['created_at', 'updated_at']

    def to_representation(self, instance):
        logger.debug(f"Serializing country: {instance.name}", {
            'country_id': instance.id,
            'country_name': instance.name
        })
        return super().to_representation(instance)

    def validate(self, data):
        logger.debug("Validating country data", {
            'data_keys': list(data.keys()),
            'is_update': self.instance is not None
        })
        return super().validate(data)


# Simplified list serializers for use in Facility API
class ProvinceListSerializer(serializers.ModelSerializer):
    """Simplified serializer for Province list view with only id, name, code"""
    class Meta:
        model = Province
        fields = ['id', 'name', 'code']


class DistrictListSerializer(serializers.ModelSerializer):
    """Simplified serializer for District list view with only id, name, code"""
    class Meta:
        model = District
        fields = ['id', 'name', 'code']


class MunicipalityListSerializer(serializers.ModelSerializer):
    """Simplified serializer for Municipality list view with only id, name, code"""
    class Meta:
        model = Municipality
        fields = ['id', 'name', 'code']


class WardListSerializer(serializers.ModelSerializer):
    """Simplified serializer for Ward list view with only id, name, code"""
    class Meta:
        model = Ward
        fields = ['id', 'name', 'code']


class ProvinceSerializer(serializers.ModelSerializer):
    country_name = serializers.CharField(source='country.name', read_only=True)

    class Meta:
        model = Province
        fields = ['id', 'country', 'country_name', 'name', 'code', 'is_active', 'created_at', 'updated_at']
        read_only_fields = ['created_at', 'updated_at']

    def to_representation(self, instance):
        logger.debug(f"Serializing province: {instance.name}", {
            'province_id': instance.id,
            'province_name': instance.name,
            'country_name': instance.country.name
        })
        return super().to_representation(instance)

    def validate(self, data):
        logger.debug("Validating province data", {
            'data_keys': list(data.keys()),
            'is_update': self.instance is not None
        })
        return super().validate(data)


class DistrictSerializer(serializers.ModelSerializer):
    province_name = serializers.CharField(source='province.name', read_only=True)
    country_name = serializers.CharField(source='province.country.name', read_only=True)

    class Meta:
        model = District
        fields = ['id', 'province', 'province_name', 'country_name', 'name', 'code', 'is_active', 'created_at',
                  'updated_at']
        read_only_fields = ['created_at', 'updated_at']

    def to_representation(self, instance):
        logger.debug(f"Serializing district: {instance.name}", {
            'district_id': instance.id,
            'district_name': instance.name,
            'province_name': instance.province.name,
            'country_name': instance.province.country.name
        })
        return super().to_representation(instance)

    def validate(self, data):
        logger.debug("Validating district data", {
            'data_keys': list(data.keys()),
            'is_update': self.instance is not None
        })
        return super().validate(data)


class MunicipalityTypeSerializer(serializers.ModelSerializer):
    class Meta:
        model = MunicipalityType
        fields = ['id', 'name', 'code', 'is_active', 'created_at', 'updated_at']
        read_only_fields = ['created_at', 'updated_at']

    def to_representation(self, instance):
        logger.debug(f"Serializing municipality type: {instance.name}", {
            'municipality_type_id': instance.id,
            'municipality_type_name': instance.name
        })
        return super().to_representation(instance)

    def validate(self, data):
        logger.debug("Validating municipality type data", {
            'data_keys': list(data.keys()),
            'is_update': self.instance is not None
        })
        return super().validate(data)


class MunicipalitySerializer(serializers.ModelSerializer):
    district_name = serializers.CharField(source='district.name', read_only=True)
    province_name = serializers.CharField(source='district.province.name', read_only=True)
    municipality_type_name = serializers.CharField(source='municipality_type.name', read_only=True)

    class Meta:
        model = Municipality
        fields = [
            'id', 'district', 'district_name', 'province_name',
            'municipality_type', 'municipality_type_name',
            'name', 'code', 'is_active', 'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']

    def to_representation(self, instance):
        logger.debug(f"Serializing municipality: {instance.name}", {
            'municipality_id': instance.id,
            'municipality_name': instance.name,
            'district_name': instance.district.name,
            'municipality_type_name': instance.municipality_type.name
        })
        return super().to_representation(instance)

    def validate(self, data):
        logger.debug("Validating municipality data", {
            'data_keys': list(data.keys()),
            'is_update': self.instance is not None
        })
        return super().validate(data)


class WardSerializer(serializers.ModelSerializer):
    municipality_name = serializers.CharField(source='municipality.name', read_only=True)
    district_name = serializers.CharField(source='municipality.district.name', read_only=True)
    province_name = serializers.CharField(source='municipality.district.province.name', read_only=True)

    class Meta:
        model = Ward
        fields = [
            'id', 'municipality', 'municipality_name',
            'district_name', 'province_name',
            'name', 'code', 'ward_number', 'is_active',
            'created_at', 'updated_at'
        ]
        read_only_fields = ['created_at', 'updated_at']

    def to_representation(self, instance):
        logger.debug(f"Serializing ward: {instance.name}", {
            'ward_id': instance.id,
            'ward_name': instance.name,
            'ward_number': instance.ward_number,
            'municipality_name': instance.municipality.name
        })
        return super().to_representation(instance)

    def validate(self, data):
        logger.debug("Validating ward data", {
            'data_keys': list(data.keys()),
            'is_update': self.instance is not None
        })
        return super().validate(data)
