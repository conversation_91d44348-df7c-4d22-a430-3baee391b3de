from django.db import models
from django.core.exceptions import ValidationError
from core.models import AuditModel
from core.logging_utils import HealthScopeLogger
from django.core.cache import cache
from django.db.models.signals import post_save, post_delete
from django.dispatch import receiver

# Initialize logger for location models
logger = HealthScopeLogger('location', 'models')


class Country(AuditModel):
    """Country model representing countries in the system"""
    name = models.CharField(max_length=100, unique=True, help_text="Name of the country")
    code = models.CharField(max_length=3, unique=True, help_text="ISO 3166-1 alpha-3 country code")
    is_active = models.BooleanField(default=True)

    class Meta:
        verbose_name = "Country"
        verbose_name_plural = "Countries"
        ordering = ['name']
        db_table = 'location_country'

    def __str__(self):
        return self.name

    def save(self, *args, **kwargs):
        logger.info(f"Saving country: {self.name} (code: {self.code})", {
            'country_id': self.id,
            'is_new': self.pk is None
        })
        super().save(*args, **kwargs)

    def clean(self):
        if not self.name:
            logger.error("Country validation failed: name is required")
            raise ValidationError("Country name is required")
        if not self.code:
            logger.error("Country validation failed: code is required")
            raise ValidationError("Country code is required")
        if len(self.code) != 3:
            logger.error(f"Country validation failed: code must be 3 characters, got {len(self.code)}")
            raise ValidationError("Country code must be 3 characters long")


class Province(AuditModel):
    """Province model representing provinces/states in a country"""
    country = models.ForeignKey(Country, on_delete=models.PROTECT, related_name='provinces')
    name = models.CharField(max_length=100, help_text="Name of the province")
    code = models.CharField(max_length=10, unique=True, help_text="Province code (e.g., 1 for Koshi)")
    is_active = models.BooleanField(default=True)

    class Meta:
        verbose_name = "Province"
        verbose_name_plural = "Provinces"
        ordering = ['code']
        unique_together = ['country', 'name']
        db_table = 'location_province'

    def __str__(self):
        return f"{self.name} ({self.country.name})"

    def save(self, *args, **kwargs):
        logger.info(f"Saving province: {self.name} (code: {self.code}) in country: {self.country.name}", {
            'province_id': self.id,
            'country_id': self.country.id,
            'is_new': self.pk is None
        })
        super().save(*args, **kwargs)

    def clean(self):
        if not self.name:
            logger.error("Province validation failed: name is required")
            raise ValidationError("Province name is required")
        if not self.code:
            logger.error("Province validation failed: code is required")
            raise ValidationError("Province code is required")
        if not self.country:
            logger.error("Province validation failed: country is required")
            raise ValidationError("Country is required")


class District(AuditModel):
    """District model representing districts in a province"""
    province = models.ForeignKey(Province, on_delete=models.PROTECT, related_name='districts')
    name = models.CharField(max_length=100, help_text="Name of the district")
    code = models.CharField(max_length=10, unique=True, help_text="District code (e.g., 101 for Taplejung)")
    is_active = models.BooleanField(default=True)

    class Meta:
        verbose_name = "District"
        verbose_name_plural = "Districts"
        ordering = ['code']
        unique_together = ['province', 'name']
        db_table = 'location_district'

    def __str__(self):
        return f"{self.name} ({self.province.name})"

    def save(self, *args, **kwargs):
        logger.info(f"Saving district: {self.name} (code: {self.code}) in province: {self.province.name}", {
            'district_id': self.id,
            'province_id': self.province.id,
            'is_new': self.pk is None
        })
        super().save(*args, **kwargs)

    def clean(self):
        if not self.name:
            logger.error("District validation failed: name is required")
            raise ValidationError("District name is required")
        if not self.code:
            logger.error("District validation failed: code is required")
            raise ValidationError("District code is required")
        if not self.province:
            logger.error("District validation failed: province is required")
            raise ValidationError("Province is required")


class MunicipalityType(AuditModel):
    """Municipality type model (e.g., Metropolitan City, Sub-Metropolitan City, Municipality, Rural Municipality)"""
    name = models.CharField(max_length=100, unique=True, help_text="Name of the municipality type")
    code = models.CharField(max_length=10, unique=True, help_text="Municipality type code")
    is_active = models.BooleanField(default=True)

    class Meta:
        verbose_name = "Municipality Type"
        verbose_name_plural = "Municipality Types"
        ordering = ['name']
        db_table = 'location_municipality_type'

    def __str__(self):
        return self.name

    def save(self, *args, **kwargs):
        logger.info(f"Saving municipality type: {self.name} (code: {self.code})", {
            'municipality_type_id': self.id,
            'is_new': self.pk is None
        })
        super().save(*args, **kwargs)

    def clean(self):
        if not self.name:
            logger.error("Municipality type validation failed: name is required")
            raise ValidationError("Municipality type name is required")
        if not self.code:
            logger.error("Municipality type validation failed: code is required")
            raise ValidationError("Municipality type code is required")


class Municipality(AuditModel):
    """Municipality/Palika model representing local government units"""
    district = models.ForeignKey(District, on_delete=models.PROTECT, related_name='municipalities')
    municipality_type = models.ForeignKey(MunicipalityType, on_delete=models.PROTECT, related_name='municipalities')
    name = models.CharField(max_length=100, help_text="Name of the municipality")
    code = models.CharField(max_length=10, unique=True, help_text="Municipality code (e.g., 10101 for Phaktanlung)")
    is_active = models.BooleanField(default=True)

    class Meta:
        verbose_name = "Municipality"
        verbose_name_plural = "Municipalities"
        ordering = ['code']
        unique_together = ['district', 'name']
        db_table = 'location_municipality'

    def __str__(self):
        return f"{self.name} ({self.municipality_type.name})"

    def save(self, *args, **kwargs):
        logger.info(f"Saving municipality: {self.name} (code: {self.code}) in district: {self.district.name}", {
            'municipality_id': self.id,
            'district_id': self.district.id,
            'municipality_type_id': self.municipality_type.id,
            'is_new': self.pk is None
        })
        super().save(*args, **kwargs)

    def clean(self):
        if not self.name:
            logger.error("Municipality validation failed: name is required")
            raise ValidationError("Municipality name is required")
        if not self.code:
            logger.error("Municipality validation failed: code is required")
            raise ValidationError("Municipality code is required")
        if not self.district:
            logger.error("Municipality validation failed: district is required")
            raise ValidationError("District is required")
        if not self.municipality_type:
            logger.error("Municipality validation failed: municipality type is required")
            raise ValidationError("Municipality type is required")


class Ward(AuditModel):
    """Ward model representing wards in a municipality"""
    municipality = models.ForeignKey(Municipality, on_delete=models.PROTECT, related_name='wards')
    name = models.CharField(max_length=100, help_text="Name of the ward")
    code = models.CharField(max_length=10, unique=True, help_text="Ward code (e.g., 1010101 for Ward 1)")
    ward_number = models.PositiveIntegerField(help_text="Ward number (1-32)")
    is_active = models.BooleanField(default=True)

    class Meta:
        verbose_name = "Ward"
        verbose_name_plural = "Wards"
        ordering = ['code']
        unique_together = ['municipality', 'ward_number']
        db_table = 'location_ward'

    def __str__(self):
        return f"Ward {self.ward_number} - {self.municipality.name}"

    def save(self, *args, **kwargs):
        logger.info(f"Saving ward: {self.ward_number} (code: {self.code}) in municipality: {self.municipality.name}", {
            'ward_id': self.id,
            'municipality_id': self.municipality.id,
            'ward_number': self.ward_number,
            'is_new': self.pk is None
        })
        super().save(*args, **kwargs)

    def clean(self):
        if not self.name:
            logger.error("Ward validation failed: name is required")
            raise ValidationError("Ward name is required")
        if not self.code:
            logger.error("Ward validation failed: code is required")
            raise ValidationError("Ward code is required")
        if not self.municipality:
            logger.error("Ward validation failed: municipality is required")
            raise ValidationError("Municipality is required")
        if not self.ward_number:
            logger.error("Ward validation failed: ward number is required")
            raise ValidationError("Ward number is required")
        if self.ward_number < 1 or self.ward_number > 32:
            logger.error(f"Ward validation failed: ward number must be between 1 and 32, got {self.ward_number}")
            raise ValidationError("Ward number must be between 1 and 32")


@receiver([post_save, post_delete], sender=Province)
@receiver([post_save, post_delete], sender=District)
@receiver([post_save, post_delete], sender=Municipality)
@receiver([post_save, post_delete], sender=Ward)
def clear_location_cache(sender, instance, **kwargs):
    """
    Clear location cache when any location data is updated
    """
    # For now, we'll use a cache version approach
    # Increment a cache version number to invalidate all cached data
    cache_version = cache.get('location_cache_version', 0)
    cache.set('location_cache_version', cache_version + 1, timeout=None)
    
    logger.info(f"Location cache invalidated due to {sender.__name__} change", {
        'model': sender.__name__,
        'instance_id': getattr(instance, 'id', None),
        'new_cache_version': cache_version + 1
    })
