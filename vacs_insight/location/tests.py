"""
Tests for Location API
Tests the hierarchical location endpoints for frontend filtering
"""

import json
import requests
from django.test import TestCase
from django.contrib.auth import get_user_model
from rest_framework.test import APITestCase
from rest_framework import status
from rest_framework_simplejwt.tokens import RefreshToken
from .models import Country, Province, District, MunicipalityType, Municipality, Ward

User = get_user_model()


class LocationHierarchyAPITestCase(APITestCase):
    """Test case for Location Hierarchy API endpoints"""
    
    def setUp(self):
        """Set up test data"""
        # Create admin user
        self.admin_user = User.objects.create_user(
            username='admin',
            email='<EMAIL>',
            password='Admin@123!#',
            first_name='Admin',
            last_name='User',
            status='active',
            authority_level='center',
            is_active=True
        )
        
        # Get JWT token
        refresh = RefreshToken.for_user(self.admin_user)
        self.access_token = str(refresh.access_token)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.access_token}')
        
        # Create test location data
        self.create_test_location_data()
    
    def create_test_location_data(self):
        """Create test location data"""
        # Create country
        self.country = Country.objects.create(
            name="Nepal",
            code="NPL"
        )
        
        # Create province
        self.province = Province.objects.create(
            name="Koshi",
            code="1",
            country=self.country
        )
        
        # Create district
        self.district = District.objects.create(
            name="Taplejung",
            code="101",
            province=self.province
        )
        
        # Create municipality type
        self.municipality_type = MunicipalityType.objects.create(
            name="Rural Municipality",
            code="RM"
        )
        
        # Create municipality
        self.municipality = Municipality.objects.create(
            name="Phaktanlung",
            code="10101",
            district=self.district,
            municipality_type=self.municipality_type
        )
        
        # Create ward
        self.ward = Ward.objects.create(
            name="Ward 1",
            code="1010101",
            municipality=self.municipality,
            ward_number=1
        )
    
    def test_hierarchy_endpoint_success(self):
        """Test successful hierarchical location data retrieval"""
        url = '/api/v1/location/hierarchy'
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()
        
        # Check response structure
        self.assertEqual(data['status'], 'success')
        self.assertIn('data', data)
        self.assertIn('countries', data['data'])
        
        # Check data content
        countries = data['data']['countries']
        self.assertEqual(len(countries), 1)
        
        country = countries[0]
        self.assertEqual(country['name'], 'Nepal')
        self.assertEqual(country['code'], 'NPL')
        self.assertEqual(len(country['provinces']), 1)
        
        province = country['provinces'][0]
        self.assertEqual(province['name'], 'Koshi')
        self.assertEqual(province['code'], '1')
        self.assertEqual(len(province['districts']), 1)
        
        district = province['districts'][0]
        self.assertEqual(district['name'], 'Taplejung')
        self.assertEqual(district['code'], '101')
        self.assertEqual(len(district['municipalities']), 1)
        
        municipality = district['municipalities'][0]
        self.assertEqual(municipality['name'], 'Phaktanlung')
        self.assertEqual(municipality['code'], '10101')
        self.assertEqual(municipality['municipality_type'], 'Rural Municipality')
        self.assertEqual(len(municipality['wards']), 1)
        
        ward = municipality['wards'][0]
        self.assertEqual(ward['name'], 'Ward 1')
        self.assertEqual(ward['code'], '1010101')
        self.assertEqual(ward['ward_number'], 1)
    
    def test_hierarchy_endpoint_unauthorized(self):
        """Test hierarchical endpoint without authentication"""
        self.client.credentials()  # Remove authentication
        url = '/api/v1/location/hierarchy'
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
    
    def test_flat_endpoint_success(self):
        """Test successful flat location data retrieval"""
        url = '/api/v1/location/flat'
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()
        
        # Check response structure
        self.assertEqual(data['status'], 'success')
        self.assertIn('data', data)
        self.assertIn('provinces', data['data'])
        self.assertIn('districts', data['data'])
        self.assertIn('municipalities', data['data'])
        self.assertIn('wards', data['data'])
        
        # Check data content
        provinces = data['data']['provinces']
        districts = data['data']['districts']
        municipalities = data['data']['municipalities']
        wards = data['data']['wards']
        
        self.assertEqual(len(provinces), 1)
        self.assertEqual(len(districts), 1)
        self.assertEqual(len(municipalities), 1)
        self.assertEqual(len(wards), 1)
        
        self.assertEqual(provinces[0]['name'], 'Koshi')
        self.assertEqual(districts[0]['name'], 'Taplejung')
        self.assertEqual(municipalities[0]['name'], 'Phaktanlung')
        self.assertEqual(wards[0]['name'], 'Ward 1')
    
    def test_flat_endpoint_unauthorized(self):
        """Test flat endpoint without authentication"""
        self.client.credentials()  # Remove authentication
        url = '/api/v1/location/flat'
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_401_UNAUTHORIZED)
    
    def test_hierarchy_with_inactive_data(self):
        """Test that inactive location data is not included"""
        # Create inactive province
        inactive_province = Province.objects.create(
            name="Inactive Province",
            code="99",
            country=self.country,
            is_active=False
        )
        
        url = '/api/v1/location/hierarchy'
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()
        
        countries = data['data']['countries']
        country = countries[0]
        
        # Should only have the active province
        self.assertEqual(len(country['provinces']), 1)
        self.assertEqual(country['provinces'][0]['name'], 'Koshi')
    
    def test_hierarchy_empty_data(self):
        """Test hierarchical endpoint with no location data"""
        # Delete all location data
        Ward.objects.all().delete()
        Municipality.objects.all().delete()
        District.objects.all().delete()
        Province.objects.all().delete()
        Country.objects.all().delete()
        
        url = '/api/v1/location/hierarchy'
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()
        
        self.assertEqual(data['status'], 'success')
        self.assertEqual(len(data['data']['countries']), 0)


class LocationIndividualEndpointsTestCase(APITestCase):
    """Test case for individual location endpoints"""
    
    def setUp(self):
        """Set up test data"""
        # Create admin user
        self.admin_user = User.objects.create_user(
            username='admin',
            email='<EMAIL>',
            password='Admin@123!#',
            first_name='Admin',
            last_name='User',
            status='active',
            authority_level='center',
            is_active=True
        )
        
        # Get JWT token
        refresh = RefreshToken.for_user(self.admin_user)
        self.access_token = str(refresh.access_token)
        self.client.credentials(HTTP_AUTHORIZATION=f'Bearer {self.access_token}')
        
        # Create test location data
        self.create_test_location_data()
    
    def create_test_location_data(self):
        """Create test location data"""
        self.country = Country.objects.create(name="Nepal", code="NPL")
        self.province = Province.objects.create(name="Koshi", code="1", country=self.country)
        self.district = District.objects.create(name="Taplejung", code="101", province=self.province)
        self.municipality_type = MunicipalityType.objects.create(name="Rural Municipality", code="RM")
        self.municipality = Municipality.objects.create(
            name="Phaktanlung", 
            code="10101", 
            district=self.district, 
            municipality_type=self.municipality_type
        )
        self.ward = Ward.objects.create(
            name="Ward 1", 
            code="1010101", 
            municipality=self.municipality, 
            ward_number=1
        )
    
    def test_province_list(self):
        """Test province list endpoint"""
        url = '/api/v1/location/province'
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()
        
        self.assertIn('results', data)
        self.assertEqual(len(data['results']), 1)
        self.assertEqual(data['results'][0]['name'], 'Koshi')
    
    def test_district_list(self):
        """Test district list endpoint"""
        url = '/api/v1/location/district'
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()
        
        self.assertIn('results', data)
        self.assertEqual(len(data['results']), 1)
        self.assertEqual(data['results'][0]['name'], 'Taplejung')
    
    def test_municipality_list(self):
        """Test municipality list endpoint"""
        url = '/api/v1/location/municipality'
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()
        
        self.assertIn('results', data)
        self.assertEqual(len(data['results']), 1)
        self.assertEqual(data['results'][0]['name'], 'Phaktanlung')
    
    def test_ward_list(self):
        """Test ward list endpoint"""
        url = '/api/v1/location/ward'
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()
        
        self.assertIn('results', data)
        self.assertEqual(len(data['results']), 1)
        self.assertEqual(data['results'][0]['name'], 'Ward 1')
    
    def test_province_filter_by_country(self):
        """Test province filtering by country"""
        url = f'/api/v1/location/province?country={self.country.id}'
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()
        
        self.assertEqual(len(data['results']), 1)
        self.assertEqual(data['results'][0]['name'], 'Koshi')
    
    def test_district_filter_by_province(self):
        """Test district filtering by province"""
        url = f'/api/v1/location/district?province={self.province.id}'
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()
        
        self.assertEqual(len(data['results']), 1)
        self.assertEqual(data['results'][0]['name'], 'Taplejung')
    
    def test_municipality_filter_by_district(self):
        """Test municipality filtering by district"""
        url = f'/api/v1/location/municipality?district={self.district.id}'
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()
        
        self.assertEqual(len(data['results']), 1)
        self.assertEqual(data['results'][0]['name'], 'Phaktanlung')
    
    def test_ward_filter_by_municipality(self):
        """Test ward filtering by municipality"""
        url = f'/api/v1/location/ward?municipality={self.municipality.id}'
        response = self.client.get(url)
        
        self.assertEqual(response.status_code, status.HTTP_200_OK)
        data = response.json()
        
        self.assertEqual(len(data['results']), 1)
        self.assertEqual(data['results'][0]['name'], 'Ward 1') 