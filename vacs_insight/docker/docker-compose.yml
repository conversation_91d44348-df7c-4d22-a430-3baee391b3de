version: '3.8'

services:
  # Django Application
  backend:
    container_name: healthscope-app
    build:
      context: ..
      dockerfile: docker/Dockerfile
    env_file:
      - ../.env
    ports:
      - "8000:8000"
    depends_on:
      - database
      - redis
    volumes:
      - static_volume:/app/static
      - media_volume:/app/media
    restart: unless-stopped
    networks:
      - healthscope_network

  # PostgreSQL Database
  database:
    image: postgres:15-alpine
    env_file:
      - ../.env
    environment:
      - POSTGRES_DB=${DB_NAME}
      - POSTGRES_USER=${DB_USER}
      - POSTGRES_PASSWORD=${DB_PASSWORD}
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./init.sql:/docker-entrypoint-initdb.d/init.sql
    ports:
      - "5432:5432"
    restart: unless-stopped
    networks:
      - healthscope_network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${DB_USER} -d ${DB_NAME}"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis Cache
  redis:
    image: redis:7-alpine
    env_file:
      - ../.env
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    restart: unless-stopped
    networks:
      - healthscope_network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Nginx (Optional - for production)
  gateway:
    image: nginx:alpine
    env_file:
      - ../.env
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf
      - ./nginx/ssl:/etc/nginx/ssl
      - static_volume:/app/static
      - media_volume:/app/media
    depends_on:
      - backend
    restart: unless-stopped
    networks:
      - healthscope_network
    profiles:
      - production

volumes:
  postgres_data:
  redis_data:
  static_volume:
  media_volume:

networks:
  healthscope_network:
    driver: bridge 