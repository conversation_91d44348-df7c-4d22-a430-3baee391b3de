#!/usr/bin/env python3
"""
Test script for token invalidation after password change
Run this script when your Django server is running
"""
import requests
import json
import time

# Configuration
BASE_URL = "http://localhost:8000"
LOGIN_URL = f"{BASE_URL}/api/v1/user/auth/login"
CHANGE_PASSWORD_URL = f"{BASE_URL}/api/v1/user/change_password"
PROFILE_URL = f"{BASE_URL}/api/v1/user/profile"
REFRESH_URL = f"{BASE_URL}/api/v1/token/refresh"

def print_response(response, title):
    """Print response details"""
    print(f"\n{'='*50}")
    print(f"{title}")
    print(f"{'='*50}")
    print(f"Status Code: {response.status_code}")
    print(f"Headers: {dict(response.headers)}")
    try:
        print(f"Response: {json.dumps(response.json(), indent=2)}")
    except:
        print(f"Response: {response.text}")

def test_token_invalidation():
    """Test token invalidation after password change"""
    print("🔐 Testing Token Invalidation After Password Change")
    print("=" * 60)
    
    # Step 1: Login to get tokens
    print("\n1️⃣ Logging in to get tokens...")
    login_data = {
        "username": "<EMAIL>",  # Replace with your test user
        "password": "Admin@123!#"  # Replace with your test password
    }
    
    login_response = requests.post(LOGIN_URL, json=login_data)
    print_response(login_response, "Login Response")
    
    if login_response.status_code != 200:
        print("❌ Failed to login. Please check your credentials and server.")
        return
    
    # Extract tokens
    response_data = login_response.json()
    access_token = response_data.get("accessToken")
    refresh_token = response_data.get("refreshToken")
    
    if not access_token or not refresh_token:
        print("❌ No tokens received from login")
        return
    
    print(f"✅ Got tokens:")
    print(f"   Access Token: {access_token[:50]}...")
    print(f"   Refresh Token: {refresh_token[:50]}...")
    
    # Step 2: Test that we can access protected endpoint
    print("\n2️⃣ Testing access to protected endpoint...")
    headers = {"Authorization": f"Bearer {access_token}"}
    profile_response = requests.get(PROFILE_URL, headers=headers)
    print_response(profile_response, "Profile Access (Before Password Change)")
    
    if profile_response.status_code != 200:
        print("❌ Failed to access profile before password change")
        return
    
    print("✅ Successfully accessed profile with token")
    
    # Step 3: Change password
    print("\n3️⃣ Changing password...")
    password_change_data = {
        "old_password": "Admin@123!#",  # Replace with your test password
        "new_password": "NewSecurePassword123!",
        "confirm_password": "NewSecurePassword123!"
    }
    
    password_change_response = requests.post(
        CHANGE_PASSWORD_URL, 
        json=password_change_data, 
        headers=headers
    )
    print_response(password_change_response, "Password Change Response")
    
    if password_change_response.status_code != 200:
        print("❌ Failed to change password")
        return
    
    print("✅ Password changed successfully")
    
    # Step 3.5: Debug - Check blacklist status
    print("\n3.5️⃣ Debug: Checking blacklist status...")
    debug_data = {"token": access_token}
    debug_response = requests.post(f"{BASE_URL}/api/v1/user/debug_blacklist", json=debug_data)
    print_response(debug_response, "Debug Blacklist Status")
    
    if debug_response.status_code == 200:
        debug_info = debug_response.json()
        print(f"   JTI: {debug_info.get('jti', 'N/A')}")
        print(f"   Is Blacklisted: {debug_info.get('is_blacklisted', 'N/A')}")
        print(f"   Outstanding Token Exists: {debug_info.get('token_info', {}).get('outstanding_token_exists', 'N/A')}")
        print(f"   Total Blacklisted Tokens: {debug_info.get('blacklist_info', {}).get('total_blacklisted_tokens', 'N/A')}")
    
    # Step 4: Try to access profile with old token (should fail)
    print("\n4️⃣ Testing access with old token (should fail)...")
    profile_response_after = requests.get(PROFILE_URL, headers=headers)
    print_response(profile_response_after, "Profile Access (After Password Change)")
    
    if profile_response_after.status_code == 401:
        print("✅ Token successfully invalidated - access denied")
        # Check if it's specifically a blacklist error
        try:
            response_data = profile_response_after.json()
            if response_data.get('code') == 'token_blacklisted':
                print("✅ Confirmed: Token was rejected due to blacklisting")
            else:
                print("ℹ️  Token rejected, but not specifically due to blacklisting")
        except:
            print("ℹ️  Token rejected with 401 status")
    else:
        print("❌ Token was NOT invalidated - access still allowed")
        print("   This indicates the blacklisting is not working properly")
    
    # Step 5: Try to refresh the token (should fail)
    print("\n5️⃣ Testing token refresh (should fail)...")
    refresh_data = {"refresh": refresh_token}
    refresh_response = requests.post(REFRESH_URL, json=refresh_data)
    print_response(refresh_response, "Token Refresh Response")
    
    if refresh_response.status_code == 401:
        print("✅ Refresh token successfully invalidated")
    else:
        print("❌ Refresh token was NOT invalidated")
    
    # Step 6: Login with new password
    print("\n6️⃣ Logging in with new password...")
    new_login_data = {
        "username": "<EMAIL>",  # Replace with your test user
        "password": "NewSecurePassword123!"
    }
    
    new_login_response = requests.post(LOGIN_URL, json=new_login_data)
    print_response(new_login_response, "Login with New Password")
    
    if new_login_response.status_code == 200:
        print("✅ Successfully logged in with new password")
        
        # Step 7: Change password back to original
        print("\n7️⃣ Changing password back to original...")
        new_response_data = new_login_response.json()
        new_access_token = new_response_data.get("accessToken")
        new_headers = {"Authorization": f"Bearer {new_access_token}"}
        
        revert_password_data = {
            "old_password": "NewSecurePassword123!",
            "new_password": "Admin@123!#",  # Replace with your original password
            "confirm_password": "Admin@123!#"
        }
        
        revert_response = requests.post(
            CHANGE_PASSWORD_URL, 
            json=revert_password_data, 
            headers=new_headers
        )
        print_response(revert_response, "Revert Password Response")
        
        if revert_response.status_code == 200:
            print("✅ Successfully reverted password to original")
        else:
            print("❌ Failed to revert password to original")
    else:
        print("❌ Failed to login with new password")
    
    print("\n" + "=" * 60)
    print("🏁 Test completed!")
    print("=" * 60)

if __name__ == "__main__":
    print("🚀 Starting Token Invalidation Test")
    print("Make sure your Django server is running on http://localhost:8000")
    print("Update the credentials in the script if needed")
    
    try:
        test_token_invalidation()
    except requests.exceptions.ConnectionError:
        print("❌ Could not connect to server. Make sure it's running on http://localhost:8000")
    except Exception as e:
        print(f"❌ Error during test: {e}") 