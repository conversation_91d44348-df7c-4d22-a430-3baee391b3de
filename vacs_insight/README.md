# 🩺 VacsInsight – Digital Health Analytics Platform for Nepal

**VacsInsight** is a standalone digital health analytics platform designed to support routine immunization and other public health programs in Nepal. It integrates data from various health systems and registries to provide actionable insights, geospatial analysis, and hierarchical role-based access for health administrators across all levels.

---

## 🧩 Key Features

* Ingests data from:

  * DHIS2 (Program Data)
  * Health Facility Registry (HFR)
  * Health Worker Registry (HWR)
  * Community Health Toolkit (CHT)
  * Vaccine stock reports
* File-based (`.csv`, `.xlsx`) and API-based ingestion
* Hierarchical user access: Central → Province → District → Palika → Ward
* GIS-based mapping and dashboards
* Designed for extensibility to other public health programs (e.g., nutrition, maternal health)

---

## 🏗️ System Architecture

### 📐 Non-Technical Overview

```
+------------+        +-----------------+        +------------------+
|   Users    | <----> |   React Frontend| <----> | Django Backend   |
| (MOHP, DPHO|        +-----------------+        +------------------+
|  CHO, etc) |                                        |
+------------+                                        |
       |                                              |
       v                                              v
+--------------+                               +-----------------+
| GIS Map View |                               | PostgreSQL +    |
| & Dashboards |                               | PostGIS DB      |
+--------------+                               +-----------------+
                                           +--------+  +--------+
                                           | Celery |  | Redis  |
                                           +--------+  +--------+
```

### ⚙️ Technical Stack

| Component        | Technology                     |
| ---------------- | ------------------------------ |
| Frontend         | React.js                       |
| Backend API      | Django + Django REST Framework |
| Background Tasks | Celery + Celery Beat           |
| Queue Broker     | Redis                          |
| Database         | PostgreSQL + PostGIS           |
| GIS Support      | GeoDjango, Leaflet/OpenLayers  |
| File Processing  | Pandas, Python ETL             |
| Authentication   | JWT (SimpleJWT or similar)     |
| Deployment Ready | Docker (optional)              |

---

## 🧱 Database Setup

Ensure you have PostgreSQL installed with the PostGIS extension enabled:

```sql
CREATE EXTENSION postgis;
```

> If using Django, this can also be handled via `migrations` when using `django.contrib.gis`.

---

## 🚀 Getting Started

### Option 1: Docker Deployment (Recommended)

The easiest way to get started is using Docker:

#### Prerequisites
- Docker (version 20.10 or higher)
- Docker Compose (version 2.0 or higher)

#### Quick Start
```bash
# Clone the repository
git clone https://github.com/your-org/vacs_insight.git
cd vacs_insight

# Setup environment
cp env.example .env
# Edit .env with your configuration

# Start the application
./docker-manage.sh start

# Create superuser
./docker-manage.sh createsuperuser
```

The application will be available at `http://localhost`

For detailed Docker instructions, see [DOCKER_README.md](DOCKER_README.md)

### Option 2: Local Development Setup

#### 1. Clone the Repository

```bash
git clone https://github.com/your-org/vacs_insight.git
cd vacs_insight
```

#### 2. Backend Setup (Django)

##### Create and activate a virtual environment

```bash
python -m venv env
source env/bin/activate
```

##### Install dependencies

```bash
pip install -r requirements.txt
```

##### Set up environment variables

Create a `.env` file:

```
DEBUG=True
SECRET_KEY=your_secret_key
DATABASE_URL=postgres://username:password@localhost:5432/vacs_insight
```

##### Apply database migrations

```bash
python manage.py migrate
```

##### Create a superuser

```bash
python manage.py createsuperuser
```

##### Start the development server

```bash
python manage.py runserver
```

---

### 3. Celery & Redis Setup

Ensure Redis is running. Then, in separate terminals:

```bash
# Start Celery worker
celery -A healthscope worker --loglevel=info

# Start Celery beat scheduler
celery -A healthscope beat --loglevel=info
```

---

### 4. Frontend Setup (React)

```bash
cd frontend
npm install
npm start
```

---

## 📁 Project Structure

```
vacs_insight/
├── api/               # DRF API views and serializers
├── core/              # Location and hierarchy models
├── ingestion/         # File/API ingestion pipelines
├── users/             # Custom user model and RBAC
├── gis/               # Facility model and mapping utilities
├── docker/            # Docker configuration files
│   ├── Dockerfile
│   ├── docker-compose.yml
│   ├── docker-compose.override.yml
│   ├── docker-manage.sh
│   ├── DOCKER_README.md
│   ├── nginx/
│   └── init.sql
├── celery.py          # Celery configuration
└── settings.py        # Django settings
```

---

## 📌 Development Notes

* Admin hierarchy: Province → District → Palika → Ward
* Custom user model with assigned administrative unit
* Geo-tagged facilities using `PointField` (PostGIS)
* Uploads tracked for lineage and validation
* CSV imports and API-based sync handled via Celery tasks

---

## 🧭 Future Roadmap

* Support for additional programs (nutrition, maternal, child health)
* Integration with live DHIS2 APIs
* Real-time dropout detection and alerting
* GIS heatmaps for zero-dose tracking
* Apache Superset or Metabase integration
* Airflow-based data pipeline orchestration

---

## 🤝 Contributing

Development is currently internal. For contribution requests or partnership, please email:

📧 `<EMAIL>`

---

## 🛡 License

This project is licensed under the [MIT License](LICENSE).

```
MIT License

Copyright (c) 2025 Trigonal Technology

Permission is hereby granted, free of charge, to any person obtaining a copy
of this software and associated documentation files (the "Software"), to deal
in the Software without restriction, including without limitation the rights
to use, copy, modify, merge, publish, distribute, sublicense, and/or sell
copies of the Software, and to permit persons to whom the Software is
furnished to do so, subject to the following conditions:

The above copyright notice and this permission notice shall be included in all
copies or substantial portions of the Software.

THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM,
OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE
SOFTWARE.
```

---

## 🇳🇵 Developed for Nepal's Health System

This project is intended to strengthen digital health infrastructure in Nepal and is aligned with the Ministry of Health and Population's efforts to build interoperable, open-source public health platforms.

---
