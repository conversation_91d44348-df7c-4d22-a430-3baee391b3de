#!/bin/bash

# Create virtual environment
python3 -m venv venv
source venv/bin/activate

# Install dependencies
pip install -r requirements.txt

# Run migrations
python manage.py migrate location

# Create Django project (only if not already created)
# django-admin startproject healthscope .

# Create apps (only if not already created)
# python manage.py startapp core
# python manage.py startapp facilities
# python manage.py startapp users

# Create necessary directories
mkdir -p media/uploads
mkdir -p static
mkdir -p templates

# Set permissions
chmod +x manage.py 